package com.hb.crm.core.beans;

import com.hb.crm.core.Enums.ImageCategory;
import com.hb.crm.core.Enums.MediaType;
import com.hb.crm.core.beans.LiveStream.LiveStream;

import lombok.Getter;
import lombok.Setter;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.mongodb.core.mapping.DBRef;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Setter
@Getter
public class Media {
    @Id
    private String id;
    private String title;

    @CreatedDate
    private Date creationDate;
    @LastModifiedDate
    private Date lastUpdate;

    private String source;
    private String description;
    private String videoUrl;
    private String trimmedVideoUrl;
    private ImageCategory imageCategory;
    private BigDecimal videoDuration;
    private BigDecimal videoDurationMS;
    private String thumbnailClipUrl;
    private String thumbnailCaptureUrl;
    private MediaType mediaType;
    private String OwnerId;
    private Double videoSize;
    private String LastUpdaterId;
    private Boolean employee;
    private Boolean privateMedia = false;
    private int numberOfReactions;
    private int numberOfComments;
    private int numberOfShares;
    private String clipStartTimecode;
    private String clipEndTimecode;
    private Double startTime;
    private Double endTime;
    private float latitude;
    private float longtuid;
    private List<StoryOverlay> overlays;
    @DBRef(lazy = true)
    private List<User> taggedUsers;
    @DBRef(lazy = true)
    private  Package _package;
    private Boolean showPackage=true;
    private Boolean showPost=true;
    @DBRef(lazy = true)
    private  Post post;
    @DBRef(lazy = true)
    private  User user;
    @DBRef(lazy = true)
    private List<Tag> tags;
    @DBRef(lazy = true)
    private  LiveStream liveStream;
    public Post getPost() {
        if(showPost){
            return post;

        }else {
            return null;
        }
    }

    public Package get_package() {
        if(showPackage){
            return _package;
        }else
            return null;
    }

    public Media() {
    }
    public LiveStream getLiveStream() {
        if(liveStream != null){
            return liveStream;
        }else{
            return null;
        }
    }
    
}
