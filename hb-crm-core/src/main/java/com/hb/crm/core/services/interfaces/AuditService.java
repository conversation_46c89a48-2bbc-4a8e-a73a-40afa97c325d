package com.hb.crm.core.services.interfaces;

import com.hb.crm.core.Enums.AuditAction;
import com.hb.crm.core.Enums.AuditEntityType;
import com.hb.crm.core.Enums.AuditStatus;
import com.hb.crm.core.beans.AopAudit.AopAuditLog;
import com.hb.crm.core.dtos.PageDto;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * Service interface for audit trail operations
 */
public interface AuditService {

    /**
     * Save an audit log entry
     */
    AopAuditLog saveAuditLog(AopAuditLog auditLog);

    /**
     * Create and save an audit log entry
     */
    AopAuditLog createAuditLog(AuditAction action, AuditEntityType entityType, String entityId, String userId);

    /**
     * Create and save an audit log entry with status
     */
    AopAuditLog createAuditLog(AuditAction action, AuditEntityType entityType, String entityId, String userId, AuditStatus status);

    /**
     * Get audit log by ID
     */
    Optional<AopAuditLog> getAuditLogById(String id);

    /**
     * Get audit logs for a specific user
     */
    Page<AopAuditLog> getAuditLogsByUser(String userId, Pageable pageable);

    /**
     * Get audit logs for a specific employee
     */
    Page<AopAuditLog> getAuditLogsByEmployee(String employeeId, Pageable pageable);

    /**
     * Get audit logs for a specific entity
     */
    Page<AopAuditLog> getAuditLogsByEntity(AuditEntityType entityType, String entityId, Pageable pageable);

    /**
     * Get audit logs by action type
     */
    Page<AopAuditLog> getAuditLogsByAction(AuditAction action, Pageable pageable);

    /**
     * Get audit logs by status
     */
    Page<AopAuditLog> getAuditLogsByStatus(AuditStatus status, Pageable pageable);

    /**
     * Get audit logs by date range
     */
    Page<AopAuditLog> getAuditLogsByDateRange(LocalDateTime startDate, LocalDateTime endDate, Pageable pageable);

    /**
     * Get audit logs by entity type
     */
    Page<AopAuditLog> getAuditLogsByEntityType(AuditEntityType entityType, Pageable pageable);

    /**
     * Get audit logs by module
     */
    Page<AopAuditLog> getAuditLogsByModule(String moduleName, Pageable pageable);

    /**
     * Get audit logs by IP address
     */
    Page<AopAuditLog> getAuditLogsByIpAddress(String ipAddress, Pageable pageable);

    /**
     * Get audit logs by correlation ID
     */
    List<AopAuditLog> getAuditLogsByCorrelationId(String correlationId);

    /**
     * Get audit logs by transaction ID
     */
    List<AopAuditLog> getAuditLogsByTransactionId(String transactionId);

    /**
     * Get failed audit logs
     */
    Page<AopAuditLog> getFailedAuditLogs(Pageable pageable);

    /**
     * Get security-relevant audit logs
     */
    Page<AopAuditLog> getSecurityAuditLogs(Pageable pageable);

    /**
     * Get compliance audit logs for date range
     */
    Page<AopAuditLog> getComplianceAuditLogs(LocalDateTime startDate, LocalDateTime endDate, Pageable pageable);

    /**
     * Search audit logs with complex filters
     */
    Page<AopAuditLog> searchAuditLogs(
            String userSearch,
            List<AuditEntityType> entityTypes,
            List<AuditAction> actions,
            List<AuditStatus> statuses,
            LocalDateTime startDate,
            LocalDateTime endDate,
            Boolean archived,
            Pageable pageable);

    /**
     * Search audit logs by text
     */
    Page<AopAuditLog> searchAuditLogsByText(String searchText, Pageable pageable);

    /**
     * Get audit logs with errors in date range
     */
    Page<AopAuditLog> getErrorAuditLogs(LocalDateTime startDate, LocalDateTime endDate, Pageable pageable);

    /**
     * Get recent audit logs for an entity
     */
    List<AopAuditLog> getRecentAuditLogsForEntity(AuditEntityType entityType, String entityId);

    /**
     * Check if entity has audit logs
     */
    boolean hasAuditLogs(AuditEntityType entityType, String entityId);

    /**
     * Get latest audit log for an entity
     */
    Optional<AopAuditLog> getLatestAuditLogForEntity(AuditEntityType entityType, String entityId);

    /**
     * Count audit logs by user in date range
     */
    long countAuditLogsByUser(String userId, LocalDateTime startDate, LocalDateTime endDate);

    /**
     * Count failed operations by user in date range
     */
    long countFailedOperationsByUser(String userId, LocalDateTime startDate, LocalDateTime endDate);

    /**
     * Archive old audit logs
     */
    int archiveOldAuditLogs(LocalDateTime cutoffDate);

    /**
     * Delete archived audit logs older than specified date
     */
    int deleteArchivedAuditLogs(LocalDateTime cutoffDate);

    /**
     * Get audit statistics for dashboard
     */
    Map<String, Object> getAuditStatistics(LocalDateTime startDate, LocalDateTime endDate);

    /**
     * Get audit logs by session ID
     */
    List<AopAuditLog> getAuditLogsBySessionId(String sessionId);

    /**
     * Get audit logs by request ID
     */
    List<AopAuditLog> getAuditLogsByRequestId(String requestId);

    /**
     * Export audit logs to CSV
     */
    byte[] exportAuditLogsToCSV(
            LocalDateTime startDate,
            LocalDateTime endDate,
            List<AuditEntityType> entityTypes,
            List<AuditAction> actions);

    /**
     * Export audit logs to JSON
     */
    byte[] exportAuditLogsToJSON(
            LocalDateTime startDate,
            LocalDateTime endDate,
            List<AuditEntityType> entityTypes,
            List<AuditAction> actions);

    /**
     * Get audit trail for a specific entity (all related audit logs)
     */
    PageDto<AopAuditLog> getEntityAuditTrail(AuditEntityType entityType, String entityId, int page, int size);

    /**
     * Get user activity summary
     */
    Map<String, Object> getUserActivitySummary(String userId, LocalDateTime startDate, LocalDateTime endDate);

    /**
     * Get system activity summary
     */
    Map<String, Object> getSystemActivitySummary(LocalDateTime startDate, LocalDateTime endDate);

    /**
     * Approve audit log entry
     */
    AopAuditLog approveAuditLog(String auditLogId, String approvedBy);

    /**
     * Reject audit log entry
     */
    AopAuditLog rejectAuditLog(String auditLogId, String rejectedBy, String reason);

    /**
     * Get audit logs requiring approval
     */
    Page<AopAuditLog> getAuditLogsRequiringApproval(Pageable pageable);

    /**
     * Bulk archive audit logs
     */
    int bulkArchiveAuditLogs(List<String> auditLogIds);

    /**
     * Bulk delete audit logs
     */
    int bulkDeleteAuditLogs(List<String> auditLogIds);

    /**
     * Get audit logs by risk level
     */
    Page<AopAuditLog> getAuditLogsByRiskLevel(String riskLevel, Pageable pageable);

    /**
     * Get audit logs by tags
     */
    Page<AopAuditLog> getAuditLogsByTag(String tag, Pageable pageable);

    /**
     * Add tags to audit log
     */
    AopAuditLog addTagsToAuditLog(String auditLogId, List<String> tags);

    /**
     * Remove tags from audit log
     */
    AopAuditLog removeTagsFromAuditLog(String auditLogId, List<String> tags);

    /**
     * Get audit logs with sensitive data
     */
    Page<AopAuditLog> getAuditLogsWithSensitiveData(Pageable pageable);

    /**
     * Anonymize audit logs for a user
     */
    int anonymizeUserAuditLogs(String userId);

    /**
     * Get audit logs by external reference ID
     */
    Optional<AopAuditLog> getAuditLogByExternalReference(String externalReferenceId);

    /**
     * Create audit log for manual entry
     */
    AopAuditLog createManualAuditLog(
            AuditAction action,
            AuditEntityType entityType,
            String entityId,
            String description,
            String createdBy);

    /**
     * Get audit logs by environment
     */
    Page<AopAuditLog> getAuditLogsByEnvironment(String environment, Pageable pageable);

    /**
     * Validate audit log integrity
     */
    boolean validateAuditLogIntegrity(String auditLogId);

    /**
     * Get audit logs for compliance report
     */
    List<AopAuditLog> getComplianceReport(
            LocalDateTime startDate,
            LocalDateTime endDate,
            List<String> complianceFlags);
}
