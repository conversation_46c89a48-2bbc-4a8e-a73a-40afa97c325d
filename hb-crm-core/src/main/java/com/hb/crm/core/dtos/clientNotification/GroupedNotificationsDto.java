package com.hb.crm.core.dtos.clientNotification;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class GroupedNotificationsDto {
    private List<NotificationDto> today;
    private List<NotificationDto> yesterday;
    private List<NotificationDto> older;
    private long totalCount;
    private int currentPage;
    private int pageSize;
}