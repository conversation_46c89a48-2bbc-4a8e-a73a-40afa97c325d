package com.hb.crm.core.Audit;

import com.hb.crm.core.repositories.AuditLogRepository;
import lombok.Getter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class AuditLogContext {

    @Getter
    private static AuditLogRepository auditLogRepository;

    @Autowired
    public AuditLogContext(AuditLogRepository auditLogRepository) {
        AuditLogContext.auditLogRepository = auditLogRepository;
    }

}
