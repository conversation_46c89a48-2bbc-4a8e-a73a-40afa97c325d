package com.hb.crm.core.Enums;

/**
 * Enum representing different entity types that can be audited
 */
public enum AuditEntityType {
    USER("User", "User entity"),
    EMPLOYEE("Employee", "Employee entity"),
    POST("Post", "Post entity"),
    PACKAGE("Package", "Package entity"),
    SUB_PACKAGE("SubPackage", "Sub Package entity"),
    COMMENT("Comment", "Comment entity"),
    MEDIA("Media", "Media entity"),
    REACTION("Reaction", "Reaction entity"),
    NOTIFICATION("Notification", "Notification entity"),
    PAYMENT("Payment", "Payment entity"),
    SUBSCRIPTION("Subscription", "Subscription entity"),
    FOLLOW("Follow", "Follow relationship"),
    LIKE("Like", "Like entity"),
    BOOKMARK("Bookmark", "Bookmark entity"),
    REPORT("Report", "Report entity"),
    TAG("Tag", "Tag entity"),
    CATEGORY("Category", "Category entity"),
    MOOD("Mood", "Mood entity"),
    ACTIVITY("Activity", "Activity entity"),
    HOTEL("Hotel", "Hotel entity"),
    FLIGHT("Flight", "Flight entity"),
    COUNTRY("Country", "Country entity"),
    CITY("City", "City entity"),
    AREA("Area", "Area entity"),
    AIRPORT("Airport", "Airport entity"),
    ROLE("Role", "Role entity"),
    PERMISSION("Permission", "Permission entity"),
    SETTING("Setting", "Setting entity"),
    CONFIGURATION("Configuration", "Configuration entity"),
    LIVE_STREAM("LiveStream", "Live Stream entity"),
    CHAT_MESSAGE("ChatMessage", "Chat Message entity"),
    CONVERSATION("Conversation", "Conversation entity"),
    GROUP_CHAT("GroupChat", "Group Chat entity"),
    POLL("Poll", "Poll entity"),
    VOTE("Vote", "Vote entity"),
    STORY("Story", "Story entity"),
    HIGHLIGHT("Highlight", "Highlight entity"),
    PLACE("Place", "Place entity"),
    RATE("Rate", "Rate entity"),
    REVIEW("Review", "Review entity"),
    AMENITY("Amenity", "Amenity entity"),
    PROVIDER("Provider", "Provider entity"),
    TRANSACTION("Transaction", "Transaction entity"),
    REFUND("Refund", "Refund entity"),
    INVOICE("Invoice", "Invoice entity"),
    RECEIPT("Receipt", "Receipt entity"),
    COUPON("Coupon", "Coupon entity"),
    DISCOUNT("Discount", "Discount entity"),
    PROMOTION("Promotion", "Promotion entity"),
    CAMPAIGN("Campaign", "Campaign entity"),
    ANALYTICS("Analytics", "Analytics entity"),
    LOG("Log", "Log entity"),
    AUDIT("Audit", "Audit entity"),
    BACKUP("Backup", "Backup entity"),
    IMPORT("Import", "Import entity"),
    EXPORT("Export", "Export entity"),
    TEMPLATE("Template", "Template entity"),
    WORKFLOW("Workflow", "Workflow entity"),
    TASK("Task", "Task entity"),
    EVENT("Event", "Event entity"),
    CALENDAR("Calendar", "Calendar entity"),
    REMINDER("Reminder", "Reminder entity"),
    ALERT("Alert", "Alert entity"),
    DASHBOARD("Dashboard", "Dashboard entity"),
    WIDGET("Widget", "Widget entity"),
    CHART("Chart", "Chart entity"),
    REPORT_TEMPLATE("ReportTemplate", "Report Template entity"),
    EMAIL_TEMPLATE("EmailTemplate", "Email Template entity"),
    SMS_TEMPLATE("SmsTemplate", "SMS Template entity"),
    NOTIFICATION_TEMPLATE("NotificationTemplate", "Notification Template entity"),
    CUSTOM("Custom", "Custom entity type");

    private final String entityName;
    private final String description;

    AuditEntityType(String entityName, String description) {
        this.entityName = entityName;
        this.description = description;
    }

    public String getEntityName() {
        return entityName;
    }

    public String getDescription() {
        return description;
    }

    @Override
    public String toString() {
        return entityName;
    }

    /**
     * Get AuditEntityType from class name
     */
    public static AuditEntityType fromClassName(String className) {
        if (className == null) return CUSTOM;
        
        String simpleName = className.substring(className.lastIndexOf('.') + 1);
        
        for (AuditEntityType type : values()) {
            if (type.getEntityName().equalsIgnoreCase(simpleName)) {
                return type;
            }
        }
        
        return CUSTOM;
    }
}
