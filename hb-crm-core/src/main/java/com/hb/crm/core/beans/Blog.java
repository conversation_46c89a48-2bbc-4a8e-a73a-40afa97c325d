package com.hb.crm.core.beans;

import com.hb.crm.core.Enums.BlogStatus;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.mongodb.core.index.CompoundIndex;
import org.springframework.data.mongodb.core.index.CompoundIndexes;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.index.TextIndexed;
import org.springframework.data.mongodb.core.mapping.DBRef;
import org.springframework.data.mongodb.core.mapping.Document;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Blog Entity - Represents a blog post in the system
 *
 * This entity is designed to handle large volumes of blog data efficiently with:
 * - Compound indexes for optimized queries on status and dates
 * - Text indexes for full-text search capabilities
 * - Lazy loading for relationships to improve performance
 * - Built-in analytics fields for tracking engagement
 * - Soft delete functionality for data retention
 *
 * Database Optimization Features:
 * - status_published_idx: Optimizes queries for published blogs by date
 * - author_status_idx: Optimizes author-specific blog queries
 * - Text indexes on title (weight=3), content (weight=2) for search relevance
 *
 * <AUTHOR> CRM Team
 * @version 1.0
 * @since 2024-01-01
 */

@Data
@NoArgsConstructor
@Document(collection = "blog")
@CompoundIndexes({
    @CompoundIndex(name = "status_published_idx", def = "{'status': 1, 'publishedDate': -1}"),
    @CompoundIndex(name = "author_status_idx", def = "{'author': 1, 'status': 1, 'publishedDate': -1}")
})
public class Blog {
    /**
     * Unique identifier for the blog post
     */
    @Id
    private String id;

    /**
     * Blog title - indexed for search with the highest weight (3) for relevance
     * Also has a regular index for sorting and filtering
     */
    @TextIndexed(weight = 3)
    @Indexed
    private String title;

    /**
     * URL-friendly slug for SEO optimization
     * Must be unique across all blogs
     */
    @Indexed(unique = true)
    private String slug;

    /**
     * Main blog content - indexed for search with medium weight (2)
     * Supports rich text and HTML content
     */
    @TextIndexed(weight = 2)
    private String content;


    /**
     * Blog author - lazy loaded to improve performance
     * References the User entity
     */
    @DBRef(lazy = true)
    private Employee author;

    /**
     * Associated tags - lazy loaded for performance
     * Used for categorization and filtering
     */
    @DBRef(lazy = true)
    private List<Tag> tags;

    
    /**
     * Media attachments (images, videos, documents)
     * Supports multiple media files per blog
     */
    private List<MediaWrapper> media;

    /**
     * Current status of the blog - indexed for efficient filtering
     * Values: draft, published, archived, deleted
     */
    @Indexed
    private BlogStatus status = BlogStatus.draft;

    /**
     * Timestamp when the blog was created
     */
    @CreatedDate
    private LocalDateTime createdDate;

    /**
     * Timestamp when the blog was last updated
     */
    @LastModifiedDate
    private LocalDateTime updatedDate;

    /**
     * Timestamp when the blog was published - indexed for sorting
     * Used in compound indexes for efficient published blog queries
     */
    @Indexed
    private LocalDateTime publishedDate;

    // Analytics fields - updated in real-time for performance tracking

    /**
     * Total number of views - updated via atomic operations
     */
    private int viewCount = 0;

    /**
     * Total number of comments - updated when comments are added/removed
     */
    private int commentCount = 0;

    /**
     * Total number of likes/reactions - updated when reactions change
     */
    private int likeCount = 0;

    /**
     * Total number of shares - updated when blog is shared
     */
    private int shareCount = 0;


    // Content interaction settings

    /**
     * Whether comments are allowed on this blog
     */
    private boolean allowComments = true;

    /**
     * Whether reactions (likes, etc.) are allowed on this blog
     */
    private boolean allowReactions = true;

    // Soft delete functionality for data retention

    /**
     * Timestamp when the blog was soft deleted
     */
    private LocalDateTime deletedDate;

    /**
     * Soft delete flag - allows data recovery and audit trails
     */
    private boolean deleted = false;
    
    /**
     * Constructor for creating a new blog with basic information
     *
     * @param title The blog title
     * @param content The blog content
     * @param author The blog author
     */
    public Blog(String title, String content, Employee author) {
        this.title = title;
        this.content = content;
        this.author = author;
        this.createdDate = LocalDateTime.now();
        this.updatedDate = LocalDateTime.now();
    }
}
