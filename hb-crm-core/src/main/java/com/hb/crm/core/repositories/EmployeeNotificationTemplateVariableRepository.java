package com.hb.crm.core.repositories;

import com.hb.crm.core.beans.Notification.EmployeeNotificationTemplateVariable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface EmployeeNotificationTemplateVariableRepository extends MongoRepository<EmployeeNotificationTemplateVariable, String> {
    Optional<EmployeeNotificationTemplateVariable> findByNameAndEntity(String name, String entity);
}
