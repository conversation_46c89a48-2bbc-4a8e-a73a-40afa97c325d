package com.hb.crm.core.beans.Notification;

import lombok.Getter;
import lombok.Setter;
import org.springframework.data.mongodb.core.index.TextIndexed;

import java.util.List;

@Getter
@Setter
public class EmployeeNotificationTemplatePush {
    @TextIndexed
    private String subject;
    @TextIndexed
    private String body;
    private String icon;
    private boolean status = false;
    private List<EmployeeNotificationTemplateVariable> templateVariables;
}
