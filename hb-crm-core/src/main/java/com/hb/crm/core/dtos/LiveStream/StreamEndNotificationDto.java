package com.hb.crm.core.dtos.LiveStream;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class StreamEndNotificationDto {
    private String liveStreamId;
    private String influencerId;
    private String influencerName;
    private String reason; // "STOPPED_BY_INFLUENCER", "SYSTEM_ERROR", "TIMEOUT", etc.
    private LocalDateTime endedAt;
    private String message; // User-friendly message
    private String action; // "STREAM_ENDED"
} 