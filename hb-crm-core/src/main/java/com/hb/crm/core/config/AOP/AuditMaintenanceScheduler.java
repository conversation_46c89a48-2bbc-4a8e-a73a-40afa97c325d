package com.hb.crm.core.config.AOP;

import com.hb.crm.core.services.interfaces.AuditService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
 * Scheduled tasks for audit trail maintenance
 * Handles archiving and cleanup of old audit logs
 */
@Component
public class AuditMaintenanceScheduler {

    private static final Logger logger = LoggerFactory.getLogger(AuditMaintenanceScheduler.class);

    @Autowired
    private AuditService auditService;

    @Value("${audit.maintenance.enabled:true}")
    private boolean maintenanceEnabled;

    @Value("${audit.retention.days:365}")
    private int retentionDays;

    @Value("${audit.archive.retention.days:2555}") // 7 years
    private int archiveRetentionDays;

    /**
     * Archive old audit logs daily at 2 AM
     */
//    @Scheduled(cron = "0 0 2 * * ?")
    public void archiveOldAuditLogs() {
        if (!maintenanceEnabled) {
            return;
        }

        try {
            LocalDateTime cutoffDate = LocalDateTime.now().minusDays(retentionDays);
            int archivedCount = auditService.archiveOldAuditLogs(cutoffDate);
            
            if (archivedCount > 0) {
                logger.info("Archived {} audit logs older than {}", archivedCount, cutoffDate);
            }
            
        } catch (Exception e) {
            logger.error("Failed to archive old audit logs", e);
        }
    }

    /**
     * Delete very old archived audit logs weekly on Sunday at 3 AM
     */
//    @Scheduled(cron = "0 0 3 * * SUN")
    public void deleteOldArchivedAuditLogs() {
        if (!maintenanceEnabled) {
            return;
        }

        try {
            LocalDateTime cutoffDate = LocalDateTime.now().minusDays(archiveRetentionDays);
            int deletedCount = auditService.deleteArchivedAuditLogs(cutoffDate);
            
            if (deletedCount > 0) {
                logger.info("Deleted {} archived audit logs older than {}", deletedCount, cutoffDate);
            }
            
        } catch (Exception e) {
            logger.error("Failed to delete old archived audit logs", e);
        }
    }

    /**
     * Generate audit statistics report weekly on Monday at 8 AM
     */
//    @Scheduled(cron = "0 0 8 * * MON")
    public void generateWeeklyAuditReport() {
        if (!maintenanceEnabled) {
            return;
        }

        try {
            LocalDateTime endDate = LocalDateTime.now();
            LocalDateTime startDate = endDate.minusDays(7);
            
            var statistics = auditService.getAuditStatistics(startDate, endDate);
            
            logger.info("Weekly Audit Report ({}  to {}): {}", 
                startDate.toLocalDate(), endDate.toLocalDate(), statistics);
            
        } catch (Exception e) {
            logger.error("Failed to generate weekly audit report", e);
        }
    }

    /**
     * Health check for audit system every hour
     */
//    @Scheduled(fixedRate = 3600000) // 1 hour
    public void auditSystemHealthCheck() {
        if (!maintenanceEnabled) {
            return;
        }

        try {
            // Check if audit logs are being created
            LocalDateTime oneHourAgo = LocalDateTime.now().minusHours(1);
            LocalDateTime now = LocalDateTime.now();
            
            long recentAuditCount = auditService.getAuditLogsByDateRange(
                oneHourAgo, now, 
                org.springframework.data.domain.PageRequest.of(0, 1)
            ).getTotalElements();
            
            if (recentAuditCount == 0) {
                logger.warn("No audit logs created in the last hour - audit system may not be functioning properly");
            }
            
            // Check for failed audit operations
            var failedLogs = auditService.getFailedAuditLogs(
                org.springframework.data.domain.PageRequest.of(0, 10)
            );
            
            if (failedLogs.getTotalElements() > 0) {
                logger.warn("Found {} failed audit operations in recent logs", failedLogs.getTotalElements());
            }
            
        } catch (Exception e) {
            logger.error("Audit system health check failed", e);
        }
    }

    /**
     * Cleanup orphaned audit logs monthly on the 1st at 4 AM
     */
//    @Scheduled(cron = "0 0 4 1 * ?")
    public void cleanupOrphanedAuditLogs() {
        if (!maintenanceEnabled) {
            return;
        }

        try {
            // This is a placeholder for cleanup logic
            // In a real implementation, you might check for audit logs
            // referencing entities that no longer exist
            
            logger.info("Orphaned audit logs cleanup completed");
            
        } catch (Exception e) {
            logger.error("Failed to cleanup orphaned audit logs", e);
        }
    }

    /**
     * Validate audit log integrity monthly on the 15th at 5 AM
     */
//    @Scheduled(cron = "0 0 5 15 * ?")
    public void validateAuditLogIntegrity() {
        if (!maintenanceEnabled) {
            return;
        }

        try {
            // Sample validation of recent audit logs
            LocalDateTime oneMonthAgo = LocalDateTime.now().minusMonths(1);
            LocalDateTime now = LocalDateTime.now();
            
            var recentLogs = auditService.getAuditLogsByDateRange(
                oneMonthAgo, now,
                org.springframework.data.domain.PageRequest.of(0, 100)
            );
            
            int validCount = 0;
            int invalidCount = 0;
            
            for (var log : recentLogs.getContent()) {
                if (auditService.validateAuditLogIntegrity(log.getId())) {
                    validCount++;
                } else {
                    invalidCount++;
                    logger.warn("Invalid audit log found: {}", log.getId());
                }
            }
            
            logger.info("Audit log integrity check completed: {} valid, {} invalid", validCount, invalidCount);
            
        } catch (Exception e) {
            logger.error("Failed to validate audit log integrity", e);
        }
    }

    /**
     * Performance monitoring for audit system every 6 hours
     */
//    @Scheduled(fixedRate = 21600000) // 6 hours
    public void monitorAuditPerformance() {
        if (!maintenanceEnabled) {
            return;
        }

        try {
            LocalDateTime sixHoursAgo = LocalDateTime.now().minusHours(6);
            LocalDateTime now = LocalDateTime.now();
            
            // Check for slow audit operations (duration > 5 seconds)
            var auditLogs = auditService.getAuditLogsByDateRange(
                sixHoursAgo, now,
                org.springframework.data.domain.PageRequest.of(0, 1000)
            );
            
            long slowOperations = auditLogs.getContent().stream()
                .filter(log -> log.getDuration() != null && log.getDuration() > 5000)
                .count();
            
            if (slowOperations > 0) {
                logger.warn("Found {} slow audit operations (>5s) in the last 6 hours", slowOperations);
            }
            
            // Check audit log storage growth
            long totalAuditLogs = auditService.getAuditLogsByDateRange(
                LocalDateTime.now().minusYears(1), now,
                org.springframework.data.domain.PageRequest.of(0, 1)
            ).getTotalElements();
            
            logger.debug("Total audit logs in system: {}", totalAuditLogs);
            
        } catch (Exception e) {
            logger.error("Failed to monitor audit performance", e);
        }
    }
}
