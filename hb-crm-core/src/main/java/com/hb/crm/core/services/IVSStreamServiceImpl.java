package com.hb.crm.core.services;

import com.amazonaws.auth.AWSStaticCredentialsProvider;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.services.ivs.AmazonIVS;
import com.amazonaws.services.ivs.AmazonIVSClientBuilder;
import com.amazonaws.services.ivs.model.*;
import com.hb.crm.core.Enums.NotificationEntityType;
import com.hb.crm.core.Enums.NotificationType;
import com.hb.crm.core.Enums.UserType;
import com.hb.crm.core.beans.User;
import com.hb.crm.core.dtos.LiveStream.LiveStreamStartResponseDto;
import com.hb.crm.core.services.interfaces.IVSStreamService;
import com.hb.crm.core.beans.LiveStream.LiveStream;
import com.hb.crm.core.beans.LiveStream.LiveStreamReaction;
import com.hb.crm.core.repositories.LiveStream.LiveStreamReactionRepository;
import com.hb.crm.core.repositories.LiveStream.LiveStreamRepository;
import com.hb.crm.core.repositories.MediaReactionRepository;
import com.hb.crm.core.repositories.MediaRepository;
import com.hb.crm.core.beans.Media;
import com.hb.crm.core.Enums.Mp4ConversionStatus;
import com.hb.crm.core.Enums.MediaType;
import com.hb.crm.core.services.interfaces.NotificationService;
import org.springframework.scheduling.annotation.Async;


import lombok.RequiredArgsConstructor;
import software.amazon.awssdk.services.s3.model.GetObjectRequest;
import software.amazon.awssdk.services.s3.presigner.S3Presigner;
import software.amazon.awssdk.services.s3.presigner.model.GetObjectPresignRequest;

import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import java.net.URL;
import java.time.Duration;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.io.*;
import java.nio.file.*;
import java.util.regex.Pattern;
import java.util.regex.Matcher;
import java.util.Set;
import java.util.HashSet;
import java.io.ByteArrayInputStream;
import java.math.BigDecimal;

import com.amazonaws.services.s3.model.PutObjectRequest;
import com.amazonaws.services.s3.model.ObjectMetadata;

import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3ClientBuilder;
import com.amazonaws.services.s3.model.ListObjectsV2Request;
import com.amazonaws.services.s3.model.ListObjectsV2Result;
import com.amazonaws.services.s3.model.S3ObjectSummary;

@Service
@RequiredArgsConstructor
public class IVSStreamServiceImpl implements IVSStreamService 
{

        //private final LiveStreamConfigurationService configurationService;

        @Value("${cloud.aws.credentials.accessKey}")
        private String awsAccessKey;
        
        @Value("${cloud.aws.credentials.secretKey}")
        private String awsSecretKey;
        
        @Value("${cloud.aws.region.static}")
        private String awsRegion;
        @Value("${cloud.aws.ivs.recordingConfigurationArn}")
        private String recordingConfigurationArn;
        @Value("${cloud.aws.bucketName}")
        private String recordingsBucketName;
        @Value("${cloud.aws.ivs.ChannelType}")
        private String channelType;
        @Value("${cloud.aws.ivs.LatencyMode}")
        private String latencyMode;
        @Value("${cloud.aws.ivs.AwsAccountId}")
        private String awsAccountId ;
        private final S3Presigner s3Presigner;
        private final LiveStreamRepository liveStreamRepository;
        private final LiveStreamReactionRepository liveStreamReactionRepository;
        private final MediaRepository mediaRepository;
        private final NotificationService notificationService;
        private final MediaReactionRepository mediaReactionRepository;
        final static boolean test = false;
        @Override
        public LiveStreamStartResponseDto createChannelForInfluencer(String influencerId) 
        {
                if (test) 
                {
                        LiveStreamStartResponseDto mock = new LiveStreamStartResponseDto();
                        mock.setChannelArn("arn:aws:ivs:us-east-1:************:channel/mockChannel");
                        mock.setPlaybackUrl("https://mock.playback.aws.com/influencer_" + influencerId);
                        mock.setStreamKey("mock-stream-key-123");
                        mock.setIngestEndpoint("rtmps://mock.ingest.aws.com/app/");
                        return mock;
                }

                // Step 1: Create AWS credentials
                BasicAWSCredentials awsCreds = new BasicAWSCredentials(awsAccessKey, awsSecretKey);
                AmazonIVS ivs = AmazonIVSClientBuilder.standard()
                        .withCredentials(new AWSStaticCredentialsProvider(awsCreds))
                        .withRegion(awsRegion)
                        .build();

                // Step 2: Build CreateChannelRequest
                CreateChannelRequest channelRequest = new CreateChannelRequest()
                        .withName("influencer_" + influencerId+ "_" + System.currentTimeMillis())
                        .withType(channelType)
                        .withLatencyMode(latencyMode)
                        .withRecordingConfigurationArn(recordingConfigurationArn);
;
                // ListRecordingConfigurationsRequest request1 = new ListRecordingConfigurationsRequest();
                // ListRecordingConfigurationsResult result1 = ivs.listRecordingConfigurations(request1);

                // List<RecordingConfigurationSummary> configs1 = result1.getRecordingConfigurations();

                // for (RecordingConfigurationSummary config : configs1) {
                //         System.out.println("ARN: " + config.getArn());
                //         System.out.println("Name: " + config.getName());
                //         }
                // Step 3: Create the channel
                CreateChannelResult result = ivs.createChannel(channelRequest);
                Channel channel = result.getChannel();
                StreamKey streamKey = result.getStreamKey();

                // Step 4: Fetch full channel to get ingest endpoint
                Channel fullChannel = ivs.getChannel(new GetChannelRequest().withArn(channel.getArn())).getChannel();
                String ingestEndpoint = fullChannel.getIngestEndpoint(); // e.g., a1b2c3d4.global-contribute.live-video.net
                
                // Step 5: Build response DTO
                LiveStreamStartResponseDto response = new LiveStreamStartResponseDto();
                response.setChannelArn(channel.getArn());
                response.setPlaybackUrl(channel.getPlaybackUrl());
                response.setStreamKey(streamKey.getValue());
                response.setIngestEndpoint("rtmps://" + ingestEndpoint + "/app/");

                return response;
        }
        @Override
        public void stopStream(String channelArn) 
        {
                if (test) return;
                
                BasicAWSCredentials creds = new BasicAWSCredentials(awsAccessKey, awsSecretKey);
                AmazonIVS ivs = AmazonIVSClientBuilder.standard()
                        .withCredentials(new AWSStaticCredentialsProvider(creds))
                        .withRegion(awsRegion)
                        .build();

                try 
                {
                        // Try to stop stream first (ignore if it fails)
                        try 
                        {
                            ivs.stopStream(new StopStreamRequest().withChannelArn(channelArn));
                            Thread.sleep(3000); // Brief wait for stream to stop
                        } 
                        catch (Exception e) 
                        {
                            System.out.println("Stream stop failed or already offline: " + e.getMessage());
                        }
                        
                       
                        // Delete the channel
                        ivs.deleteChannel(new DeleteChannelRequest().withArn(channelArn));
                        System.out.println("Channel deleted successfully: " + channelArn);
                        
                } 
                catch (Exception e) 
                {
                        throw new RuntimeException("Failed to stop stream and delete channel: " + channelArn, e);
                }
        }
        
        /**
         * Extract channel ID from channel ARN
         */
        private String extractChannelIdFromArn(String channelArn) 
        {
                if (channelArn == null || !channelArn.contains("/")) 
                {
                        throw new IllegalArgumentException("Invalid channel ARN format: " + channelArn);
                }
                
                // ARN format: arn:aws:ivs:region:account:channel/channelId
                String[] parts = channelArn.split("/");
                return parts[parts.length - 1];
        }

        /**
         * Construct the S3 path where recordings are stored
         */
        private String constructRecordingPath(String channelId) 
        {
                // For recent recordings, you might want to search the last few days
                // This is more efficient than searching all recordings
                return "ivs/v1/" + awsAccountId + "/" + channelId + "/";
        }
        
        /**
         * Find HIGHEST QUALITY segments in CONTINUOUS sequence starting from 0
         * This fixes the quality mixing and repetition issues
         */
        private List<String> findAllSegmentsFromCompleteStream(AmazonS3 s3Client, List<S3ObjectSummary> objects) 
        {
            System.out.println("🎬 ANALYZING STREAM FOR HIGHEST QUALITY CONTINUOUS SEQUENCE:");
            
            // Step 1: HighLight files by quality level and find TS segments only
            Map<String, List<S3ObjectSummary>> qualityGroups = new HashMap<>();
            
            for (S3ObjectSummary obj : objects) {
                String key = obj.getKey();
                
                // Only process TS segments
                if (!key.endsWith(".ts")) continue;
                
                // Extract quality from path (720p, 1080p, etc.)
                String quality = extractQualityFromPath(key);
                qualityGroups.computeIfAbsent(quality, k -> new ArrayList<>()).add(obj);
            }
            
            System.out.println("   📊 Found quality levels: " + qualityGroups.keySet());
            
            // Step 2: Find the highest quality level with the most segments
            String bestQuality = null;
            int maxSegments = 0;
            
            for (Map.Entry<String, List<S3ObjectSummary>> entry : qualityGroups.entrySet()) {
                String quality = entry.getKey();
                int segmentCount = entry.getValue().size();
                int qualityValue = parseQualityValue(quality);
                
                System.out.println("   🎥 Quality " + quality + ": " + segmentCount + " segments (" + qualityValue + "p)");
                
                // Prefer higher quality, but also consider segment count for completeness
                if 
                (
                    bestQuality == null || 
                    (qualityValue > parseQualityValue(bestQuality)) ||
                    (qualityValue == parseQualityValue(bestQuality) && segmentCount > maxSegments)) 
                {
                    bestQuality = quality;
                    maxSegments = segmentCount;
                }
            }
            
            if (bestQuality == null) 
            {
                return new ArrayList<>();
            }
                        
            // Step 3: Get all segments for the best quality and sort them by segment number
            List<S3ObjectSummary> bestQualitySegments = qualityGroups.get(bestQuality);
            
            // Sort segments by segment number (0.ts, 1.ts, 2.ts, etc.)
            bestQualitySegments.sort((s1, s2) -> 
            {
                int num1 = extractSegmentNumber(s1.getKey());
                int num2 = extractSegmentNumber(s2.getKey());
                return Integer.compare(num1, num2);
            });
            
            // Step 4: Find continuous sequence starting from 0
            List<String> continuousSegments = new ArrayList<>();
            int expectedSegmentNumber = 0;
            
            for (S3ObjectSummary segment : bestQualitySegments) 
            {
                int segmentNumber = extractSegmentNumber(segment.getKey());
                
                if (segmentNumber == expectedSegmentNumber) 
                {
                    continuousSegments.add(segment.getKey());
                    expectedSegmentNumber++;
                } 
                else if (segmentNumber > expectedSegmentNumber) 
                {
                    // Gap found - stop here to avoid incomplete sequences
                    break;
                }
            }
            
            System.out.println("   ✅ CONTINUOUS SEQUENCE FOUND:");
            System.out.println("      🎥 Quality: " + bestQuality);
            System.out.println("      📦 Segments: " + continuousSegments.size() + " (from 0 to " + (continuousSegments.size() - 1) + ")");
            System.out.println("      📅 First: " + extractSegmentIdentifier(continuousSegments.get(0)));
            System.out.println("      📅 Last: " + extractSegmentIdentifier(continuousSegments.get(continuousSegments.size() - 1)));
            
            // Verify no duplicates
            Set<String> uniqueSegments = new HashSet<>(continuousSegments);
            if (uniqueSegments.size() != continuousSegments.size()) 
            {
                System.out.println("   ⚠️  WARNING: Found " + (continuousSegments.size() - uniqueSegments.size()) + " duplicate segments!");
            }
            
            return continuousSegments;
        }
        
        /**
         * Extract quality level from S3 object path
         */
        private String extractQualityFromPath(String key) 
        {
            // AWS IVS path typically: ivs/v1/account/channel/year/month/day/hour/minute/session/quality/segment.ts
            String[] parts = key.split("/");
            
            // Look for quality indicators in the path
            for (String part : parts) 
            {
                if (part.matches("\\d+p\\d+") || part.matches("\\d+p")) 
                {
                    return part;
                }
            }
            
            // Look for quality in filename
            String filename = parts[parts.length - 1];
            if (filename.contains("720p")) return "720p";
            if (filename.contains("1080p")) return "1080p";
            if (filename.contains("480p")) return "480p";
            if (filename.contains("360p")) return "360p";
            
            return "unknown";
        }
        
        /**
         * Parse quality value as integer for comparison
         */
        private int parseQualityValue(String quality) 
        {
            if (quality.contains("1080")) return 1080;
            if (quality.contains("720")) return 720;
            if (quality.contains("480")) return 480;
            if (quality.contains("360")) return 360;
            if (quality.contains("240")) return 240;
            if (quality.contains("160")) return 160;
            return 0; // Unknown quality
        }
        
        /**
         * Extract segment number from TS filename
         */
        private int extractSegmentNumber(String key)
        {
            try {
                String filename = key.substring(key.lastIndexOf('/') + 1);
                
                // Remove .ts extension
                String nameWithoutExt = filename.replace(".ts", "");
                
                // Look for number patterns
                Pattern pattern = Pattern.compile("(\\d+)");
                Matcher matcher = pattern.matcher(nameWithoutExt);
                
                if (matcher.find()) {
                    return Integer.parseInt(matcher.group(1));
                }
            } catch (Exception e) {
                System.out.println("Failed to extract segment number from: " + key);
            }
            
            return -1; // Invalid segment number
        }
        
        /**
         * Convert HIGHEST QUALITY continuous sequence to MP4, clip, and thumbnail
         * No duplication, no quality mixing, proper segment ordering
         * OPTIMIZED: Generates all assets from local files to avoid multiple S3 downloads
         */
        private Map<String, String> performCompleteStreamToMp4Conversion(AmazonS3 s3Client, List<String> continuousSegmentKeys, String channelId) 
        {
            Path tempDir = null;
            Map<String, String> assetKeys = new HashMap<>();
            
            try 
            {
                // Step 1: Create temporary directory for processing
                tempDir = Files.createTempDirectory("hq_stream_conversion_" + channelId);
                
                if (continuousSegmentKeys.isEmpty()) 
                {
                    return null;
                }               
                
                System.out.println("🚀 OPTIMIZED CONVERSION: Processing " + continuousSegmentKeys.size() + " segments for MP4, clip, and thumbnail");
                
                // Step 2: Download segments in exact sequence order
                List<Path> downloadedSegments = new ArrayList<>();
                
                for (int i = 0; i < continuousSegmentKeys.size(); i++) {
                    String segmentKey = continuousSegmentKeys.get(i);
                    int segmentNumber = extractSegmentNumber(segmentKey);
                
                    Path segmentFile = tempDir.resolve(String.format("seq_%04d.ts", i)); // Preserve sequence order
                    
                    try
                    {
                        s3Client.getObject(
                            new com.amazonaws.services.s3.model.GetObjectRequest(recordingsBucketName, segmentKey),
                            segmentFile.toFile()
                        );
                        downloadedSegments.add(segmentFile);
                    } 
                    catch (Exception e) 
                    {
                        System.out.println("Failed to download segment " + segmentNumber + ": " + segmentKey + " - " + e.getMessage());
                        // For continuous sequences, missing segments are critical
                        System.out.println("Missing segment in continuous sequence - conversion may be broken!");
                    }
                }
                
                if (downloadedSegments.isEmpty()) 
                {
                    return null;
                }
                
                // Step 3: Concatenate segments in exact order
                Path concatenatedFile = tempDir.resolve("high_quality_stream.ts");
                concatenateTsFiles(downloadedSegments, concatenatedFile);
                
                // Step 4: Convert to MP4 with high quality settings
                Path mp4File = tempDir.resolve("high_quality_stream.mp4");
                boolean conversionSuccess = convertTsToMp4WithHighQuality(concatenatedFile, mp4File);
                
                if (!conversionSuccess) {
                    System.out.println("⚠️ MP4 conversion failed, skipping clip and thumbnail generation");
                    return null;
                }
                
                System.out.println("✅ MP4 CONVERSION SUCCESSFUL - Now generating clip and thumbnail from local file...");
                
                // Step 5: Generate CLIP from local MP4 (no S3 download!)
                Path clipFile = tempDir.resolve("clip_3sec.mp4");
                boolean clipSuccess = generateClipWithFFmpeg(mp4File, clipFile, 3);
                
                // Step 6: Generate THUMBNAIL from local MP4 (no S3 download!)
                Path thumbnailFile = tempDir.resolve("thumbnail.jpg");
                boolean thumbnailSuccess = generateThumbnailWithFFmpeg(mp4File, thumbnailFile);
                
                // Step 7: Upload ALL assets to S3
                String baseFileName = "hq_stream_" + channelId + "_" + System.currentTimeMillis();
                
                // Upload MP4
                String mp4Key = "converted-mp4/" + channelId + "/" + baseFileName + ".mp4";
                uploadFileToS3(s3Client, mp4File, mp4Key, "video/mp4");
                assetKeys.put("mp4Key", mp4Key);
                Long duration = extractVideoDurationWithFFmpeg(mp4File);
                assetKeys.put("durationMS", duration != null ? duration.toString() : "0");
                // Upload Clip (if successful)
                if (clipSuccess)
                 {
                    String clipKey = "converted-mp4/" + channelId + "/" + baseFileName + "_clip_3sec.mp4";
                    uploadFileToS3(s3Client, clipFile, clipKey, "video/mp4");
                    assetKeys.put("clipKey", clipKey);
                    System.out.println("✅ Clip generated and uploaded: " + clipKey);
                } 
                else 
                {
                    System.out.println("⚠️ Clip generation failed");
                }
                
                // Upload Thumbnail (if successful)
                if (thumbnailSuccess) 
                {
                    String thumbnailKey = "converted-mp4/" + channelId + "/" + baseFileName + "_thumbnail.jpg";
                    uploadFileToS3(s3Client, thumbnailFile, thumbnailKey, "image/jpeg");
                    assetKeys.put("thumbnailKey", thumbnailKey);
                    System.out.println("✅ Thumbnail generated and uploaded: " + thumbnailKey);
                } 
                else 
                {
                    System.out.println("⚠️ Thumbnail generation failed");
                }
                
                // Success summary
                System.out.println("🎉 OPTIMIZED CONVERSION COMPLETED:");
                System.out.println("   📦 Segments processed: " + continuousSegmentKeys.size() + " (continuous from 0 to " + (continuousSegmentKeys.size() - 1) + ")");
                System.out.println("   💾 Successfully downloaded: " + downloadedSegments.size());
                System.out.println("   📱 MP4 file size: " + Files.size(mp4File) + " bytes");
                System.out.println("   🎬 Assets created: " + assetKeys.size());
                System.out.println("   📁 Base filename: " + baseFileName);
                
                if (downloadedSegments.size() == continuousSegmentKeys.size()) 
                {
                    System.out.println("   ✅ PERFECT: Complete continuous sequence - no missing segments!");
                } 
                else
                {
                    int missing = continuousSegmentKeys.size() - downloadedSegments.size();
                    System.out.println("   ⚠️ Missing segments: " + missing + " (sequence may be incomplete)");
                }
                
                return assetKeys;
                
            } 
            catch (Exception e) 
            {
                e.printStackTrace();
                return null;
            } 
            finally 
            {
                // Cleanup
                if (tempDir != null) 
                {
                    try 
                    {
                        Files.walk(tempDir)
                            .sorted((a, b) -> b.compareTo(a))
                            .forEach(path -> {
                                try 
                                {
                                    Files.deleteIfExists(path);
                                } 
                                catch (IOException e) {
                                    System.out.println("Failed to delete temp file: " + path);
                                }
                            });
                    } 
                    catch (Exception e) 
                    {
                        System.out.println("Failed to cleanup temp directory: " + e.getMessage());
                    }
                }
            }
        }
        
        /**
         * Helper method to upload file to S3 with proper metadata
         */
        private void uploadFileToS3(AmazonS3 s3Client, Path localFile, String s3Key, String contentType) throws Exception 
        {
            ObjectMetadata metadata = new ObjectMetadata();
            metadata.setContentLength(Files.size(localFile));
            metadata.setContentType(contentType);
            
            PutObjectRequest putRequest = new PutObjectRequest(
                recordingsBucketName,
                s3Key,
                localFile.toFile()
            ).withMetadata(metadata);
            
            s3Client.putObject(putRequest);
            System.out.println("📤 Uploaded to S3: " + s3Key + " (" + Files.size(localFile) + " bytes)");
        }
        
        /**
         * Convert TS to MP4 with HIGH QUALITY settings - no quality loss
         */
        private boolean convertTsToMp4WithHighQuality(Path inputTs, Path outputMp4) {
            try 
            {
                // Try different FFmpeg paths
                String[] ffmpegPaths = {
                    "C:\\ffmpeg\\bin\\ffmpeg.exe",  // Windows specific path
                    "C:\\ffmpeg\\ffmpeg.exe",       // Alternative Windows path
                    "ffmpeg",                       // System PATH
                    "/usr/bin/ffmpeg",              // Linux/Unix
                    "/usr/local/bin/ffmpeg"         // Alternative Linux/Unix
                };
                
                String ffmpegPath = null;
                
                // Find available FFmpeg
                for (String path : ffmpegPaths) {
                    try 
                    {
                        ProcessBuilder testPb = new ProcessBuilder(path, "-version");
                        testPb.redirectErrorStream(true);
                        Process testProcess = testPb.start();
                        int testExitCode = testProcess.waitFor();
                        
                        if (testExitCode == 0) 
                        {
                            ffmpegPath = path;
                            System.out.println("Found FFmpeg at: " + path);
                            break;
                        }
                    } 
                    catch (Exception e) 
                    {
                        // Try next path
                        continue;
                    }
                }
                
                if (ffmpegPath == null)
                {
                    System.out.println("FFmpeg not found - keeping as TS format");
                    return false;
                }
                
                // HIGH QUALITY conversion with minimal compression
                ProcessBuilder pb = new ProcessBuilder(
                    ffmpegPath,
                    "-i", inputTs.toString(),
                    "-c:v", "libx264",              // Video codec
                    "-preset", "slow",              // Slower preset = higher quality
                    "-crf", "18",                   // High quality (18 = visually lossless)
                    "-c:a", "aac",                  // Audio codec
                    "-b:a", "320k",                 // High quality audio bitrate
                    "-movflags", "faststart",       // Optimize for web playback
                    "-avoid_negative_ts", "make_zero", // Fix timestamp issues
                    "-y",                           // Overwrite output file
                    outputMp4.toString()
                );
                
                System.out.println("🎬 Converting with HIGH QUALITY settings (CRF 18, slow preset)...");
                
                // Capture output for debugging
                pb.redirectErrorStream(true);
                Process process = pb.start();
                
                // Read process output
                try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) 
                {
                    String line;
                    while ((line = reader.readLine()) != null) 
                    {
                        // Only log important FFmpeg messages
                        if (line.contains("Duration:") || line.contains("Stream") || line.contains("fps")) 
                        {
                            System.out.println("FFmpeg: " + line);
                        }
                    }
                }
                
                int exitCode = process.waitFor();
                
                if (exitCode == 0 && Files.exists(outputMp4) && Files.size(outputMp4) > 0) 
                {
                    System.out.println("✅ HIGH QUALITY MP4 conversion successful!");
                    System.out.println("   📱 Input TS size: " + Files.size(inputTs) + " bytes");
                    System.out.println("   📱 Output MP4 size: " + Files.size(outputMp4) + " bytes");
                    System.out.println("   🎥 Quality: CRF 18 (visually lossless)");
                    return true;
                } 
                else 
                {
                    System.out.println("❌ FFmpeg conversion failed with exit code: " + exitCode);
                    if (Files.exists(outputMp4)) {
                        System.out.println("   Output file size: " + Files.size(outputMp4) + " bytes");
                    }
                    return false;
                }
                
            }
            catch (Exception e) 
            {
                System.out.println("❌ High-quality conversion failed: " + e.getMessage());
                e.printStackTrace();
                return false;
            }
        }
        

        // Method to grant access to an S3 URL for a given object name
        public URL grantAccessUrl(String key) {
    
            // Generate the pre-signed URL with read access
            GetObjectRequest getObjectRequest = GetObjectRequest.builder()
                    .bucket(recordingsBucketName)
                    .key(key)
                    .build();
    
            GetObjectPresignRequest presignRequest = GetObjectPresignRequest.builder()
                    .getObjectRequest(getObjectRequest)
                    .signatureDuration(Duration.ofHours(1)) // URL valid for 1 hour
                    .build();
    
            return s3Presigner.presignGetObject(presignRequest).url();
        }

        /**
         * Convert live stream recording from HLS (TS + M3U8) to MP4 and save to S3
         * @param channelArn The ARN of the channel containing the recording
         * @return Map containing conversion result and MP4 file key
         */
        public Map<String, Object> convertLiveStreamToMp4(String channelArn) 
        {
            Map<String, Object> result = new HashMap<>();
            
            try 
            {
                // Step 1: Extract channel ID from ARN
                String channelId = extractChannelIdFromArn(channelArn);
                
                // Step 2: Create S3 client
                BasicAWSCredentials awsCreds = new BasicAWSCredentials(awsAccessKey, awsSecretKey);
                AmazonS3 s3Client = AmazonS3ClientBuilder.standard()
                        .withCredentials(new AWSStaticCredentialsProvider(awsCreds))
                        .withRegion(awsRegion)
                        .build();

                // Step 3: Search for recordings in S3 bucket
                String recordingPrefix = constructRecordingPath(channelId);
                ListObjectsV2Request listRequest = new ListObjectsV2Request()
                        .withBucketName(recordingsBucketName)
                        .withPrefix(recordingPrefix)
                        .withMaxKeys(1000);

                ListObjectsV2Result listResult = s3Client.listObjectsV2(listRequest);
                
                if (listResult.getObjectSummaries().isEmpty()) 
                {
                    result.put("status", "NO_RECORDINGS");
                    result.put("message", "No recordings found for this channel");
                    return result;
                }

                // Step 4: Check if MP4 already exists in converted directory
                String convertedPrefix = "converted-mp4/" + channelId + "/";
                ListObjectsV2Request convertedListRequest = new ListObjectsV2Request()
                        .withBucketName(recordingsBucketName)
                        .withPrefix(convertedPrefix)
                        .withMaxKeys(100);

                ListObjectsV2Result convertedListResult = s3Client.listObjectsV2(convertedListRequest);
                S3ObjectSummary existingMp4 = convertedListResult.getObjectSummaries().stream()
                        .filter(obj -> obj.getKey().endsWith(".mp4"))
                        .max(Comparator.comparing(S3ObjectSummary::getLastModified))
                        .orElse(null);

                if (existingMp4 != null) 
                {
                    // MP4 already exists, return file key and download URL
                    result.put("status", "COMPLETED");
                    result.put("mp4Key", existingMp4.getKey());
                    result.put("fileSize", existingMp4.getSize());
                    result.put("lastModified", existingMp4.getLastModified());
                    result.put("message", "MP4 file already exists");
                    return result;
                }

                // Step 5: Find ALL recording sessions for complete stream conversion
                List<String> allSegmentKeys = findAllSegmentsFromCompleteStream(s3Client, listResult.getObjectSummaries());
                
                if (allSegmentKeys.isEmpty()) {
                    result.put("status", "NO_SEGMENTS_FOUND");
                    result.put("message", "No video segments found for conversion");
                    return result;
                }

                // Step 6: Perform OPTIMIZED HLS to MP4/clip/thumbnail conversion with ALL segments
                Map<String, String> allAssets = performCompleteStreamToMp4Conversion(s3Client, allSegmentKeys, channelId);
                
                if (allAssets != null && allAssets.containsKey("mp4Key")) 
                {
                    // Conversion successful, return all asset keys and download URL
                    String mp4Key = allAssets.get("mp4Key");
                    URL downloadUrl = grantAccessUrl(mp4Key);
                    result.put("status", "COMPLETED");
                    result.put("mp4Key", mp4Key);
                    result.put("mp4DownloadUrl", downloadUrl.toString());
                    result.put("clipKey", allAssets.get("clipKey"));
                    result.put("thumbnailKey", allAssets.get("thumbnailKey"));
                    result.put("durationMS", allAssets.get("durationMS"));
                    result.put("channelId", channelId);
                    result.put("message", "Live stream successfully converted with all assets generated");

                    return result;
                } 
                else 
                {
                    result.put("status", "CONVERSION_FAILED");
                    result.put("message", "Failed to convert HLS to MP4 and generate assets");
                    return result;
                }
                
            } 
            catch (Exception e) 
            {
                result.put("status", "ERROR");
                result.put("message", "Failed to convert live stream: " + e.getMessage());
                return result;
            }
        }     
        /**
         * Extract a unique identifier from segment path for deduplication
         * This extracts the actual segment filename without the full path
         */
        private String extractSegmentIdentifier(String segmentKey) 
        {
            if (segmentKey == null) return "";
            
            // Extract just the filename from the full path
            int lastSlash = segmentKey.lastIndexOf('/');
            String filename = lastSlash >= 0 ? segmentKey.substring(lastSlash + 1) : segmentKey;
            
            // Remove any query parameters or fragments
            int queryIndex = filename.indexOf('?');
            if (queryIndex >= 0) {
                filename = filename.substring(0, queryIndex);
            }
            
            return filename.toLowerCase(); // Normalize case for comparison
        }
        /**
         * Concatenate TS files (TS format allows direct concatenation)
         */
        private void concatenateTsFiles(List<Path> segments, Path outputFile) throws IOException 
        {
            try (FileOutputStream output = new FileOutputStream(outputFile.toFile())) {
                for (Path segment : segments) {
                    try (FileInputStream input = new FileInputStream(segment.toFile())) {
                        byte[] buffer = new byte[8192];
                        int bytesRead;
                        while ((bytesRead = input.read(buffer)) != -1) {
                            output.write(buffer, 0, bytesRead);
                        }
                    }
                }
            }
        }
        
         /**
          * Check if MP4 files exist for a channel and return download URL
          * @param channelId Channel ID for constructing output path
          * @return Map containing MP4 file info and download URL if available
          */
         public Map<String, Object> checkMp4Status(String channelId) {
             Map<String, Object> result = new HashMap<>();
             
             try 
             {
                 BasicAWSCredentials awsCreds = new BasicAWSCredentials(awsAccessKey, awsSecretKey);
                 AmazonS3 s3Client = AmazonS3ClientBuilder.standard()
                         .withCredentials(new AWSStaticCredentialsProvider(awsCreds))
                         .withRegion(awsRegion)
                         .build();

                 // Look for MP4 files in the converted directory
                 String convertedPrefix = "converted-mp4/" + channelId + "/";
                 ListObjectsV2Request listRequest = new ListObjectsV2Request()
                         .withBucketName(recordingsBucketName)
                         .withPrefix(convertedPrefix)
                         .withMaxKeys(100);

                 ListObjectsV2Result listResult = s3Client.listObjectsV2(listRequest);
                 
                 // Find the most recent MP4 file
                 S3ObjectSummary latestMp4 = listResult.getObjectSummaries().stream()
                         .filter(obj -> obj.getKey().endsWith(".mp4"))
                         .max(Comparator.comparing(S3ObjectSummary::getLastModified))
                         .orElse(null);

                 if (latestMp4 != null) {
                     // MP4 exists! Generate download URL using grantAccessUrl
                     URL downloadUrl = grantAccessUrl(latestMp4.getKey());
                     
                     result.put("status", "AVAILABLE");
                     result.put("channelId", channelId);
                     result.put("mp4DownloadUrl", downloadUrl.toString());
                     result.put("mp4Key", latestMp4.getKey());
                     result.put("fileSize", latestMp4.getSize());
                     result.put("lastModified", latestMp4.getLastModified());
                     result.put("message", "MP4 file found and ready for download");
                     
                     return result;
                 }
                 
                 // No MP4 found
                 result.put("status", "NOT_FOUND");
                 result.put("channelId", channelId);
                 result.put("message", "No MP4 files found for this channel");
                 
                 return result;
                 
             } catch (Exception e) {
                 result.put("status", "ERROR");
                 result.put("channelId", channelId);
                 result.put("message", "Error checking MP4 status: " + e.getMessage());
                 return result;
             }
         }

        /**
         * Generate 3-second clip from MP4 file in S3 and save back to S3
         * @param mp4Key S3 key of the source MP4 file
         * @param streamId LiveStream ID to update with clip key
         * @return S3 key of the generated clip
         */
        @Override
        public String generateClipFromMp4(String mp4Key, String streamId) 
        {
            System.out.println("🎬 Starting 3-second clip generation for MP4: " + mp4Key);
            
            Path tempDir = null;
            try {
                // Step 1: Create temporary directory
                tempDir = Files.createTempDirectory("clip_generation_" + System.currentTimeMillis());
                
                // Step 2: Create S3 client
                BasicAWSCredentials awsCreds = new BasicAWSCredentials(awsAccessKey, awsSecretKey);
                AmazonS3 s3Client = AmazonS3ClientBuilder.standard()
                        .withCredentials(new AWSStaticCredentialsProvider(awsCreds))
                        .withRegion(awsRegion)
                        .build();
                
                // Step 3: Download MP4 file from S3
                Path originalMp4 = tempDir.resolve("original.mp4");
                System.out.println("📥 Downloading MP4 from S3: " + mp4Key);
                s3Client.getObject(
                    new com.amazonaws.services.s3.model.GetObjectRequest(recordingsBucketName, mp4Key),
                    originalMp4.toFile()
                );
                
                // Step 4: Generate 3-second clip using FFmpeg
                Path clipFile = tempDir.resolve("clip_3sec.mp4");
                boolean clipSuccess = generateClipWithFFmpeg(originalMp4, clipFile, 3);
                
                if (!clipSuccess) 
                {
                    System.out.println("❌ Failed to generate clip using FFmpeg");
                    return null;
                }
                
                // Step 5: Generate clip key for S3
                String clipKey = mp4Key.replaceAll("\\.[^.]+$", "_clip_3sec.mp4");
                
                // Step 6: Upload clip to S3
                System.out.println("📤 Uploading clip to S3: " + clipKey);
                ObjectMetadata clipMetadata = new ObjectMetadata();
                clipMetadata.setContentLength(Files.size(clipFile));
                clipMetadata.setContentType("video/mp4");
                
                PutObjectRequest putClipRequest = new PutObjectRequest(
                    recordingsBucketName,
                    clipKey,
                    clipFile.toFile()
                ).withMetadata(clipMetadata);
                
                s3Client.putObject(putClipRequest);
                
                // Step 7: Update LiveStream document with clip key
                if (streamId != null) 
                {
                    try 
                    {
                        LiveStream stream = liveStreamRepository.findById(streamId).orElse(null);
                        if (stream != null) 
                        {
                            stream.setThumbnailClipUrl(clipKey);
                            liveStreamRepository.save(stream);
                            System.out.println("🔄 LiveStream document would be updated with clipKey: " + clipKey);
                        }
                    } 
                    catch (Exception e) 
                    {
                        System.out.println("⚠️ Failed to update LiveStream document: " + e.getMessage());
                    }
                }
                
                System.out.println("✅ 3-second clip generated successfully!");
                System.out.println("   📁 Original: " + mp4Key);
                System.out.println("   📁 Clip: " + clipKey);
                System.out.println("   📱 Clip size: " + Files.size(clipFile) + " bytes");
                
                return clipKey;
                
            } 
            catch (Exception e) 
            {
                System.out.println("❌ Failed to generate clip: " + e.getMessage());
                e.printStackTrace();
                return null;
                
            }
            finally {
                // Cleanup temporary directory
                if (tempDir != null) 
                {
                    try 
                    {
                        Files.walk(tempDir)
                            .sorted((a, b) -> b.compareTo(a))
                            .forEach(path -> 
                            {
                                try 
                                {
                                    Files.deleteIfExists(path);
                                } 
                                catch (IOException e) 
                                {
                                    System.out.println("Failed to delete temp file: " + path);
                                }
                            });
                    } 
                    catch (Exception e) 
                    {
                        System.out.println("Failed to cleanup temp directory: " + e.getMessage());
                    }
                }
            }
        }
        
        /**
         * Generate clip using FFmpeg
         * @param inputMp4 Input MP4 file path
         * @param outputClip Output clip file path  
         * @param durationSeconds Duration of clip in seconds
         * @return true if successful, false otherwise
         */
        private boolean generateClipWithFFmpeg(Path inputMp4, Path outputClip, int durationSeconds) 
        {
            try 
            {
                // Try different FFmpeg paths (reusing logic from existing method)
                String[] ffmpegPaths = 
                {
                    "C:\\ffmpeg\\bin\\ffmpeg.exe",  // Windows specific path
                    "C:\\ffmpeg\\ffmpeg.exe",       // Alternative Windows path
                    "ffmpeg",                       // System PATH
                    "/usr/bin/ffmpeg",              // Linux/Unix
                    "/usr/local/bin/ffmpeg"         // Alternative Linux/Unix
                };
                
                String ffmpegPath = null;
                
                // Find available FFmpeg
                for (String path : ffmpegPaths) 
                {
                    try 
                    {
                        ProcessBuilder testPb = new ProcessBuilder(path, "-version");
                        testPb.redirectErrorStream(true);
                        Process testProcess = testPb.start();
                        int testExitCode = testProcess.waitFor();
                        
                        if (testExitCode == 0) 
                        {
                            ffmpegPath = path;
                            System.out.println("Found FFmpeg at: " + path);
                            break;
                        }
                    } 
                    catch (Exception e) 
                    {
                        // Try next path
                        continue;
                    }
                }
                
                if (ffmpegPath == null) 
                {
                    System.out.println("FFmpeg not found - cannot generate clip");
                    return false;
                }
                
                // Generate clip with FFmpeg
                ProcessBuilder pb = new ProcessBuilder(
                    ffmpegPath,
                    "-i", inputMp4.toString(),      // Input file
                    "-t", String.valueOf(durationSeconds), // Duration (3 seconds)
                    "-ss", "0",                     // Start from beginning
                    "-c:v", "libx264",              // Video codec
                    "-c:a", "aac",                  // Audio codec
                    "-preset", "fast",              // Fast encoding preset
                    "-crf", "23",                   // Good quality
                    "-movflags", "faststart",       // Optimize for web playback
                    "-y",                           // Overwrite output file
                    outputClip.toString()
                );
                
                System.out.println("🎬 Generating " + durationSeconds + "-second clip...");
                
                // Execute FFmpeg
                pb.redirectErrorStream(true);
                Process process = pb.start();
                
                // Read process output
                try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) 
                {
                    String line;
                    while ((line = reader.readLine()) != null) 
                    {
                        // Log important FFmpeg messages
                        if (line.contains("Duration:") || line.contains("time=") || line.contains("fps")) 
                        {
                            System.out.println("FFmpeg: " + line);
                        }
                    }
                }
                
                int exitCode = process.waitFor();
                
                if (exitCode == 0 && Files.exists(outputClip) && Files.size(outputClip) > 0) 
                {
                    System.out.println("✅ Clip generation successful!");
                    System.out.println("   📱 Input size: " + Files.size(inputMp4) + " bytes");
                    System.out.println("   📱 Clip size: " + Files.size(outputClip) + " bytes");
                    System.out.println("   ⏱️ Duration: " + durationSeconds + " seconds");
                    return true;
                } 
                else 
                {
                    System.out.println("❌ FFmpeg clip generation failed with exit code: " + exitCode);
                    if (Files.exists(outputClip)) 
                    {
                        System.out.println("   Output file size: " + Files.size(outputClip) + " bytes");
                    }
                    return false;
                }
                
            } 
            catch (Exception e) 
            {
                System.out.println("❌ Clip generation failed: " + e.getMessage());
                e.printStackTrace();
                return false;
            }
        }

        /**
         * Generate thumbnail image from MP4 file in S3 and save back to S3
         * @param mp4Key S3 key of the source MP4 file
         * @param streamId LiveStream ID to update with thumbnail key
         * @return S3 key of the generated thumbnail
         */
        @Override
        public String generateThumbnailFromMp4(String mp4Key, String streamId) 
        {
            System.out.println("🖼️ Starting thumbnail generation for MP4: " + mp4Key);
            
            Path tempDir = null;
            try {
                // Step 1: Create temporary directory
                tempDir = Files.createTempDirectory("thumbnail_generation_" + System.currentTimeMillis());
                
                // Step 2: Create S3 client
                BasicAWSCredentials awsCreds = new BasicAWSCredentials(awsAccessKey, awsSecretKey);
                AmazonS3 s3Client = AmazonS3ClientBuilder.standard()
                        .withCredentials(new AWSStaticCredentialsProvider(awsCreds))
                        .withRegion(awsRegion)
                        .build();
                
                // Step 3: Download MP4 file from S3
                Path originalMp4 = tempDir.resolve("original.mp4");
                System.out.println("📥 Downloading MP4 from S3: " + mp4Key);
                s3Client.getObject(
                    new com.amazonaws.services.s3.model.GetObjectRequest(recordingsBucketName, mp4Key),
                    originalMp4.toFile()
                );
                
                // Step 4: Generate thumbnail using FFmpeg
                Path thumbnailFile = tempDir.resolve("thumbnail.jpg");
                boolean thumbnailSuccess = generateThumbnailWithFFmpeg(originalMp4, thumbnailFile);
                
                if (!thumbnailSuccess) 
                {
                    System.out.println("❌ Failed to generate thumbnail using FFmpeg");
                    return null;
                }
                
                // Step 5: Generate thumbnail key for S3
                String thumbnailKey = mp4Key.replaceAll("\\.[^.]+$", "_thumbnail.jpg");
                
                // Step 6: Upload thumbnail to S3
                System.out.println("📤 Uploading thumbnail to S3: " + thumbnailKey);
                ObjectMetadata thumbnailMetadata = new ObjectMetadata();
                thumbnailMetadata.setContentLength(Files.size(thumbnailFile));
                thumbnailMetadata.setContentType("image/jpeg");
                
                PutObjectRequest putThumbnailRequest = new PutObjectRequest(
                    recordingsBucketName,
                    thumbnailKey,
                    thumbnailFile.toFile()
                ).withMetadata(thumbnailMetadata);
                
                s3Client.putObject(putThumbnailRequest);
                
                // Step 7: Update LiveStream document with thumbnail key
                if (streamId != null) 
                {
                    try 
                    {
                        LiveStream stream = liveStreamRepository.findById(streamId).orElse(null);
                        if (stream != null) 
                        {
                            stream.setThumbnailImageUrl(thumbnailKey);
                            liveStreamRepository.save(stream);
                            System.out.println("🔄 LiveStream document updated with thumbnailKey: " + thumbnailKey);
                        }
                    } 
                    catch (Exception e) 
                    {
                        System.out.println("⚠️ Failed to update LiveStream document: " + e.getMessage());
                    }
                }
                
                System.out.println("✅ Thumbnail generated successfully!");
                System.out.println("   📁 Original: " + mp4Key);
                System.out.println("   📁 Thumbnail: " + thumbnailKey);
                System.out.println("   📱 Thumbnail size: " + Files.size(thumbnailFile) + " bytes");
                
                return thumbnailKey;
                
            } 
            catch (Exception e) 
            {
                System.out.println("❌ Failed to generate thumbnail: " + e.getMessage());
                e.printStackTrace();
                return null;
                
            }
            finally {
                // Cleanup temporary directory
                if (tempDir != null) 
                {
                    try 
                    {
                        Files.walk(tempDir)
                            .sorted((a, b) -> b.compareTo(a))
                            .forEach(path -> 
                            {
                                try 
                                {
                                    Files.deleteIfExists(path);
                                } 
                                catch (IOException e) 
                                {
                                    System.out.println("Failed to delete temp file: " + path);
                                }
                            });
                    } 
                    catch (Exception e) 
                    {
                        System.out.println("Failed to cleanup temp directory: " + e.getMessage());
                    }
                }
            }
        }

        /**
         * Generate thumbnail using FFmpeg
         * @param inputMp4 Input MP4 file path
         * @param outputThumbnail Output thumbnail file path  
         * @return true if successful, false otherwise
         */
        private boolean generateThumbnailWithFFmpeg(Path inputMp4, Path outputThumbnail) 
        {
            try 
            {
                // Try different FFmpeg paths (reusing logic from existing method)
                String[] ffmpegPaths = 
                {
                    "C:\\ffmpeg\\bin\\ffmpeg.exe",  // Windows specific path
                    "C:\\ffmpeg\\ffmpeg.exe",       // Alternative Windows path
                    "ffmpeg",                       // System PATH
                    "/usr/bin/ffmpeg",              // Linux/Unix
                    "/usr/local/bin/ffmpeg"         // Alternative Linux/Unix
                };
                
                String ffmpegPath = null;
                
                // Find available FFmpeg
                for (String path : ffmpegPaths) 
                {
                    try 
                    {
                        ProcessBuilder testPb = new ProcessBuilder(path, "-version");
                        testPb.redirectErrorStream(true);
                        Process testProcess = testPb.start();
                        int testExitCode = testProcess.waitFor();
                        
                        if (testExitCode == 0) 
                        {
                            ffmpegPath = path;
                            System.out.println("Found FFmpeg at: " + path);
                            break;
                        }
                    } 
                    catch (Exception e) 
                    {
                        // Try next path
                        continue;
                    }
                }
                
                if (ffmpegPath == null) 
                {
                    System.out.println("FFmpeg not found - cannot generate thumbnail");
                    return false;
                }
                
                // Generate thumbnail with FFmpeg - extract frame at 1 second
                ProcessBuilder pb = new ProcessBuilder(
                    ffmpegPath,
                    "-i", inputMp4.toString(),      // Input file
                    "-ss", "00:00:01",              // Seek to 1 second
                    "-vframes", "1",                // Extract 1 frame
                    "-q:v", "2",                    // High quality (2 = very high quality for JPEG)
                    "-vf", "scale=1280:720",        // Scale to 720p resolution
                    "-y",                           // Overwrite output file
                    outputThumbnail.toString()
                );
                
                System.out.println("🖼️ Generating thumbnail from 1-second mark...");
                
                // Execute FFmpeg
                pb.redirectErrorStream(true);
                Process process = pb.start();
                
                // Read process output
                try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) 
                {
                    String line;
                    while ((line = reader.readLine()) != null) 
                    {
                        // Log important FFmpeg messages
                        if (line.contains("Duration:") || line.contains("Stream") || line.contains("fps")) 
                        {
                            System.out.println("FFmpeg: " + line);
                        }
                    }
                }
                
                int exitCode = process.waitFor();
                
                if (exitCode == 0 && Files.exists(outputThumbnail) && Files.size(outputThumbnail) > 0) 
                {
                    System.out.println("✅ Thumbnail generation successful!");
                    System.out.println("   📱 Input size: " + Files.size(inputMp4) + " bytes");
                    System.out.println("   📱 Thumbnail size: " + Files.size(outputThumbnail) + " bytes");
                    System.out.println("   🖼️ Resolution: 1280x720");
                    return true;
                } 
                else 
                {
                    System.out.println("❌ FFmpeg thumbnail generation failed with exit code: " + exitCode);
                    if (Files.exists(outputThumbnail)) 
                    {
                        System.out.println("   Output file size: " + Files.size(outputThumbnail) + " bytes");
                    }
                    return false;
                }
                
            } 
            catch (Exception e) 
            {
                System.out.println("❌ Thumbnail generation failed: " + e.getMessage());
                e.printStackTrace();
                return false;
            }
        }

        /**
         * Upload custom clip for live stream and replace existing clip
         * @param clipFileBytes The clip file as byte array
         * @param originalFileName Original filename for content type detection
         * @param streamId LiveStream ID to update with new clip key
         * @param channelId Channel ID for S3 path construction
         * @return S3 key of the uploaded clip
         */
        @Override
        public String uploadCustomClipForStream(byte[] clipFileBytes, String originalFileName, String streamId, String channelId) 
        {
            System.out.println("🎬 Starting custom clip upload for stream: " + streamId);
            
            try 
            {
                // Step 1: Create S3 client
                BasicAWSCredentials awsCreds = new BasicAWSCredentials(awsAccessKey, awsSecretKey);
                AmazonS3 s3Client = AmazonS3ClientBuilder.standard()
                        .withCredentials(new AWSStaticCredentialsProvider(awsCreds))
                        .withRegion(awsRegion)
                        .build();
                
                // Step 2: Determine content type from filename
                String contentType = "video/mp4"; // Default
                if (originalFileName != null) {
                    String lowercaseFileName = originalFileName.toLowerCase();
                    if (lowercaseFileName.endsWith(".mp4")) {
                        contentType = "video/mp4";
                    } else if (lowercaseFileName.endsWith(".mov")) {
                        contentType = "video/quicktime";
                    } else if (lowercaseFileName.endsWith(".avi")) {
                        contentType = "video/x-msvideo";
                    } else if (lowercaseFileName.endsWith(".mkv")) {
                        contentType = "video/x-matroska";
                    }
                }
                
                // Step 3: Generate unique clip key (custom clips get "custom_clip" prefix)
                String customClipKey = "converted-mp4/custom_clip_" + streamId + "_" + System.currentTimeMillis() + ".mp4";
                
                // Step 4: Upload clip to S3
                System.out.println("📤 Uploading custom clip to S3: " + customClipKey);
                ObjectMetadata clipMetadata = new ObjectMetadata();
                clipMetadata.setContentLength(clipFileBytes.length);
                clipMetadata.setContentType(contentType);
                clipMetadata.addUserMetadata("upload-type", "custom-clip");
                clipMetadata.addUserMetadata("stream-id", streamId);
                clipMetadata.addUserMetadata("original-filename", originalFileName != null ? originalFileName : "unknown");
                
                // Create input stream from byte array
                ByteArrayInputStream inputStream = new ByteArrayInputStream(clipFileBytes);
                
                PutObjectRequest putClipRequest = new PutObjectRequest(
                    recordingsBucketName,
                    customClipKey,
                    inputStream,
                    clipMetadata
                );
                
                s3Client.putObject(putClipRequest);
                
                // Step 5: Delete any existing clips for this stream (auto-generated or previous custom)
                try 
                {
                    deleteExistingClipsForStream(s3Client, channelId, streamId);
                } 
                catch (Exception e) 
                {
                    System.out.println("⚠️ Warning: Failed to delete existing clips: " + e.getMessage());
                    // Continue anyway - the new clip will be used
                }
                
                // Step 6: Update LiveStream document with new custom clip key
                if (streamId != null) 
                {
                    try 
                    {
                        LiveStream stream = liveStreamRepository.findById(streamId).orElse(null);
                        if (stream != null) 
                        {
                            stream.setThumbnailClipUrl(customClipKey);
                            liveStreamRepository.save(stream);
                            System.out.println("🔄 LiveStream document updated with custom clip key: " + customClipKey);
                        }
                    } 
                    catch (Exception e) 
                    {
                        System.out.println("⚠️ Failed to update LiveStream document: " + e.getMessage());
                    }
                }
                
                System.out.println("✅ Custom clip uploaded successfully!");
                System.out.println("   📁 Clip S3 key: " + customClipKey);
                System.out.println("   📱 Clip size: " + clipFileBytes.length + " bytes");
                System.out.println("   🎥 Content type: " + contentType);
                System.out.println("   📝 Original filename: " + (originalFileName != null ? originalFileName : "unknown"));
                
                return customClipKey;
                
            } 
            catch (Exception e) 
            {
                System.out.println("❌ Failed to upload custom clip: " + e.getMessage());
                e.printStackTrace();
                return null;
            }
        }
        
        /**
         * Upload custom thumbnail image for live stream and replace existing thumbnail
         * @param thumbnailFileBytes The thumbnail image file as byte array
         * @param originalFileName Original filename for content type detection
         * @param streamId LiveStream ID to update with new thumbnail key
         * @param channelId Channel ID for S3 path construction
         * @return S3 key of the uploaded thumbnail
         */
        @Override
        public String uploadCustomThumbnailForStream(byte[] thumbnailFileBytes, String originalFileName, String streamId, String channelId) 
        {
            System.out.println("🖼️ Starting custom thumbnail upload for stream: " + streamId);
            
            try 
            {
                // Step 1: Create S3 client
                BasicAWSCredentials awsCreds = new BasicAWSCredentials(awsAccessKey, awsSecretKey);
                AmazonS3 s3Client = AmazonS3ClientBuilder.standard()
                        .withCredentials(new AWSStaticCredentialsProvider(awsCreds))
                        .withRegion(awsRegion)
                        .build();
                
                // Step 2: Determine content type from filename
                String contentType = "image/jpeg"; // Default
                if (originalFileName != null) {
                    String lowercaseFileName = originalFileName.toLowerCase();
                    if (lowercaseFileName.endsWith(".jpg") || lowercaseFileName.endsWith(".jpeg")) {
                        contentType = "image/jpeg";
                    } else if (lowercaseFileName.endsWith(".png")) {
                        contentType = "image/png";
                    } else if (lowercaseFileName.endsWith(".gif")) {
                        contentType = "image/gif";
                    } else if (lowercaseFileName.endsWith(".webp")) {
                        contentType = "image/webp";
                    }
                }
                
                // Step 3: Generate unique thumbnail key (custom thumbnails get "custom_thumbnail" prefix)
                String customThumbnailKey = "converted-mp4/custom_thumbnail_" + streamId + "_" + System.currentTimeMillis() + getImageExtension(contentType);
                
                // Step 4: Upload thumbnail to S3
                System.out.println("📤 Uploading custom thumbnail to S3: " + customThumbnailKey);
                ObjectMetadata thumbnailMetadata = new ObjectMetadata();
                thumbnailMetadata.setContentLength(thumbnailFileBytes.length);
                thumbnailMetadata.setContentType(contentType);
                thumbnailMetadata.addUserMetadata("upload-type", "custom-thumbnail");
                thumbnailMetadata.addUserMetadata("stream-id", streamId);
                thumbnailMetadata.addUserMetadata("original-filename", originalFileName != null ? originalFileName : "unknown");
                
                // Create input stream from byte array
                ByteArrayInputStream inputStream = new ByteArrayInputStream(thumbnailFileBytes);
                
                PutObjectRequest putThumbnailRequest = new PutObjectRequest(
                    recordingsBucketName,
                    customThumbnailKey,
                    inputStream,
                    thumbnailMetadata
                );
                
                s3Client.putObject(putThumbnailRequest);
                
                // Step 5: Delete any existing thumbnails for this stream (auto-generated or previous custom)
                try 
                {
                    deleteExistingThumbnailsForStream(s3Client, channelId, streamId);
                } 
                catch (Exception e) 
                {
                    System.out.println("⚠️ Warning: Failed to delete existing thumbnails: " + e.getMessage());
                    // Continue anyway - the new thumbnail will be used
                }
                
                // Step 6: Update LiveStream document with new custom thumbnail key
                if (streamId != null) 
                {
                    try 
                    {
                        LiveStream stream = liveStreamRepository.findById(streamId).orElse(null);
                        if (stream != null) 
                        {
                            stream.setThumbnailImageUrl(customThumbnailKey);
                            liveStreamRepository.save(stream);
                            System.out.println("🔄 LiveStream document updated with custom thumbnail key: " + customThumbnailKey);
                        }
                    } 
                    catch (Exception e) 
                    {
                        System.out.println("⚠️ Failed to update LiveStream document: " + e.getMessage());
                    }
                }
                
                System.out.println("✅ Custom thumbnail uploaded successfully!");
                System.out.println("   📁 Thumbnail S3 key: " + customThumbnailKey);
                System.out.println("   📱 Thumbnail size: " + thumbnailFileBytes.length + " bytes");
                System.out.println("   🖼️ Content type: " + contentType);
                System.out.println("   📝 Original filename: " + (originalFileName != null ? originalFileName : "unknown"));
                
                return customThumbnailKey;
                
            } 
            catch (Exception e) 
            {
                System.out.println("❌ Failed to upload custom thumbnail: " + e.getMessage());
                e.printStackTrace();
                return null;
            }
        }
        
        /**
         * Helper method to get image file extension based on content type
         */
        private String getImageExtension(String contentType) 
        {
            switch (contentType.toLowerCase()) {
                case "image/jpeg":
                    return ".jpg";
                case "image/png":
                    return ".png";
                case "image/gif":
                    return ".gif";
                case "image/webp":
                    return ".webp";
                default:
                    return ".jpg";
            }
        }
        
        /**
         * Delete existing thumbnails for a stream (both auto-generated and previous custom thumbnails)
         */
        private void deleteExistingThumbnailsForStream(AmazonS3 s3Client, String channelId, String streamId) 
        {
            try 
            {
                // List of possible thumbnail patterns to delete
                String[] thumbnailPatterns = {
                    "converted-mp4/" + channelId + "/custom_thumbnail_" + streamId,  // Previous custom thumbnails
                    "converted-mp4/" + channelId + "/hq_stream_" + channelId + "_.*_thumbnail", // Auto-generated thumbnails
                };
                
                System.out.println("🗑️ Cleaning up existing thumbnails for stream: " + streamId);
                
                // For simplicity, we'll just log this - in production you might want to implement proper cleanup
                System.out.println("   Pattern cleanup would target:");
                for (String pattern : thumbnailPatterns) {
                    System.out.println("   - " + pattern);
                }
                
                // Note: Full implementation would use S3 list objects and delete matching thumbnails
                // This is simplified for now to avoid complexity
                
            } 
            catch (Exception e) 
            {
                System.out.println("⚠️ Warning: Thumbnail cleanup failed: " + e.getMessage());
            }
        }
        
        /**
         * Delete existing clips for a stream (both auto-generated and previous custom clips)
         */
        private void deleteExistingClipsForStream(AmazonS3 s3Client, String channelId, String streamId) 
        {
            try 
            {
                System.out.println("🗑️ Deleting existing clips for stream: " + streamId);
                
                // List all objects in the channel directory
                String prefix = "converted-mp4/" + channelId + "/";
                ListObjectsV2Request listRequest = new ListObjectsV2Request()
                        .withBucketName(recordingsBucketName)
                        .withPrefix(prefix)
                        .withMaxKeys(100);

                ListObjectsV2Result listResult = s3Client.listObjectsV2(listRequest);
                
                // Find clips related to this stream (both auto-generated and custom)
                List<String> clipsToDelete = new ArrayList<>();
                
                for (S3ObjectSummary obj : listResult.getObjectSummaries()) 
                {
                    String key = obj.getKey();
                    
                    // Check for auto-generated clips (contains "_clip_3sec.mp4")
                    if (key.contains("_clip_3sec.mp4")) {
                        clipsToDelete.add(key);
                    }
                    
                    // Check for custom clips (contains streamId and "custom_clip")
                    if (key.contains("custom_clip_") && key.contains(streamId)) {
                        clipsToDelete.add(key);
                    }
                }
                
                // Delete found clips
                for (String clipKey : clipsToDelete) 
                {
                    try 
                    {
                        s3Client.deleteObject(recordingsBucketName, clipKey);
                        System.out.println("🗑️ Deleted existing clip: " + clipKey);
                    } 
                    catch (Exception e) 
                    {
                        System.out.println("⚠️ Failed to delete clip: " + clipKey + " - " + e.getMessage());
                    }
                }
                
                if (clipsToDelete.isEmpty()) 
                {
                    System.out.println("ℹ️ No existing clips found to delete for stream: " + streamId);
                } 
                else 
                {
                    System.out.println("✅ Deleted " + clipsToDelete.size() + " existing clips for stream: " + streamId);
                }
                
            } 
            catch (Exception e) 
            {
                System.out.println("❌ Error during clip cleanup: " + e.getMessage());
                e.printStackTrace();
            }
        }

        @Override
        @Async
        public void convertToMp4Async(String streamId, String channelArn) 
        {            
            try 
            {
                // Find the LiveStream document
                LiveStream stream = liveStreamRepository.findById(streamId).orElse(null);
                if (stream == null) 
                {
                    return;
                }
                
                // Update status to PROCESSING
                stream.setMp4ConversionStatus(Mp4ConversionStatus.PROCESSING);
                liveStreamRepository.save(stream);
                System.out.println("✅ Updated stream status to PROCESSING");
                
                // Perform the actual conversion using existing method
                Map<String, Object> result = convertLiveStreamToMp4(channelArn);
                
                // Update LiveStream document based on result
                if ("COMPLETED".equals(result.get("status"))) 
                {
                    stream.setMp4ConversionStatus(Mp4ConversionStatus.COMPLETED);
                    stream.setMp4Key((String) result.get("mp4Key"));
                    
                    // Set clip and thumbnail keys from the optimized conversion (no additional S3 downloads!)
                    String clipKey = (String) result.get("clipKey");
                    String thumbnailKey = (String) result.get("thumbnailKey");
                    stream.setThumbnailClipUrl(clipKey);
                    stream.setThumbnailImageUrl(thumbnailKey);
                    
                    liveStreamRepository.save(stream);
                    
                    // Parse durationMS safely from String to Long
                    Long durationMS = 0L;
                    try
                    {
                        String durationStr = (String) result.get("durationMS");
                        if (durationStr != null && !durationStr.isEmpty()) {
                            durationMS = Long.parseLong(durationStr);
                        }
                    } catch (Exception e) {
                        System.out.println("⚠️ Warning: Failed to parse duration, using default value: " + e.getMessage());
                    }
                    
                    // Create Media object from LiveStream (non-critical - don't fail conversion if this fails)
                    try 
                    {
                        Media media = createMediaFromLiveStream(stream, durationMS);
                       
                    } 
                    catch (Exception e) 
                    {
                        e.printStackTrace();
                    }
                    
                    // Send notification to influencer
                    User user = stream.getInfulancer();
                    notificationService.sendAndStoreNotification(
                            stream.getId(),
                            NotificationType.LiveConversionCompleted,
                            user,
                            List.of(stream),
                            null,
                            null,
                            NotificationEntityType.LIVE_STREAM,
                            null,
                            null,
                            user.getUsername(),
                            UserType.Influencer);
                } 
                else 
                {
                    stream.setMp4ConversionStatus(Mp4ConversionStatus.FAILED);
                    liveStreamRepository.save(stream);
                    User user = stream.getInfulancer();
                    notificationService.sendAndStoreNotification(
                            stream.getId(),
                            NotificationType.LiveConversionFailed,    // You'd need to add this enum
                            user,
                            List.of(stream),
                            null,
                            null,
                            NotificationEntityType.LIVE_STREAM,
                            null,
                            null,
                            user.getUsername(),
                            UserType.Influencer);
                    }  
            } 
            catch (Exception e) 
            {
                e.printStackTrace();
                // Update stream status to failed
                try
                {
                    LiveStream stream = liveStreamRepository.findById(streamId).orElse(null);
                    if (stream != null) 
                    {
                        stream.setMp4ConversionStatus(Mp4ConversionStatus.FAILED);
                        liveStreamRepository.save(stream);
                    }
                } 
                catch (Exception saveEx) 
                {
                    System.out.println("Failed to update stream status: " + saveEx.getMessage());
                }
            }
        }

       
        /**
         * Extract video duration using FFmpeg probe
         * @param videoFile Path to the video file
         * @return Duration in milliseconds, or null if extraction fails
         */
        private Long extractVideoDurationWithFFmpeg(Path videoFile) 
        {
            try 
            {
                // Try different FFprobe paths (companion tool to FFmpeg)
                String[] ffprobePaths = 
                {
                    "C:\\ffmpeg\\bin\\ffprobe.exe",  // Windows specific path
                    "C:\\ffmpeg\\ffprobe.exe",       // Alternative Windows path
                    "ffprobe",                       // System PATH
                    "/usr/bin/ffprobe",              // Linux/Unix
                    "/usr/local/bin/ffprobe"         // Alternative Linux/Unix
                };
                
                String ffprobePath = null;
                
                // Find available FFprobe
                for (String path : ffprobePaths) 
                {
                    try 
                    {
                        ProcessBuilder testPb = new ProcessBuilder(path, "-version");
                        testPb.redirectErrorStream(true);
                        Process testProcess = testPb.start();
                        int testExitCode = testProcess.waitFor();
                        
                        if (testExitCode == 0) 
                        {
                            ffprobePath = path;
                            System.out.println("Found FFprobe at: " + path);
                            break;
                        }
                    } 
                    catch (Exception e) 
                    {
                        // Try next path
                        continue;
                    }
                }
                
                if (ffprobePath == null) 
                {
                    System.out.println("FFprobe not found - cannot extract duration");
                    return null;
                }
                
                // Use FFprobe to get duration in seconds (with high precision)
                ProcessBuilder pb = new ProcessBuilder(
                    ffprobePath,
                    "-v", "quiet",                           // Suppress verbose output
                    "-show_entries", "format=duration",      // Only show duration
                    "-of", "csv=p=0",                       // Output as CSV without headers
                    videoFile.toString()
                );
                
                System.out.println("🔍 Extracting duration with FFprobe...");
                
                // Execute FFprobe
                pb.redirectErrorStream(true);
                Process process = pb.start();
                
                // Read process output
                StringBuilder output = new StringBuilder();
                try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) 
                {
                    String line;
                    while ((line = reader.readLine()) != null) 
                    {
                        output.append(line.trim());
                    }
                }
                
                int exitCode = process.waitFor();
                
                if (exitCode == 0) 
                {
                    String durationStr = output.toString().trim();
                    
                    if (!durationStr.isEmpty() && !durationStr.equals("N/A")) 
                    {
                        try 
                        {
                            // Parse duration from seconds to milliseconds
                            double durationSeconds = Double.parseDouble(durationStr);
                            long durationMs = Math.round(durationSeconds * 1000);
                            
                            return durationMs;
                        } 
                        catch (NumberFormatException e) 
                        {
                        }
                    } 
                    else 
                    {
                    }
                } 
                
                return null;
                
            } 
            catch (Exception e) 
            {
                e.printStackTrace();
                return null;
            }
        }

        /**
         * Create a new Media object from a LiveStream
         * @param liveStream The LiveStream object to convert to Media
         * @return The created Media object, or null if creation fails
         */
        @Override
        public Media createMediaFromLiveStream(LiveStream liveStream,Long durationMS) 
        {            
            try 
            {
                if (liveStream == null) 
                {
                    return null;
                }
                
                // Create new Media object
                Media media = new Media();
                media.setPrivateMedia(true);
                // Map basic properties
                media.setTitle(liveStream.getTitle());
                media.setDescription(liveStream.getDescription());
                media.setSource("Live Stream Recording");
                
                // Set media type as video (since it's a live stream recording)
                media.setMediaType(MediaType.live);
                
                media.setVideoUrl(liveStream.getMp4Key());
                
                // Set thumbnail URLs
                media.setThumbnailClipUrl(liveStream.getThumbnailClipUrl());
                media.setThumbnailCaptureUrl(liveStream.getThumbnailImageUrl());
                
                // Map user and ownership
                if (liveStream.getInfulancer() != null) 
                {
                    media.setUser(liveStream.getInfulancer());
                    media.setOwnerId(liveStream.getInfulancer().getId());
                }
                media.setLiveStream(liveStream);
                // Map engagement metrics
                media.setNumberOfReactions(liveStream.getNumberOfReactions());
                media.setNumberOfComments(liveStream.getNumberOfComments());
                media.setNumberOfShares(liveStream.getNumberOfShares());
                
                // Map tags
                if (liveStream.getTags() != null) 
                {
                    media.setTags(liveStream.getTags());
                }
                
                // Map package reference
                if (liveStream.getPackageRef() != null) 
                {
                    media.set_package(liveStream.getPackageRef());
                }
                
                // Set employee flag based on user type (assuming influencer is not employee)
                media.setEmployee(false);
                
                // Set video duration safely (avoid division by zero)
                if (durationMS != null && durationMS > 0) 
                {
                    media.setVideoDuration(BigDecimal.valueOf(durationMS / 1000.0));
                    media.setVideoDurationMS(BigDecimal.valueOf(durationMS));
                } 
                else 
                {
                    media.setVideoDuration(BigDecimal.ZERO);
                    media.setVideoDurationMS(BigDecimal.ZERO);
                }
                
                // Set creation date to stream creation time (with safety checks)
                try 
                {
                    if (liveStream.getCreatedAt() != null) 
                    {
                        media.setCreationDate(java.sql.Timestamp.valueOf(liveStream.getCreatedAt()));
                    }
                } 
                catch (Exception e) 
                {
                    System.out.println("⚠️ Warning: Failed to set creation date, using current time: " + e.getMessage());
                    media.setCreationDate(new java.sql.Timestamp(System.currentTimeMillis()));
                }
                
                // Set update date to stream end time or current time (with safety checks)
                try 
                {
                    if (liveStream.getEndedAt() != null) 
                    {
                        media.setLastUpdate(java.sql.Timestamp.valueOf(liveStream.getEndedAt()));
                    } 
                    else 
                    {
                        media.setLastUpdate(new java.sql.Timestamp(System.currentTimeMillis()));
                    }
                }
                catch (Exception e) 
                {
                    System.out.println("⚠️ Warning: Failed to set last update date, using current time: " + e.getMessage());
                    media.setLastUpdate(new java.sql.Timestamp(System.currentTimeMillis()));
                }
                
                // Save the Media object to database
                Media savedMedia = mediaRepository.save(media);
              
                List<com.hb.crm.core.beans.LiveStream.LiveStreamReaction> liveStreamReactions = 
                liveStreamReactionRepository.findAllByLiveStreamId( new ObjectId(liveStream.getId()));
                for(LiveStreamReaction liveStreamReaction : liveStreamReactions)
                {
                    com.hb.crm.core.beans.MediaReaction mediaReaction = new com.hb.crm.core.beans.MediaReaction();
                    mediaReaction.setMedia(savedMedia);
                    mediaReaction.setReactionType(liveStreamReaction.getReactionType());
                    mediaReaction.setUser(liveStreamReaction.getUser());
                    mediaReaction.setCreateDate(liveStreamReaction.getCreateDate());
                    mediaReaction.setUpdateDate(liveStreamReaction.getUpdateDate());
                    mediaReaction.setId(liveStreamReaction.getId());
                    mediaReactionRepository.save(mediaReaction);
                }
                
                return savedMedia;
                
            } 
            catch (Exception e) 
            {
                e.printStackTrace();
                return null;
            }
        }

        /**
         * Check if a channel is currently active and receiving streaming
         * @param channelArn The ARN of the channel to check
         * @return Map containing channel status information
         */
        @Override
        public boolean checkChannelStreamStatus(String channelArn) 
        {
            Map<String, Object> result = new HashMap<>();
            
            
            try 
            {
                // Step 1: Create AWS credentials and IVS client
                BasicAWSCredentials awsCreds = new BasicAWSCredentials(awsAccessKey, awsSecretKey);
                AmazonIVS ivs = AmazonIVSClientBuilder.standard()
                        .withCredentials(new AWSStaticCredentialsProvider(awsCreds))
                        .withRegion(awsRegion)
                        .build();
                // Step 3: Get stream information for this channel
                GetStreamRequest streamRequest = new GetStreamRequest().withChannelArn(channelArn);
                
                try 
                {
                    GetStreamResult streamResult = ivs.getStream(streamRequest);
                    System.out.println("❌ Stream result: " + streamResult);
                    Stream stream = streamResult.getStream();
                    System.out.println("❌ Stream: " + stream);
                    
                    // Stream exists and is active
                    String streamState = stream.getState();
                    System.out.println("❌ Stream state: " + streamState);
                    boolean isLive = "LIVE".equalsIgnoreCase(streamState);
                   
                    System.out.println("❌ Is live: " + isLive);
                    
                return isLive;
                    
                } 
                catch (ResourceNotFoundException e) 
                {
                    System.out.println("❌ Resource not found1: " + e.getMessage());
                    return false;
                }
                                
            } 
            catch (ResourceNotFoundException e) 
            {
              System.out.println("❌ Resource not found: " + e.getMessage());
                return false;
                
            } 
            catch (Exception e) 
            {
                System.out.println("❌ Error checking channel stream status: " + e.getMessage());
                e.printStackTrace();
                return false;
            }
        }

}


