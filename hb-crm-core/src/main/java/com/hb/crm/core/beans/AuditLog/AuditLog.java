package com.hb.crm.core.beans.AuditLog;

import com.fasterxml.jackson.databind.JsonNode;
import com.hb.crm.core.Enums.UserRole;
import com.hb.crm.core.Enums.UserType;
import lombok.Getter;
import lombok.Setter;
import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.mongodb.core.mapping.Document;

import java.time.LocalDateTime;
import java.util.Map;


@Document
@Setter
@Getter
public class AuditLog {

    @Id
    private String id;

    private String entityName;

    private String entityId;

    private String actionType; // e.g., "CREATE", "UPDATE", "DELETE"

    private Map<String, Object> oldValue;

    private Map<String, Object> newValue;

    private String userEmail;

    private String userId;

    private String userName;

    private String ipAddress;

    private UserType userType;

    private UserRole userRole;

    @LastModifiedDate
    private LocalDateTime createdAt;

}
