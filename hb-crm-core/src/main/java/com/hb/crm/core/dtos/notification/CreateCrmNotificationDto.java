package com.hb.crm.core.dtos.notification;

import com.hb.crm.core.Enums.CrmNotificationChannelTypes;
import com.hb.crm.core.beans.Notification.CrmNotificationChannel;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class CreateCrmNotificationDto {


    String title;
    String body;
    String topic;
    String mobileNumber;
    String userId;
    boolean read;
    List<CrmNotificationPayload> payload;
    LocalDateTime sentAt;
    LocalDateTime readDate;

    CrmNotificationChannelTypes channel;
}
