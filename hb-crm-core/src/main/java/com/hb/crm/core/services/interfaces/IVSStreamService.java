package com.hb.crm.core.services.interfaces;

import com.hb.crm.core.dtos.LiveStream.LiveStreamStartResponseDto;
import com.hb.crm.core.beans.Media;
import com.hb.crm.core.beans.LiveStream.LiveStream;
import java.util.Map;

public interface IVSStreamService {
    /**
     * Create a new IVS channel for an influencer
     * @param influencerId The ID of the influencer
     * @return LiveStreamStartResponseDto containing channel details
     */
    LiveStreamStartResponseDto createChannelForInfluencer(String influencerId);
    
    /**
     * Stop streaming and revoke access to the channel
     * @param channelArn The ARN of the channel to stop
     */
    void stopStream(String channelArn);
    
   
    /**
     * Prepare live stream recording for MP4 conversion (without creating job)
     * @param channelArn The ARN of the channel containing the recording
     * @return Map containing conversion info and download URLs
     */
    Map<String, Object> convertLiveStreamToMp4(String channelArn);

    /**
     * Check if MP4 files exist for a channel and return download URL
     * @param channelId Channel ID for constructing output path
     * @return Map containing MP4 file info and download URL if available
     */
    Map<String, Object> checkMp4Status(String channelId);

    /**
     * Start background MP4 conversion job (non-blocking) - updates LiveStream document
     * @param streamId The stream ID to update
     * @param channelArn The ARN of the channel containing the recording
     */
    void convertToMp4Async(String streamId, String channelArn);

    /**
     * Generate pre-signed URL for S3 object access
     * @param key S3 object key
     * @return Pre-signed URL for download
     */
    java.net.URL grantAccessUrl(String key);

    /**
     * Generate 3-second clip from MP4 file in S3 and save back to S3
     * @param mp4Key S3 key of the source MP4 file
     * @param streamId LiveStream ID to update with clip key
     * @return S3 key of the generated clip
     */
    String generateClipFromMp4(String mp4Key, String streamId);

    /**
     * Generate thumbnail image from MP4 file in S3 and save back to S3
     * @param mp4Key S3 key of the source MP4 file
     * @param streamId LiveStream ID to update with thumbnail key
     * @return S3 key of the generated thumbnail
     */
    String generateThumbnailFromMp4(String mp4Key, String streamId);

    /**
     * Upload custom clip for live stream and replace existing clip
     * @param clipFileBytes The clip file as byte array
     * @param originalFileName Original filename for content type detection
     * @param streamId LiveStream ID to update with new clip key
     * @param channelId Channel ID for S3 path construction
     * @return S3 key of the uploaded clip
     */
    String uploadCustomClipForStream(byte[] clipFileBytes, String originalFileName, String streamId, String channelId);

    /**
     * Upload custom thumbnail image for live stream and replace existing thumbnail
     * @param thumbnailFileBytes The thumbnail image file as byte array
     * @param originalFileName Original filename for content type detection
     * @param streamId LiveStream ID to update with new thumbnail key
     * @param channelId Channel ID for S3 path construction
     * @return S3 key of the uploaded thumbnail
     */
    String uploadCustomThumbnailForStream(byte[] thumbnailFileBytes, String originalFileName, String streamId, String channelId);

    /**
     * Create a new Media object from a LiveStream
     * @param liveStream The LiveStream object to convert to Media
     * @return The created Media object, or null if creation fails
     */
    Media createMediaFromLiveStream(LiveStream liveStream,Long durationMS);

    /**
     * Check if a channel is currently active and receiving streaming
     * @param channelArn The ARN of the channel to check
     * @return Map containing channel status information
     */
    boolean checkChannelStreamStatus(String channelArn);

}
