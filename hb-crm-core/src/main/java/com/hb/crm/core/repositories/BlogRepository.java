package com.hb.crm.core.repositories;

import com.hb.crm.core.Enums.BlogStatus;
import com.hb.crm.core.beans.Blog;

import com.hb.crm.core.beans.Employee;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.Aggregation;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Blog Repository - Data access layer for blog operations
 *
 * This repository provides optimized data access methods for blog entities with:
 * - Performance-optimized queries using compound indexes
 * - Full-text search capabilities with MongoDB text indexes
 * - Advanced aggregation pipelines for analytics
 * - Efficient pagination for large datasets
 *
 * Performance Features:
 * - All queries leverage compound indexes for optimal performance
 * - Text search uses weighted indexes for relevance scoring
 * - Aggregation queries are optimized for large datasets
 * - Pagination prevents memory issues with large result sets
 *
 * Index Dependencies:
 * - Compound index: {status: 1, publishedDate: -1}
 * - Compound index: {author: 1, status: 1, publishedDate: -1}
 * - Text index: {title: "text", content: "text", excerpt: "text"}
 *
 * <AUTHOR> CRM Team
 * @version 1.0
 * @since 2024-01-01
 */
@Repository
public interface BlogRepository extends MongoRepository<Blog, String> {

    // Basic queries - NOTE: These should be used carefully as they don't filter soft-deleted blogs
    Optional<Blog> findBySlug(String slug);
    Optional<Blog> findBySlugAndDeletedFalse(String slug);
    boolean existsBySlug(String slug);
    boolean existsBySlugAndDeletedFalse(String slug);
    boolean existsBySlugIgnoreCase(String slug);

    // Status-based queries
    Page<Blog> findByStatusOrderByPublishedDateDesc(BlogStatus status, Pageable pageable);
    Page<Blog> findByStatusAndDeletedFalseOrderByPublishedDateDesc(BlogStatus status, Pageable pageable);

    // Author-based queries
    Page<Blog> findByAuthorAndStatusOrderByUpdatedDateDesc(Employee author, BlogStatus status, Pageable pageable);
    Page<Blog> findByAuthorAndStatusAndDeletedFalseOrderByUpdatedDateDesc(Employee author, BlogStatus status, Pageable pageable);
    Page<Blog> findByAuthorAndDeletedFalseOrderByUpdatedDateDesc(Employee author, Pageable pageable);





    // Date range queries
    Page<Blog> findByStatusAndPublishedDateBetweenOrderByPublishedDateDesc(
        BlogStatus status, LocalDateTime startDate, LocalDateTime endDate, Pageable pageable);
    Page<Blog> findByStatusAndDeletedFalseAndPublishedDateBetweenOrderByPublishedDateDesc(
        BlogStatus status, LocalDateTime startDate, LocalDateTime endDate, Pageable pageable);
    
    // Search queries
    @Query("{ $and: [ " +
           "  { 'status': ?0 }, " +
           "  { 'deleted': false }, " +
           "  { $or: [ " +
           "    { 'title': { $regex: ?1, $options: 'i' } }, " +
           "    { 'content': { $regex: ?1, $options: 'i' } }, " +
           "  ] } " +
           "] }")
    Page<Blog> searchByTitleContentOrExcerpt(BlogStatus status, String searchTerm, Pageable pageable);
    
    // Count queries
    long countByStatusAndDeletedFalse(BlogStatus status);
    long countByAuthorAndStatusAndDeletedFalse(Employee author, BlogStatus status);

    
    // Analytics aggregation - most viewed blogs
    @Aggregation(pipeline = {
        "{ $match: { 'status': 'published', 'deleted': false } }",
        "{ $sort: { 'viewCount': -1, 'publishedDate': -1 } }",
        "{ $limit: ?0 }"
    })
    List<Blog> findMostViewedBlogs(int limit);
    
    // Analytics aggregation - most liked blogs
    @Aggregation(pipeline = {
        "{ $match: { 'status': 'published', 'deleted': false } }",
        "{ $sort: { 'likeCount': -1, 'publishedDate': -1 } }",
        "{ $limit: ?0 }"
    })
    List<Blog> findMostLikedBlogs(int limit);
    
    // Related blogs by tags only
    @Aggregation(pipeline = {
        "{ $match: { " +
        "  'status': 'published', " +
        "  'deleted': false, " +
        "  '_id': { $ne: ?0 }, " +
        "  'tags.$id': { $in: ?1 } " +
        "} }",
        "{ $addFields: { " +
        "  'tagMatches': { $size: { $setIntersection: ['$tags.$id', ?1] } } " +
        "} }",
        "{ $sort: { 'tagMatches': -1, 'publishedDate': -1 } }",
        "{ $limit: ?2 }"
    })
    List<Blog> findRelatedBlogs(String excludeBlogId, List<String> tagIds, int limit);
    

    
    // Recent blogs with view count threshold
    @Query("{ 'status': ?0, 'deleted': false, 'publishedDate': { $gte: ?1 }, 'viewCount': { $gte: ?2 } }")
    Page<Blog> findRecentPopularBlogs(BlogStatus status, LocalDateTime sinceDate, int minViewCount, Pageable pageable);
}
