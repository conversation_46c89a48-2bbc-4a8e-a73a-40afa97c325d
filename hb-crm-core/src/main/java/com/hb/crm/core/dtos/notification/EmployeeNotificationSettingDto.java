package com.hb.crm.core.dtos.notification;

import com.hb.crm.core.beans.Notification.EmployeeNotificationDisableSetting;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class EmployeeNotificationSettingDto {
    private String id;
    private String employeeId;
    private boolean enableAll = true;
    private boolean enablePushNotification = true;
    private boolean enableInAppNotification = true;
    private boolean enableWhatsAppNotification = true;
    private boolean enableSmsAppNotification = true;
    private boolean enableEmailNotification = true;
    private List<EmployeeNotificationDisableSetting> disabledNotifications;
}
