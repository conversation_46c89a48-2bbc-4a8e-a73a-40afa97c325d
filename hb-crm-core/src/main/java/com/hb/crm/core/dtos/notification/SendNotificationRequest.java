package com.hb.crm.core.dtos.notification;

import com.google.firebase.database.annotations.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


import java.util.Map;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class SendNotificationRequest {

    @NotNull("Device token is required")
    private String token;

    @NotNull("Notification title is required")
    private String title;

    @NotNull("Notification body is required")
    private String body;

    private Map<String, String> payload;
}