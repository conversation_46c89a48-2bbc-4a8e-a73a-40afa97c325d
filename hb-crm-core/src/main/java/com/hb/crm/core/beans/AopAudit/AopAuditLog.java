package com.hb.crm.core.beans.AopAudit;

import com.hb.crm.core.Enums.AuditAction;
import com.hb.crm.core.Enums.AuditEntityType;
import com.hb.crm.core.Enums.AuditStatus;
import com.hb.crm.core.beans.AuditLog.SkipAudit;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * Entity representing an audit log entry
 * Tracks all operations performed in the system
 */
@Getter
@Setter
@NoArgsConstructor
@Document
public class AopAuditLog implements SkipAudit {

    @Id
    private String id;

    /**
     * Timestamp when the audit event occurred
     */
    @CreatedDate
    @Indexed
    private LocalDateTime timestamp;

    /**
     * Type of action performed (CREATE, UPDATE, DELETE, etc.)
     */
    @Indexed
    private AuditAction action;

    /**
     * Type of entity being audited
     */
    @Indexed
    private AuditEntityType entityType;

    /**
     * ID of the entity being audited
     */
    @Indexed
    private String entityId;

    /**
     * Name or identifier of the entity (for display purposes)
     */
    private String entityName;

    /**
     * ID of the user who performed the action
     */
    @Indexed
    private String userId;

    /**
     * Username of the user who performed the action
     */
    @Indexed
    private String username;

    /**
     * Type of user (User, Employee, etc.)
     */
    private String userType;

    /**
     * ID of the employee if action was performed by an employee
     */
    @Indexed
    private String employeeId;

    /**
     * Status of the operation (SUCCESS, FAILED, etc.)
     */
    @Indexed
    private AuditStatus status;

    /**
     * IP address from which the action was performed
     */
    @Indexed
    private String ipAddress;

    /**
     * User agent string from the request
     */
    private String userAgent;

    /**
     * Session ID
     */
    @Indexed
    private String sessionId;

    /**
     * Request ID for tracing
     */
    @Indexed
    private String requestId;

    /**
     * HTTP method used (GET, POST, PUT, DELETE, etc.)
     */
    private String httpMethod;

    /**
     * URL/endpoint that was accessed
     */
    private String endpoint;

    /**
     * Method name that was executed
     */
    private String methodName;

    /**
     * Class name where the method was executed
     */
    private String className;

    /**
     * Module or service name
     */
    @Indexed
    private String moduleName;

    /**
     * Description of the action performed
     */
    private String description;

    /**
     * Additional details about the operation
     */
    private String details;

    /**
     * Error message if the operation failed
     */
    private String errorMessage;

    /**
     * Stack trace if an exception occurred
     */
    private String stackTrace;

    /**
     * Duration of the operation in milliseconds
     */
    private Long duration;

    /**
     * Data before the change (for UPDATE operations)
     */
    private Map<String, Object> oldValues;

    /**
     * Data after the change (for CREATE/UPDATE operations)
     */
    private Map<String, Object> newValues;

    /**
     * Changed fields (for UPDATE operations)
     */
    private Map<String, Object> changedFields;

    /**
     * Request parameters
     */
    private Map<String, Object> requestParameters;

    /**
     * Response data (if applicable and not sensitive)
     */
    private Map<String, Object> responseData;

    /**
     * Additional metadata
     */
    private Map<String, Object> metadata;

    /**
     * Tags for categorization and filtering
     */
    private java.util.List<String> tags;

    /**
     * Risk level of the operation (LOW, MEDIUM, HIGH, CRITICAL)
     */
    private String riskLevel;

    /**
     * Whether this audit entry contains sensitive data
     */
    private Boolean containsSensitiveData;

    /**
     * Retention period for this audit entry (in days)
     */
    private Integer retentionDays;

    /**
     * Whether this entry has been archived
     */
    private Boolean archived;

    /**
     * Date when this entry was archived
     */
    private LocalDateTime archivedDate;

    /**
     * Correlation ID for grouping related audit entries
     */
    @Indexed
    private String correlationId;

    /**
     * Parent audit log ID (for nested operations)
     */
    @Indexed
    private String parentAuditId;

    /**
     * Business transaction ID
     */
    @Indexed
    private String transactionId;

    /**
     * Application version when the audit was created
     */
    private String applicationVersion;

    /**
     * Environment where the action was performed (DEV, TEST, PROD)
     */
    @Indexed
    private String environment;

    /**
     * Tenant ID for multi-tenant applications
     */
    @Indexed
    private String tenantId;

    /**
     * Geographic location where the action was performed
     */
    private String location;

    /**
     * Device information
     */
    private String deviceInfo;

    /**
     * Browser information
     */
    private String browserInfo;

    /**
     * Operating system information
     */
    private String osInfo;

    /**
     * Whether this action was performed by an automated system
     */
    private Boolean automated;

    /**
     * Source system or service that triggered this action
     */
    private String sourceSystem;

    /**
     * External reference ID
     */
    private String externalReferenceId;

    /**
     * Compliance flags
     */
    private java.util.List<String> complianceFlags;

    /**
     * Data classification level
     */
    private String dataClassification;

    /**
     * Whether this entry requires approval
     */
    private Boolean requiresApproval;

    /**
     * Approval status
     */
    private String approvalStatus;

    /**
     * ID of the user who approved this entry
     */
    private String approvedBy;

    /**
     * Date when this entry was approved
     */
    private LocalDateTime approvedDate;

    /**
     * Constructor for basic audit log creation
     */
    public AopAuditLog(AuditAction action, AuditEntityType entityType, String entityId, String userId) {
        this.action = action;
        this.entityType = entityType;
        this.entityId = entityId;
        this.userId = userId;
        this.timestamp = LocalDateTime.now();
        this.status = AuditStatus.SUCCESS;
        this.archived = false;
        this.containsSensitiveData = false;
        this.automated = false;
        this.requiresApproval = false;
    }

    /**
     * Constructor with status
     */
    public AopAuditLog(AuditAction action, AuditEntityType entityType, String entityId, String userId, AuditStatus status) {
        this(action, entityType, entityId, userId);
        this.status = status;
    }

    /**
     * Add metadata entry
     */
    public void addMetadata(String key, Object value) {
        if (this.metadata == null) {
            this.metadata = new HashMap<>();
        }
        this.metadata.put(key, value);
    }

    /**
     * Get metadata value
     */
    public Object getMetadata(String key) {
        return this.metadata != null ? this.metadata.get(key) : null;
    }

    /**
     * Remove metadata entry
     */
    public void removeMetadata(String key) {
        if (this.metadata != null) {
            this.metadata.remove(key);
        }
    }
}
