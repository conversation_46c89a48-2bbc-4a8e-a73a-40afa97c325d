package com.hb.crm.core.util;

import com.hb.crm.core.Enums.DisableNotificationType;
import com.hb.crm.core.Enums.NotificationChannelType;
import com.hb.crm.core.Enums.NotificationType;
import com.hb.crm.core.beans.Notification.NotificationDisableSetting;
import com.hb.crm.core.beans.Notification.NotificationMute;
import com.hb.crm.core.beans.Notification.NotificationMuteUser;
import com.hb.crm.core.beans.Notification.NotificationSetting;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * Utility class for validating notification settings and determining if notifications should be sent.
 * <p>
 * This class provides static methods to check if specific notification types are disabled
 * based on user notification settings. It uses the mapping defined in Constants.java to
 * group notification types into categories that match the DisableNotificationType enum values.
 * 
 * <AUTHOR> CRM Team
 * @see Constants for notification type groupings
 * @see DisableNotificationType for available disable categories
 * @see NotificationSetting for user notification preferences
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class NotificationValidationUtil {

    /**
     * Comprehensive validation method that checks if a notification should be blocked for a specific channel.
     *
     * <p>
     * This method combines multiple validation checks:
     * 1. Checks if the notification type is disabled for the specific channel
     * 2. Check if the channel is globally disabled by user
     * 3. Optionally, checks if the specific entity is muted
     * <p>
     *
     * @param notificationType The notification type to validate (must not be null)
     * @param channelType The notification channel type to check (must not be null)
     * @param notificationSetting The user's notification settings (can be null)
     * @param notificationMute The notification mute settings for the entity (can be null)
     * @return true if the notification should be blocked for this channel, false otherwise
     *
     * @throws IllegalArgumentException if notificationType or channelType is null
     */
    public static boolean isNotificationBlockedForChannel(NotificationType notificationType,
                                                          NotificationChannelType channelType,
                                                          NotificationSetting notificationSetting,
                                                          NotificationMute notificationMute,
                                                          NotificationMuteUser notificationMuteUser) {
        if (notificationType == null)
            throw new IllegalArgumentException("NotificationType cannot be null");

        if (channelType == null)
            throw new IllegalArgumentException("NotificationChannelType cannot be null");

        if (notificationSetting == null)
            return false;

        // Check if the notification type is disabled for this specific channel
        if (notificationSetting.getDisabledNotifications() != null &&
                isNotificationTypeDisabledForChannel(notificationType, channelType, notificationSetting.getDisabledNotifications()))
            return true;

        // check if the channel is globally disabled
        if(isChannelBlocked(channelType, notificationSetting))
            return true;

        // Check if the channel is muted for the entity
        if(notificationMute != null)
            return isEntityMutedForChannel(channelType, notificationMute);

        if(notificationMuteUser != null)
            return isUserMutedForTypeForChannel(channelType, notificationType, notificationMuteUser);

        return false;
    }

    /**
     * Helper method to check if a notification type belongs to a specific disabled category.
     *
     * @param notificationType The notification type to check
     * @param disabledCategory The disabled notification category
     * @return true if the notification type belongs to the disabled category
     */
    private static boolean isNotificationTypeInCategory(NotificationType notificationType,
                                                        DisableNotificationType disabledCategory) {

        return switch (disabledCategory) {
            case PACKAGE_NOTIFICATIONS -> Constants.PACKAGE_NOTIFICATIONS.contains(notificationType);
            case PACKAGE_NOTIFICATIONS_TWM -> Constants.PACKAGE_NOTIFICATIONS_TWM.contains(notificationType);
            case PACKAGE_NOTIFICATIONS_FOLLOW_ME -> Constants.PACKAGE_NOTIFICATIONS_FOLLOW_ME.contains(notificationType);

            case POST_STORY_NOTIFICATIONS -> Constants.POST_STORY_NOTIFICATIONS.contains(notificationType);
            case POST_NOTIFICATIONS -> Constants.POST_NOTIFICATIONS.contains(notificationType);
            case STORY_NOTIFICATIONS -> Constants.STORY_NOTIFICATIONS.contains(notificationType);
            case REEL_NOTIFICATIONS -> Constants.REEL_NOTIFICATIONS.contains(notificationType);
            case LIVESTREAMS_NOTIFICATIONS -> Constants.LIVESTREAMS_NOTIFICATIONS.contains(notificationType);

            case CHAT_NOTIFICATIONS -> Constants.CHAT_NOTIFICATIONS.contains(notificationType);
            case SUPPORT_CHAT_NOTIFICATIONS -> Constants.SUPPORT_CHAT_NOTIFICATIONS.contains(notificationType);
            case GROUP_CHAT_NOTIFICATIONS -> Constants.GROUP_CHAT_NOTIFICATIONS.contains(notificationType);

            case INTERACTION_NOTIFICATIONS -> Constants.INTERACTION_NOTIFICATIONS.contains(notificationType);
            case REACT_NOTIFICATIONS -> Constants.REACT_NOTIFICATIONS.contains(notificationType);
            case COMMENT_NOTIFICATIONS -> Constants.COMMENT_NOTIFICATIONS.contains(notificationType);

            case ACTION_NOTIFICATIONS -> Constants.ACTION_NOTIFICATIONS.contains(notificationType);
            case ADMIN_REGISTER_NOTIFICATIONS -> Constants.ADMIN_REGISTER_NOTIFICATIONS.contains(notificationType);
        };
    }

    /**
     * Validates if a notification type is disabled for a specific channel based on the user's notification settings.
     * <p>
     * This method checks if the given notification type belongs to any of the disabled
     * notification categories in the user's settings for the specified channel.
     *
     * @param notificationType The notification type to validate (must not be null)
     * @param channelType The notification channel type to check (must not be null)
     * @param notificationDisableSettings The user's notification settings containing disabled categories
     * @return true if the notification type is disabled for the specified channel, false otherwise
     *
     * @throws IllegalArgumentException if notificationType or channelType is null
     */
    public static boolean isNotificationTypeDisabledForChannel(NotificationType notificationType,
                                                               NotificationChannelType channelType,
                                                               List<NotificationDisableSetting> notificationDisableSettings) {

        if (notificationType == null) {
            throw new IllegalArgumentException("NotificationType cannot be null");
        }

        if (channelType == null) {
            throw new IllegalArgumentException("NotificationChannelType cannot be null");
        }

        // Check each disabled notification setting
        for (NotificationDisableSetting disableSetting : notificationDisableSettings) {
            // Check if this notification type belongs to the disabled category
            if (isNotificationTypeInCategory(notificationType, disableSetting.getDisableNotificationType())) {
                // Check if the channel is disabled for this category
                if (disableSetting.getChannelTypes() != null &&
                        disableSetting.getChannelTypes().contains(channelType)) {
                    return true;
                }
            }
        }

        return false;
    }


    /**
     * Validates if a specific user is muted for a given channel and type.
     *
     * @param channel The notification channel type to check (must not be null)
     * @param notificationType The notification type to validate (must not be null)
     * @param notificationMuteUser The notification mute settings for the user
     * @return true if the entity is muted for the channel, false otherwise
     */
    private static boolean isUserMutedForTypeForChannel(NotificationChannelType channel,
                                                        NotificationType notificationType,
                                                        NotificationMuteUser notificationMuteUser) {
        if(notificationMuteUser != null && notificationMuteUser.getMuteDisableSetting() != null)
            return isNotificationTypeDisabledForChannel(notificationType, channel, notificationMuteUser.getMuteDisableSetting());

        return false;
    }

    /**
     * Validates if a specific entity is muted for a given channel.
     *
     * @param channel The notification channel to check
     * @param notificationMute The notification mute settings for the entity
     * @return true if the entity is muted for the channel, false otherwise
     */
    private static boolean isEntityMutedForChannel(NotificationChannelType channel, NotificationMute notificationMute) {
        return notificationMute.getChannels().contains(channel);
    }

    /**
     * Validates the user setting blocks if he blocked the channel that sends the notification through.
     *
     * @param channelType The notification channel type to categorize
     * @param notificationSetting The setting of the user
     * @return true if the channel is blocked from the user, false otherwise
     */
    public static boolean isChannelBlocked(NotificationChannelType channelType, NotificationSetting notificationSetting) {
        return switch (channelType) {
            case Email -> !notificationSetting.isEnableEmailNotification();
            case Sms -> !notificationSetting.isEnableSmsAppNotification();
            case WhatsApp -> !notificationSetting.isEnableWhatsAppNotification();
            case Push -> !notificationSetting.isEnablePushNotification();
            case InApp -> !notificationSetting.isEnableInAppNotification();
        };
    }
}
