package com.hb.crm.core.services.interfaces;

import com.hb.crm.core.Enums.ReactionType;
import com.hb.crm.core.beans.User;
import com.hb.crm.core.beans.LiveStream.LiveStream;
import com.hb.crm.core.beans.LiveStream.LiveStreamReaction;
import com.hb.crm.core.beans.LiveStream.LiveStreamComment;
import com.hb.crm.core.dtos.PageDto;
import com.hb.crm.core.dtos.LiveStream.LiveStreamCreateRequestDto;
import com.hb.crm.core.dtos.LiveStream.LiveStreamDto;
import com.hb.crm.core.dtos.LiveStream.LiveStreamReactionDto;
import com.hb.crm.core.dtos.LiveStream.LiveStreamReactionWithUserDto;
import com.hb.crm.core.dtos.LiveStream.LiveStreamCommentDto;

public interface LiveStreamService 
{
    LiveStream startLiveStream(LiveStreamCreateRequestDto request,User influencer);
    LiveStream stopLiveStream(LiveStream stream);
    PageDto<LiveStream> getLiveStreams(String influencerId, String packageId,  int page, int size);
    //String getDownloadUrl(String streamId);
    LiveStreamReaction reactToLiveStream(String liveStreamId, User user, ReactionType reactionType);
    void removeReactionFromLiveStream(String liveStreamId, User user);
    PageDto<LiveStreamReactionDto> getLiveStreamReactions(String liveStreamId, int page, int size);
    PageDto<LiveStreamReactionWithUserDto> getLiveStreamReactionsWithUserData(String liveStreamId, int page, int size);
    
    // Comment methods
    LiveStreamComment addCommentToLiveStream(String liveStreamId, User user, String comment);
    void removeCommentFromLiveStream(String commentId, User user);
    PageDto<LiveStreamCommentDto> getLiveStreamComments(String liveStreamId, int page, int size);
    LiveStreamDto getLiveStreamById(String liveStreamId);
    LiveStream cancelLiveStream(LiveStream stream);
    
    // Share methods
    boolean shareLiveStream(String liveStreamId, User user);
    int getCurrentShareCount(String liveStreamId);
    
}