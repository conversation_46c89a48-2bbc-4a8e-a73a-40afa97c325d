package com.hb.crm.core.Enums;

import io.swagger.v3.oas.annotations.media.Schema;

@Schema(example = "Post", description = "Type of reaction (e.g., Post, Package,FollowPackage, Comment, Media, User, Blog)")

public enum EntityName {
    @Schema(description = "Post")
    Post,
    @Schema(description = "Package")
    Package,
    @Schema(description = "FollowPackage")
    FollowPackage,
    @Schema(description = "Comment")
    Comment,
    @Schema(description = "Media")
    Media,
    @Schema(description = "User")
    User,
    @Schema(description = "Blog")
    Blog
}
