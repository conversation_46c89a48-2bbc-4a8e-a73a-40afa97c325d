package com.hb.crm.core.Enums;

/**
 * Enum representing different types of audit actions
 */
public enum AuditAction {
    CREATE("CREATE", "Entity created"),
    UPDATE("UPDATE", "Entity updated"),
    DELETE("DELETE", "Entity deleted"),
    READ("READ", "Entity read/accessed"),
    LOGIN("LOGIN", "User login"),
    LOGOUT("LOGOUT", "User logout"),
    EXPORT("EXPORT", "Data exported"),
    IMPORT("IMPORT", "Data imported"),
    APPROVE("APPROVE", "Entity approved"),
    REJECT("REJECT", "Entity rejected"),
    PUBLISH("PUBLISH", "Entity published"),
    ARCHIVE("ARCHIVE", "Entity archived"),
    RESTORE("RESTORE", "Entity restored"),
    ASSIGN("ASSIGN", "Entity assigned"),
    UNASSIGN("UNASSIGN", "Entity unassigned"),
    ACTIVATE("ACTIVATE", "Entity activated"),
    DEACTIVATE("DEACTIVATE", "Entity deactivated"),
    UPLOAD("UPLOAD", "File uploaded"),
    DOWNLOAD("DOWNLOAD", "File downloaded"),
    SHARE("SHARE", "Entity shared"),
    SUBSCRIBE("SUBSCRIBE", "Subscription created"),
    UNSUBSCRIBE("UNSUBSCRIBE", "Subscription cancelled"),
    PAYMENT("PAYMENT", "Payment processed"),
    REFUND("REFUND", "Payment refunded"),
    NOTIFICATION_SENT("NOTIFICATION_SENT", "Notification sent"),
    EMAIL_SENT("EMAIL_SENT", "Email sent"),
    SMS_SENT("SMS_SENT", "SMS sent"),
    SEARCH("SEARCH", "Search performed"),
    FILTER("FILTER", "Filter applied"),
    SORT("SORT", "Sort applied"),
    BULK_UPDATE("BULK_UPDATE", "Bulk update performed"),
    BULK_DELETE("BULK_DELETE", "Bulk delete performed"),
    CONFIGURATION_CHANGE("CONFIGURATION_CHANGE", "Configuration changed"),
    PERMISSION_CHANGE("PERMISSION_CHANGE", "Permission changed"),
    ROLE_CHANGE("ROLE_CHANGE", "Role changed"),
    PASSWORD_CHANGE("PASSWORD_CHANGE", "Password changed"),
    PROFILE_UPDATE("PROFILE_UPDATE", "Profile updated"),
    SECURITY_EVENT("SECURITY_EVENT", "Security event occurred"),
    ERROR("ERROR", "Error occurred"),
    WARNING("WARNING", "Warning occurred"),
    INFO("INFO", "Information logged"),
    DEBUG("DEBUG", "Debug information logged"),
    CUSTOM("CUSTOM", "Custom action");

    private final String code;
    private final String description;

    AuditAction(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    @Override
    public String toString() {
        return code;
    }
}
