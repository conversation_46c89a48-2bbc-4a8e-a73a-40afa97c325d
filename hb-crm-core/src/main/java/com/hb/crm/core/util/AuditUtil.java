package com.hb.crm.core.util;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.hb.crm.core.Enums.AuditAction;
import com.hb.crm.core.Enums.AuditEntityType;
import com.hb.crm.core.beans.AopAudit.AuditContext;
import com.hb.crm.core.beans.AopAudit.AopAuditLog;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.mongodb.core.mapping.Document;

import java.lang.reflect.Field;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.regex.Pattern;

/**
 * Utility class for audit trail operations
 * Provides helper methods for audit log creation, data extraction, and formatting
 */
public class AuditUtil {

    private static final Logger logger = LoggerFactory.getLogger(AuditUtil.class);
    private static final ObjectMapper objectMapper = new ObjectMapper();

    // Sensitive field patterns
    private static final Set<String> SENSITIVE_FIELD_NAMES = Set.of(
        "password", "token", "secret", "key", "credential", "authorization",
        "ssn", "creditcard", "cvv", "pin", "otp", "privatekey", "apikey",
        "accesstoken", "refreshtoken", "sessiontoken", "authtoken"
    );

    private static final Set<Pattern> SENSITIVE_FIELD_PATTERNS = Set.of(
        Pattern.compile(".*password.*", Pattern.CASE_INSENSITIVE),
        Pattern.compile(".*token.*", Pattern.CASE_INSENSITIVE),
        Pattern.compile(".*secret.*", Pattern.CASE_INSENSITIVE),
        Pattern.compile(".*key.*", Pattern.CASE_INSENSITIVE),
        Pattern.compile(".*credential.*", Pattern.CASE_INSENSITIVE),
        Pattern.compile(".*auth.*", Pattern.CASE_INSENSITIVE)
    );

    // Entity ID field names to look for
    private static final List<String> ID_FIELD_NAMES = Arrays.asList("id", "_id", "uuid", "identifier");

    // Entity name field names to look for
    private static final List<String> NAME_FIELD_NAMES = Arrays.asList(
        "name", "title", "username", "email", "displayName", "fullName", "label"
    );

    /**
     * Create a basic audit log entry
     */
    public static AopAuditLog createAuditLog(AuditAction action, Object entity, String userId) {
        AuditEntityType entityType = determineEntityType(entity);
        String entityId = extractEntityId(entity);
        
        AopAuditLog auditLog = new AopAuditLog(action, entityType, entityId, userId);
        
        // Set entity name
        String entityName = extractEntityName(entity);
        if (entityName != null) {
            auditLog.setEntityName(entityName);
        }
        
        // Populate from current context if available
        populateFromContext(auditLog);
        
        return auditLog;
    }

    /**
     * Create audit log with old and new values for update operations
     */
    public static AopAuditLog createUpdateAuditLog(Object oldEntity, Object newEntity, String userId) {
        AopAuditLog auditLog = createAuditLog(AuditAction.UPDATE, newEntity, userId);
        
        try {
            // Extract old and new values
            Map<String, Object> oldValues = extractEntityValues(oldEntity);
            Map<String, Object> newValues = extractEntityValues(newEntity);
            Map<String, Object> changedFields = findChangedFields(oldValues, newValues);
            
            auditLog.setOldValues(sanitizeValues(oldValues));
            auditLog.setNewValues(sanitizeValues(newValues));
            auditLog.setChangedFields(sanitizeValues(changedFields));
            
            // Set description based on changed fields
            if (!changedFields.isEmpty()) {
                auditLog.setDescription("Updated " + changedFields.size() + " field(s): " + 
                    String.join(", ", changedFields.keySet()));
            }
            
        } catch (Exception e) {
            logger.debug("Failed to extract entity values for audit", e);
        }
        
        return auditLog;
    }

    /**
     * Determine entity type from object
     */
    public static AuditEntityType determineEntityType(Object entity) {
        if (entity == null) {
            return AuditEntityType.CUSTOM;
        }
        
        return AuditEntityType.fromClassName(entity.getClass().getName());
    }

    /**
     * Extract entity ID using reflection
     */
    public static String extractEntityId(Object entity) {
        if (entity == null) {
            return null;
        }
        
        try {
            // First, try to find @Id annotated field
            Field[] fields = entity.getClass().getDeclaredFields();
            for (Field field : fields) {
                if (field.isAnnotationPresent(org.springframework.data.annotation.Id.class)) {
                    field.setAccessible(true);
                    Object value = field.get(entity);
                    return value != null ? value.toString() : null;
                }
            }
            
            // Then try common ID field names
            for (String idFieldName : ID_FIELD_NAMES) {
                try {
                    Field field = entity.getClass().getDeclaredField(idFieldName);
                    field.setAccessible(true);
                    Object value = field.get(entity);
                    if (value != null) {
                        return value.toString();
                    }
                } catch (NoSuchFieldException ignored) {
                    // Continue to next field name
                }
            }
            
        } catch (Exception e) {
            logger.debug("Failed to extract entity ID", e);
        }
        
        return null;
    }

    /**
     * Extract entity name using reflection
     */
    public static String extractEntityName(Object entity) {
        if (entity == null) {
            return null;
        }
        
        try {
            for (String nameFieldName : NAME_FIELD_NAMES) {
                try {
                    Field field = entity.getClass().getDeclaredField(nameFieldName);
                    field.setAccessible(true);
                    Object value = field.get(entity);
                    if (value != null && !value.toString().trim().isEmpty()) {
                        return value.toString();
                    }
                } catch (NoSuchFieldException ignored) {
                    // Continue to next field name
                }
            }
        } catch (Exception e) {
            logger.debug("Failed to extract entity name", e);
        }
        
        return null;
    }

    /**
     * Extract all field values from an entity
     */
    public static Map<String, Object> extractEntityValues(Object entity) {
        Map<String, Object> values = new HashMap<>();
        
        if (entity == null) {
            return values;
        }
        
        try {
            Field[] fields = entity.getClass().getDeclaredFields();
            for (Field field : fields) {
                // Skip static and transient fields
                if (java.lang.reflect.Modifier.isStatic(field.getModifiers()) ||
                    java.lang.reflect.Modifier.isTransient(field.getModifiers())) {
                    continue;
                }
                
                field.setAccessible(true);
                Object value = field.get(entity);
                
                if (value != null) {
                    values.put(field.getName(), value);
                }
            }
        } catch (Exception e) {
            logger.debug("Failed to extract entity values", e);
        }
        
        return values;
    }

    /**
     * Find changed fields between old and new values
     */
    public static Map<String, Object> findChangedFields(Map<String, Object> oldValues, Map<String, Object> newValues) {
        Map<String, Object> changedFields = new HashMap<>();
        
        // Check for new or changed fields
        for (Map.Entry<String, Object> entry : newValues.entrySet()) {
            String fieldName = entry.getKey();
            Object newValue = entry.getValue();
            Object oldValue = oldValues.get(fieldName);
            
            if (!Objects.equals(oldValue, newValue)) {
                Map<String, Object> change = new HashMap<>();
                change.put("old", oldValue);
                change.put("new", newValue);
                changedFields.put(fieldName, change);
            }
        }
        
        // Check for removed fields
        for (Map.Entry<String, Object> entry : oldValues.entrySet()) {
            String fieldName = entry.getKey();
            if (!newValues.containsKey(fieldName)) {
                Map<String, Object> change = new HashMap<>();
                change.put("old", entry.getValue());
                change.put("new", null);
                changedFields.put(fieldName, change);
            }
        }
        
        return changedFields;
    }

    /**
     * Sanitize values to remove sensitive information
     */
    public static Map<String, Object> sanitizeValues(Map<String, Object> values) {
        if (values == null) {
            return null;
        }
        
        Map<String, Object> sanitized = new HashMap<>();
        
        for (Map.Entry<String, Object> entry : values.entrySet()) {
            String fieldName = entry.getKey();
            Object value = entry.getValue();
            
            if (isSensitiveField(fieldName)) {
                sanitized.put(fieldName, "[REDACTED]");
            } else {
                sanitized.put(fieldName, sanitizeValue(value));
            }
        }
        
        return sanitized;
    }

    /**
     * Sanitize a single value
     */
    public static Object sanitizeValue(Object value) {
        if (value == null) {
            return null;
        }
        
        try {
            // For complex objects, create a simplified representation
            if (isComplexObject(value)) {
                Map<String, Object> simplified = new HashMap<>();
                simplified.put("class", value.getClass().getSimpleName());
                
                // Try to get ID and name
                String id = extractEntityId(value);
                String name = extractEntityName(value);
                
                if (id != null) simplified.put("id", id);
                if (name != null) simplified.put("name", name);
                
                return simplified;
            }
            
            // For simple values, check size
            String stringValue = objectMapper.writeValueAsString(value);
            if (stringValue.length() > 1000) {
                return "[DATA_TOO_LARGE]";
            }
            
            return value;
            
        } catch (Exception e) {
            logger.debug("Failed to sanitize value", e);
            return "[SERIALIZATION_ERROR]";
        }
    }

    /**
     * Check if field name indicates sensitive data
     */
    public static boolean isSensitiveField(String fieldName) {
        if (fieldName == null) {
            return false;
        }
        
        String lowerFieldName = fieldName.toLowerCase();
        
        // Check exact matches
        if (SENSITIVE_FIELD_NAMES.contains(lowerFieldName)) {
            return true;
        }
        
        // Check pattern matches
        for (Pattern pattern : SENSITIVE_FIELD_PATTERNS) {
            if (pattern.matcher(fieldName).matches()) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * Check if object is a complex entity
     */
    public static boolean isComplexObject(Object obj) {
        if (obj == null) {
            return false;
        }
        
        Class<?> clazz = obj.getClass();
        
        // Check if it's a MongoDB document
        if (clazz.isAnnotationPresent(Document.class)) {
            return true;
        }
        
        // Check if it's in a beans package
        String packageName = clazz.getPackage() != null ? clazz.getPackage().getName() : "";
        if (packageName.contains(".beans") || packageName.contains(".entity") || packageName.contains(".model")) {
            return true;
        }
        
        // Check if it's a collection or map
        if (obj instanceof Collection || obj instanceof Map) {
            return false;
        }
        
        // Check if it's a primitive wrapper or string
        if (clazz.isPrimitive() || 
            clazz == String.class || 
            clazz == Integer.class || 
            clazz == Long.class || 
            clazz == Double.class || 
            clazz == Float.class || 
            clazz == Boolean.class || 
            clazz == Character.class || 
            clazz == Byte.class || 
            clazz == Short.class ||
            clazz.isEnum() ||
            java.time.temporal.Temporal.class.isAssignableFrom(clazz)) {
            return false;
        }
        
        return true;
    }

    /**
     * Populate audit log from current context
     */
    public static void populateFromContext(AopAuditLog auditLog) {
        AuditContext context = AuditContext.getCurrentContext();
        if (context != null) {
            auditLog.setRequestId(context.getRequestId());
            auditLog.setSessionId(context.getSessionId());
            auditLog.setCorrelationId(context.getCorrelationId());
            auditLog.setTransactionId(context.getTransactionId());
            auditLog.setIpAddress(context.getIpAddress());
            auditLog.setUserAgent(context.getUserAgent());
            auditLog.setHttpMethod(context.getHttpMethod());
            auditLog.setEndpoint(context.getEndpoint());
            auditLog.setModuleName(context.getModuleName());
            auditLog.setEnvironment(context.getEnvironment());
            auditLog.setTenantId(context.getTenantId());
            auditLog.setDeviceInfo(context.getDeviceInfo());
            auditLog.setBrowserInfo(context.getBrowserInfo());
            auditLog.setOsInfo(context.getOsInfo());
            auditLog.setLocation(context.getLocation());
            auditLog.setSourceSystem(context.getSourceSystem());
            auditLog.setAutomated(context.getAutomated());
            auditLog.setMetadata(context.getMetadata());
            auditLog.setRequestParameters(context.getRequestParameters());
        }
    }

    /**
     * Format audit log for display
     */
    public static String formatAuditLogForDisplay(AopAuditLog auditLog) {
        if (auditLog == null) {
            return "N/A";
        }
        
        StringBuilder sb = new StringBuilder();
        
        // Timestamp
        if (auditLog.getTimestamp() != null) {
            sb.append(auditLog.getTimestamp().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        }
        
        // User
        if (auditLog.getUsername() != null) {
            sb.append(" | User: ").append(auditLog.getUsername());
        } else if (auditLog.getUserId() != null) {
            sb.append(" | User: ").append(auditLog.getUserId());
        }
        
        // Action
        if (auditLog.getAction() != null) {
            sb.append(" | Action: ").append(auditLog.getAction());
        }
        
        // Entity
        if (auditLog.getEntityType() != null) {
            sb.append(" | Entity: ").append(auditLog.getEntityType());
            if (auditLog.getEntityName() != null) {
                sb.append(" (").append(auditLog.getEntityName()).append(")");
            } else if (auditLog.getEntityId() != null) {
                sb.append(" (").append(auditLog.getEntityId()).append(")");
            }
        }
        
        // Status
        if (auditLog.getStatus() != null) {
            sb.append(" | Status: ").append(auditLog.getStatus());
        }
        
        // Duration
        if (auditLog.getDuration() != null) {
            sb.append(" | Duration: ").append(auditLog.getDuration()).append("ms");
        }
        
        return sb.toString();
    }

    /**
     * Generate correlation ID
     */
    public static String generateCorrelationId() {
        return UUID.randomUUID().toString();
    }

    /**
     * Generate transaction ID
     */
    public static String generateTransactionId() {
        return "TXN-" + System.currentTimeMillis() + "-" + UUID.randomUUID().toString().substring(0, 8);
    }

    /**
     * Check if audit logging should be skipped for this object
     */
    public static boolean shouldSkipAudit(Object obj) {
        if (obj == null) {
            return true;
        }
        
        String className = obj.getClass().getName();
        
        // Skip audit-related classes to avoid infinite loops
        return className.startsWith("com.hb.crm.core.beans.Audit") ||
               className.startsWith("com.hb.crm.core.services.Audit") ||
               className.startsWith("com.hb.crm.core.config.Audit");
    }
}
