package com.hb.crm.core.dtos;

import com.hb.crm.core.Enums.Gender;
import com.hb.crm.core.Enums.chat.AgentConversationStatus;
import com.hb.crm.core.beans.Role;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.List;

@Setter
@Getter
public class EmployeeDto {

    private String id;
    private LocalDateTime creationDate;
    private LocalDateTime updatedDate;
    private String username;
    private String fullName;
    private String mobile;
    private String profilePicture;
    private String email;
    private List<Role> roles;
    private int failedLoginAttempts = 0;
    private boolean accountLocked = false;
    private String reasonForLockedAccount;
    private String passResetKey;
    private int failedPasswordChanges = 0;
    private Gender gender;
    private AgentConversationStatus agentConversationStatus;
}