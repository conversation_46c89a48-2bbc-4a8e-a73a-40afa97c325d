package com.hb.crm.core.services;

import com.hb.crm.core.Enums.ReportEntity;
import com.hb.crm.core.Enums.EmployeeNotificationType;
import com.hb.crm.core.Enums.NotificationEntityType;
import com.hb.crm.core.Enums.UserType;
import com.hb.crm.core.services.interfaces.EmployeeNotificationService;
import com.hb.crm.core.repositories.EmployeeRepository;
import com.hb.crm.core.beans.Employee;
import com.hb.crm.core.Enums.ReportStatus;
import com.hb.crm.core.beans.Report;
import com.hb.crm.core.beans.ReportReply;
import com.hb.crm.core.beans.User;
import com.hb.crm.core.beans.chat.GroupChat.GroupChatMessage;
import com.hb.crm.core.beans.chat.GroupChat.GroupConversation;
import com.hb.crm.core.beans.chat.OneChat.ChatMessage;
import com.hb.crm.core.beans.chat.OneChat.Conversation;
import com.hb.crm.core.beans.LiveStream.LiveStream;
import com.hb.crm.core.dtos.*;
import com.hb.crm.core.dtos.chat.response.ChatMessageResponseDto;
import com.hb.crm.core.dtos.chat.response.ConversationDto;
import com.hb.crm.core.dtos.chat.response.GroupChatMessageResponseDto;
import com.hb.crm.core.dtos.chat.response.GroupConversationResponseDto;
import com.hb.crm.core.dtos.LiveStream.LiveStreamDto;
import com.hb.crm.core.exceptions.CustomException;
import com.hb.crm.core.repositories.ReportRepository;
import com.hb.crm.core.repositories.UserRepository;
import com.hb.crm.core.repositories.chat.ChatMessageRepository;
import com.hb.crm.core.repositories.chat.ConversationRepository;
import com.hb.crm.core.repositories.chat.GroupChatMessageRepository;
import com.hb.crm.core.repositories.chat.GroupConversationRepository;
import com.hb.crm.core.repositories.LiveStream.LiveStreamRepository;
import com.hb.crm.core.services.interfaces.ReportService;
import lombok.RequiredArgsConstructor;
import org.modelmapper.ModelMapper;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Service
@RequiredArgsConstructor
public class ReportServiceImpl implements ReportService {
    
    private final ModelMapper modelMapper;
    private final ReportRepository reportRepository;
    private final UserRepository userRepository;
    private final ChatMessageRepository chatMessageRepository;
    private final GroupChatMessageRepository groupChatMessageRepository;
    private final ConversationRepository conversationRepository;
    private final GroupConversationRepository groupConversationRepository;
    private final LiveStreamRepository liveStreamRepository;
    private final EmployeeNotificationService employeeNotificationService;
    private final EmployeeRepository employeeRepository;

    @Transactional
    private Object fetchEntityDetails(String entityId, ReportEntity entityName) {
        try {
            return switch (entityName) {
                case User -> {
                    User user = userRepository.findById(entityId)
                        .orElseThrow(() -> new CustomException(404, "User not found with id: " + entityId));
                    yield modelMapper.map(user, SimpleUserinfoDto.class);
                }
                case ChatMessage -> {
                    ChatMessage chatMessage = chatMessageRepository.findById(entityId)
                        .orElseThrow(() -> new CustomException(404, "Chat message not found with id: " + entityId));
                    yield modelMapper.map(chatMessage, ChatMessageResponseDto.class);
                }
                case GroupChatMessage -> {
                    GroupChatMessage groupChatMessage = groupChatMessageRepository.findById(entityId)
                        .orElseThrow(() -> new CustomException(404, "HighLight chat message not found with id: " + entityId));
                    yield modelMapper.map(groupChatMessage, GroupChatMessageResponseDto.class);
                }
                case Conversation -> {
                    Conversation conversation = conversationRepository.findById(entityId)
                        .orElseThrow(() -> new CustomException(404, "Conversation not found with id: " + entityId));
                    yield modelMapper.map(conversation, ConversationDto.class);
                }
                case GroupConversation -> {
                    GroupConversation groupConversation = groupConversationRepository.findById(entityId)
                        .orElseThrow(() -> new CustomException(404, "HighLight conversation not found with id: " + entityId));
                    yield modelMapper.map(groupConversation, GroupConversationResponseDto.class);
                }
                case Comment -> null;
                case Media -> null;
                case Package -> null;
                case Post -> null;
                case Reply -> null;
                case LiveStream -> {
                    LiveStream liveStream = liveStreamRepository.findById(entityId)
                        .orElseThrow(() -> new CustomException(404, "Live stream not found with id: " + entityId));
                    
                    // Create DTO manually to avoid ModelMapper mapping conflicts
                    LiveStreamDto dto = new LiveStreamDto();
                    dto.setId(liveStream.getId());
                    dto.setTitle(liveStream.getTitle());
                    dto.setChannelArn(liveStream.getChannelArn());
                    dto.setStreamKey(liveStream.getStreamKey());
                    dto.setPlaybackUrl(liveStream.getPlaybackUrl());
                    dto.setIngestEndpoint(liveStream.getIngestEndpoint());
                    dto.setStatus(liveStream.getStatus());
                    dto.setCreatedAt(liveStream.getCreatedAt());
                    dto.setEndedAt(liveStream.getEndedAt());
                    dto.setNumberOfReactions(liveStream.getNumberOfReactions());
                    dto.setNumberOfComments(liveStream.getNumberOfComments());
                    dto.setViewersCount(liveStream.getViewersCount());
                    dto.setPrivate(liveStream.isPrivate());
                    dto.setMp4ConversionStatus(liveStream.getMp4ConversionStatus());
                    dto.setMp4Key(liveStream.getMp4Key());
                    dto.setThumbnailClipUrl(liveStream.getThumbnailClipUrl());
                    dto.setThumbnailImageUrl(liveStream.getThumbnailImageUrl());
                    
                    // Handle complex field mappings
                    if (liveStream.getInfulancer() != null) {
                        dto.setInfulancerId(liveStream.getInfulancer().getId());
                        String fullName = (liveStream.getInfulancer().getFirstName() != null ? liveStream.getInfulancer().getFirstName() : "") +
                                        " " + (liveStream.getInfulancer().getLastName() != null ? liveStream.getInfulancer().getLastName() : "");
                        dto.setInfulancerName(fullName.trim());
                    }
                    
                    if (liveStream.getPackageRef() != null) {
                        dto.setPackageId(liveStream.getPackageRef().getId());
                        dto.setPackageName(liveStream.getPackageRef().getName());
                    }
                    
                    yield dto;
                }
            };
        } catch (IllegalArgumentException e) {
            throw new CustomException(400, "Invalid entity type: " + entityName);
        }
    }
    private ReportDto mapReportWithEntity(Report report) {
        ReportDto dto = modelMapper.map(report, ReportDto.class);
        for(String entityId : report.getEntityId()){
            Object entityDetails = fetchEntityDetails(entityId, report.getEntityName());
            dto.getEntityDetails().add(entityDetails);
        }
        return dto;
    }

    @Override
    @Transactional
    public ReportDto create(CreateUpdateReportDto createDto , String userId) {
        Report report = new Report();
        report.setReason(createDto.getReason());
        report.setEntityId(createDto.getEntityId());
        report.setEntityName(createDto.getEntityName());
        report.setUserId(userId);
        report.setCreated(LocalDateTime.now());
        report.setModified(LocalDateTime.now());
        ReportDto result = mapReportWithEntity(report);
        Report savedReport = reportRepository.save(report);
        result.setId(savedReport.getId());

        // Send employee notification for post reporting
        if (createDto.getEntityName() == ReportEntity.Post) {
            sendEmployeeNotificationForPostReport(savedReport, userId);
        }

        return result;
    }

    @Override
    @Transactional
    public ReportDto update(String id, CreateUpdateReportDto updateDto , String userId) {
        Optional<Report> existingReport = reportRepository.findById(id);
        
        if (existingReport.isEmpty()) {
            throw new RuntimeException("Report not found with id: " + id);
        }

        Report report = existingReport.get();
        report.setReason(updateDto.getReason());
        report.setEntityId(updateDto.getEntityId());
        report.setEntityName(updateDto.getEntityName());
        report.setUserId(userId);
        report.setModified(LocalDateTime.now());
        ReportDto result = mapReportWithEntity(report);
        Report updatedReport = reportRepository.save(report);
        result.setId(updatedReport.getId());
        return result;
    }


    @Override
    @Transactional
    public void delete(String id) {
        if (!reportRepository.existsById(id)) {
            throw new RuntimeException("Report not found with id: " + id);
        }
        reportRepository.deleteById(id);
    }

    @Override
    @Transactional(readOnly = true)
    public ReportDto findById(String id) {
        Optional<Report> report = reportRepository.findById(id);
        if (report.isEmpty()) {
            throw new RuntimeException("Report not found with id: " + id);
        }
        return mapReportWithEntity(report.get());
    }
    @Override
    @Transactional(readOnly = true)
    public ReportDto addReply(String reportId, CreateReportReplyDto reply,String userId ) {
        Report report = reportRepository.findById(reportId)
                .orElseThrow(() -> new CustomException(404,"Report not found"));
        ReportReply reply1 = new ReportReply();
        reply1.setSenderId(userId);
        reply1.setMessage(reply.getMessage());
        report.getReplies().add(reply1);
        report.setModified(LocalDateTime.now());
        return mapReportWithEntity(reportRepository.save(report));
    }

    @Override
    @Transactional(readOnly = true)
    public ReportDto updateStatus(String reportId, ReportStatus status) {
        Report report = reportRepository.findById(reportId)
                .orElseThrow(() -> new CustomException(404,"Report not found"));
        report.setStatus(status);
        report.setModified(LocalDateTime.now());

        return mapReportWithEntity(reportRepository.save(report));
    }

    @Override
    @Transactional(readOnly = true)
    public PageDto<ReportDto> search(String entityId , String entityName,String userID , int page, int limit) {
        // Validate pagination parameters
        if (page < 0) {
            throw new CustomException(400, "Page number cannot be negative");
        }
        if (limit <= 0) {
            throw new CustomException(400, "Page size must be greater than 0");
        }

        // Check if at least one search parameter is provided
        boolean hasValidSearchCriteria = (entityId != null && !entityId.trim().isEmpty()) || 
                                       (entityName != null && !entityName.trim().isEmpty());
        // If no search criteria provided, return all reports
        Page<Report> reportPage;
        Pageable pageable = PageRequest.of(page, limit);
        
        if (!hasValidSearchCriteria) {
            reportPage = reportRepository.findAll(pageable);
        } else {
            // Clean the search parameters
            String cleanEntityId = entityId != null ? entityId.trim() : null;
            String cleanEntityName = entityName != null ? entityName.trim() : null;
            
            reportPage = reportRepository.search(cleanEntityId, cleanEntityName,userID, pageable);
        }
        // Map reports and include entity details
        List<ReportDto> reportDtos = reportPage.getContent().stream()
                .map(this::mapReportWithEntity)
                .toList();
        PageDto<ReportDto> pageDto = new PageDto<>();
        pageDto.setItemsPerPage(limit);
        pageDto.setTotalNoOfItems(reportPage.getTotalElements());
        pageDto.setPageNumber(page);
        pageDto.setItems(reportDtos);
        return pageDto;
    }



    @Override
    @Transactional(readOnly = true)
    public PageDto<ReportDto> findByUserId(String userId, int page, int limit) {
        // Validate user exists
        userRepository.findById(userId)
            .orElseThrow(() -> new CustomException(404, "User not found with id: " + userId));

        // Create pageable with sorting
        Pageable pageable = PageRequest.of(page, limit);
        
        // Get paginated reports
        Page<Report> reportPage = reportRepository.findByUserId(userId, pageable);

        // Convert to DTOs
        List<ReportDto> reportDtos = reportPage.getContent().stream()
            .map(this::mapReportWithEntity)
            .toList();

        // Create and return PageDto
        return new PageDto<>(
            limit,
            reportPage.getTotalElements(),
            page,
            reportDtos
        );
    }

    /**
     * Helper method to send employee notifications for post reporting
     */
    private void sendEmployeeNotificationForPostReport(Report report, String reporterUserId) {
        try {
            List<Employee> allEmployees = employeeRepository.findAll();
            User reporter = userRepository.findById(reporterUserId).orElse(null);

            if (reporter != null) {
                for (Employee employee : allEmployees) {
                    employeeNotificationService.sendAndStoreNotification(
                            report.getId(),
                            EmployeeNotificationType.POST_REPORTED,
                            employee,
                            List.of(report, reporter),
                            reporter.getProfileImage(),
                            null,
                            NotificationEntityType.USER,
                            reporter,
                            null,
                            reporter.getUsername(),
                            reporter.getUsertype()
                    );
                }
            }
        } catch (Exception e) {
            System.err.println("Error sending employee notification for post report: " + e.getMessage());
        }
    }
}
