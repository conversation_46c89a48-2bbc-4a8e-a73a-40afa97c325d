package com.hb.crm.core.dtos;

import com.hb.crm.core.Enums.Gender;
import com.hb.crm.core.Enums.UserType;
import com.hb.crm.core.beans.UserInfo;
import lombok.Getter;
import lombok.Setter;
import org.springframework.data.mongodb.core.index.Indexed;

import java.time.LocalDateTime;

@Setter
@Getter
public class UserDto {
    private String id;
    private LocalDateTime creationDate;
    private LocalDateTime updatedDate;
    private String about;
    private UserInfo userInfo;
    @Indexed(unique = true)
    private String username;
    private String firstName;
    private String lastName;
    private Gender gender;
    protected String code;
    private String role;
    private String collectionDeviceId;
    private boolean accountLocked = false;
    private String city;
    private String country;
    private String token;
    private String coverImage;
    private UserType usertype = UserType.Traveler;
    private String profileImage;
    private boolean privateProfile = false;
}
