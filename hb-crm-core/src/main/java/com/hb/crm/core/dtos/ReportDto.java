package com.hb.crm.core.dtos;

import com.hb.crm.core.Enums.ReportStatus;
import com.hb.crm.core.beans.ReportReply;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

import java.time.LocalDateTime;
import java.util.List;

@Getter
@Setter
public class ReportDto {
    private String id;
    private String reason;
    private String userId;
    private List<String> entityId;
    private String entityName;
    private List<Object> entityDetails = new ArrayList<>(); // This will hold the referenced entity details
    private ReportStatus status;
    private LocalDateTime created;
    private LocalDateTime modified;
    private List<ReportReply> replies;
}
