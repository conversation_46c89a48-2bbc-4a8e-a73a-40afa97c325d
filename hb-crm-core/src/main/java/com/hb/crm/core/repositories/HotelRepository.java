package com.hb.crm.core.repositories;

import com.hb.crm.core.beans.HotelMasterData;
import com.hb.crm.core.beans.PackagePlaces.simpleHotelInfo;
import org.springframework.data.mongodb.repository.Aggregation;
import org.springframework.data.mongodb.repository.MongoRepository;

import java.util.List;

public interface HotelRepository  extends MongoRepository<HotelMasterData, String> {
  @Aggregation(pipeline = {
    "{ $match: { " +
        "$and: [ " +
            "{ $or: [ " +
                "{ countryId: ?1 }, " +
                "{ cityId: ?1 }, " +
                "{ areaId: ?1 } " +
            "] }, " +
            "{ name: { $regex: ?0, $options: 'i' } } " +
        "] " +
    "} }",

    "{ $project: { " +
        "id: 1, " +
        "name: 1, " +
        "media: { $arrayElemAt: [ " +
            "{ $map: { " +
                "input: { $filter: { " +
                    "input: '$images', " +
                    "as: 'media', " +
                    "cond: { $eq: ['$$media.mainImage', true] } " +
                "} }, " +
                "as: 'media', " +
                "in: { " +
                    "type: '$$media.type', " +
                    "caption: '$$media.caption', " +
                    "asset: '$$media.asset', " +
                    "url: '$$media.url', " +
                    "mainImage: '$$media.mainImage' " +
                "} " +
            "} }, " +
            "0 " +
        "] " +
    "} } } "
})
List<simpleHotelInfo> searchByName(String searchQuery, String parentId);

  boolean existsByNameOrCityAndCountryCode(String name, String city, String countryCode);
  boolean existsByLatitudeAndLongitude(float latitude, float longitude);


    boolean existsByNameIgnoreCaseAndCityIgnoreCaseAndCountryCodeIgnoreCase(String name, String city, String countryCode);

  boolean existsByLatitudeBetweenAndLongitudeBetween(double v, double v1, double v2, double v3);
}
