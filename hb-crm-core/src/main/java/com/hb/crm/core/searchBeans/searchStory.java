package com.hb.crm.core.searchBeans;

import com.hb.crm.core.Enums.PackageStatus;
import com.hb.crm.core.beans.StoryOverlay;
import com.hb.crm.core.beans.Tag;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.List;

@Setter
@Getter
public class searchStory extends BaseEntity  {

    private String text;
    private List<MediaWrapperSearch> media;
    private float latitude;
    private float longtuid;
    private String place;
    private simpleUserInfo  user;
    private simplePackageInfo Package;
    private LocalDateTime PostedDate;
    private List<Tag>tags;
    private PackageStatus postStatus=PackageStatus.draft;
    private String rejectionNote;
    private String note;
    private int commentsCount;
    private int reactsCount;
    private int viewsCount;
    private int sharesCount;
    private String slug;
    private List<StoryOverlay> overlays;
    private String mapUrl;

    private String liveStreamId; 
    private boolean isLiveStream = false; 
    private boolean isCurrentlyLive = false; 
    private String playbackUrl;
    private Boolean showPackage=true;

    public simplePackageInfo getPackage() {
        if(showPackage){
            return Package;
        }else
            return null;
     }

}
