package com.hb.crm.core.config.AOP;

import com.hb.crm.core.beans.AopAudit.AuditContext;
import jakarta.servlet.*;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.io.IOException;

/**
 * Servlet filter to set up audit context for web requests
 * This filter runs early in the filter chain to ensure audit context is available
 */
@Component
@Order(1)
public class AuditContextFilter implements Filter {

    private static final Logger logger = LoggerFactory.getLogger(AuditContextFilter.class);

    @Value("${audit.enabled:true}")
    private boolean auditEnabled;

    @Value("${audit.context.enabled:true}")
    private boolean contextEnabled;

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
        logger.info("Audit Context Filter initialized");
    }

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
            throws IOException, ServletException {

        if (!auditEnabled || !contextEnabled) {
            chain.doFilter(request, response);
            return;
        }

        HttpServletRequest httpRequest = (HttpServletRequest) request;
        HttpServletResponse httpResponse = (HttpServletResponse) response;

        try {
            // Create and set up audit context
            AuditContext context = setupAuditContext(httpRequest);
            
            // Add correlation ID to response headers for tracing
            httpResponse.setHeader("X-Correlation-ID", context.getCorrelationId());
            httpResponse.setHeader("X-Request-ID", context.getRequestId());

            // Continue with the filter chain
            chain.doFilter(request, response);

        } catch (Exception e) {
            logger.error("Error in audit context filter", e);
            // Continue processing even if audit context setup fails
            chain.doFilter(request, response);
        } finally {
            // Clean up audit context
            AuditContext.clearCurrentContext();
        }
    }

    @Override
    public void destroy() {
        logger.info("Audit Context Filter destroyed");
    }

    /**
     * Set up audit context from HTTP request
     */
    private AuditContext setupAuditContext(HttpServletRequest request) {
        AuditContext context = AuditContext.createContext();

        // Basic request information
        context.setHttpMethod(request.getMethod());
        context.setEndpoint(request.getRequestURI());
        context.setIpAddress(getClientIpAddress(request));
        context.setUserAgent(request.getHeader("User-Agent"));

        // Session information
        if (request.getSession(false) != null) {
            context.setSessionId(request.getSession().getId());
        }

        // Extract correlation ID from headers if present
        String correlationId = request.getHeader("X-Correlation-ID");
        if (correlationId != null && !correlationId.isEmpty()) {
            context.setCorrelationId(correlationId);
        }

        // Extract transaction ID from headers if present
        String transactionId = request.getHeader("X-Transaction-ID");
        if (transactionId != null && !transactionId.isEmpty()) {
            context.setTransactionId(transactionId);
        }

        // Extract tenant ID from headers if present (for multi-tenant applications)
        String tenantId = request.getHeader("X-Tenant-ID");
        if (tenantId != null && !tenantId.isEmpty()) {
            context.setTenantId(tenantId);
        }

        // Extract device information
        String deviceInfo = request.getHeader("X-Device-Info");
        if (deviceInfo != null && !deviceInfo.isEmpty()) {
            context.setDeviceInfo(deviceInfo);
        }

        // Extract browser information from User-Agent
        String userAgent = request.getHeader("User-Agent");
        if (userAgent != null) {
            context.setBrowserInfo(extractBrowserInfo(userAgent));
            context.setOsInfo(extractOSInfo(userAgent));
        }

        // Extract location information if available
        String location = request.getHeader("X-User-Location");
        if (location != null && !location.isEmpty()) {
            context.setLocation(location);
        }

        // Determine if this is an automated request
        boolean isAutomated = isAutomatedRequest(request);
        context.setAutomated(isAutomated);

        // Extract source system information
        String sourceSystem = request.getHeader("X-Source-System");
        if (sourceSystem != null && !sourceSystem.isEmpty()) {
            context.setSourceSystem(sourceSystem);
        }

        // Set environment based on system properties
        context.setEnvironment(determineEnvironment());

        // Extract request parameters (for GET requests)
        if ("GET".equalsIgnoreCase(request.getMethod())) {
            request.getParameterMap().forEach((key, values) -> {
                if (values != null && values.length > 0) {
                    context.addRequestParameter(key, values.length == 1 ? values[0] : values);
                }
            });
        }

        // Add additional metadata
        context.addMetadata("requestURL", request.getRequestURL().toString());
        context.addMetadata("queryString", request.getQueryString());
        context.addMetadata("protocol", request.getProtocol());
        context.addMetadata("scheme", request.getScheme());
        context.addMetadata("serverName", request.getServerName());
        context.addMetadata("serverPort", request.getServerPort());
        context.addMetadata("contextPath", request.getContextPath());
        context.addMetadata("servletPath", request.getServletPath());

        return context;
    }

    /**
     * Get client IP address from request
     */
    private String getClientIpAddress(HttpServletRequest request) {
        String[] headerNames = {
            "X-Forwarded-For", "X-Real-IP", "Proxy-Client-IP", 
            "WL-Proxy-Client-IP", "HTTP_X_FORWARDED_FOR", "HTTP_X_FORWARDED", 
            "HTTP_X_CLUSTER_CLIENT_IP", "HTTP_CLIENT_IP", "HTTP_FORWARDED_FOR", 
            "HTTP_FORWARDED", "HTTP_VIA", "REMOTE_ADDR"
        };

        for (String header : headerNames) {
            String ip = request.getHeader(header);
            if (ip != null && !ip.isEmpty() && !"unknown".equalsIgnoreCase(ip)) {
                // Handle multiple IPs in X-Forwarded-For
                if (ip.contains(",")) {
                    ip = ip.split(",")[0].trim();
                }
                return ip;
            }
        }

        return request.getRemoteAddr();
    }

    /**
     * Extract browser information from User-Agent
     */
    private String extractBrowserInfo(String userAgent) {
        if (userAgent == null) return "Unknown";
        
        userAgent = userAgent.toLowerCase();
        if (userAgent.contains("chrome")) return "Chrome";
        if (userAgent.contains("firefox")) return "Firefox";
        if (userAgent.contains("safari")) return "Safari";
        if (userAgent.contains("edge")) return "Edge";
        if (userAgent.contains("opera")) return "Opera";
        if (userAgent.contains("internet explorer") || userAgent.contains("msie")) return "Internet Explorer";
        
        return "Unknown";
    }

    /**
     * Extract OS information from User-Agent
     */
    private String extractOSInfo(String userAgent) {
        if (userAgent == null) return "Unknown";
        
        userAgent = userAgent.toLowerCase();
        if (userAgent.contains("windows nt 10")) return "Windows 10";
        if (userAgent.contains("windows nt 6.3")) return "Windows 8.1";
        if (userAgent.contains("windows nt 6.2")) return "Windows 8";
        if (userAgent.contains("windows nt 6.1")) return "Windows 7";
        if (userAgent.contains("windows")) return "Windows";
        if (userAgent.contains("mac os x")) return "macOS";
        if (userAgent.contains("linux")) return "Linux";
        if (userAgent.contains("android")) return "Android";
        if (userAgent.contains("iphone") || userAgent.contains("ipad")) return "iOS";
        
        return "Unknown";
    }

    /**
     * Determine if this is an automated request
     */
    private boolean isAutomatedRequest(HttpServletRequest request) {
        String userAgent = request.getHeader("User-Agent");
        if (userAgent != null) {
            userAgent = userAgent.toLowerCase();
            return userAgent.contains("bot") || 
                   userAgent.contains("crawler") || 
                   userAgent.contains("spider") ||
                   userAgent.contains("curl") ||
                   userAgent.contains("wget") ||
                   userAgent.contains("postman") ||
                   userAgent.contains("insomnia");
        }
        
        // Check for API key or automation headers
        return request.getHeader("X-API-Key") != null ||
               request.getHeader("X-Automation") != null ||
               "true".equals(request.getHeader("X-Automated-Request"));
    }

    /**
     * Determine environment based on system properties
     */
    private String determineEnvironment() {
        String profile = System.getProperty("spring.profiles.active");
        if (profile != null) {
            if (profile.contains("prod")) return "PRODUCTION";
            if (profile.contains("test")) return "TEST";
            if (profile.contains("dev")) return "DEVELOPMENT";
            if (profile.contains("staging")) return "STAGING";
        }
        return "UNKNOWN";
    }
}
