package com.hb.crm.core.beans.Hotel;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.ArrayList;
@Data
public class Option {
    private String id;
    private String description;
    private ArrayList<String> amenities;
    private boolean refundable;
    private  boolean isStandard =false;
    @JsonProperty("package")
    private boolean mypackage;
    private ArrayList<CancelPenalty> cancelPenalties;
    private TotalPrice totalPrice;
    private ArrayList<OccupancyPricing> occupancyPricing;
    private ArrayList<String> paymentOptions;
    private boolean payAtProperty;
    private int availableRooms;

}
