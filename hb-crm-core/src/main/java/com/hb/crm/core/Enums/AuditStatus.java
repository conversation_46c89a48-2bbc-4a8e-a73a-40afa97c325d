package com.hb.crm.core.Enums;

/**
 * Enum representing the status of audit operations
 */
public enum AuditStatus {
    SUCCESS("SUCCESS", "Operation completed successfully"),
    FAILED("FAILED", "Operation failed"),
    PENDING("PENDING", "Operation is pending"),
    IN_PROGRESS("IN_PROGRESS", "Operation is in progress"),
    CANCELLED("CANCELLED", "Operation was cancelled"),
    TIMEOUT("TIMEOUT", "Operation timed out"),
    PARTIAL_SUCCESS("PARTIAL_SUCCESS", "Operation partially succeeded"),
    WARNING("WARNING", "Operation completed with warnings"),
    UNAUTHORIZED("UNAUTHORIZED", "Unauthorized access attempt"),
    FORBIDDEN("FORBIDDEN", "Forbidden access attempt"),
    NOT_FOUND("NOT_FOUND", "Resource not found"),
    VALIDATION_ERROR("VALIDATION_ERROR", "Validation error occurred"),
    BUSINESS_ERROR("BUSINESS_ERROR", "Business rule violation"),
    SYSTEM_ERROR("SYSTEM_ERROR", "System error occurred"),
    NETWORK_ERROR("NETWORK_ERROR", "Network error occurred"),
    DATABASE_ERROR("DATABASE_ERROR", "Database error occurred"),
    EXTERNAL_SERVICE_ERROR("EXTERNAL_SERVICE_ERROR", "External service error"),
    CONFIGURATION_ERROR("CONFIGURATION_ERROR", "Configuration error"),
    SECURITY_VIOLATION("SECURITY_VIOLATION", "Security violation detected"),
    RATE_LIMITED("RATE_LIMITED", "Rate limit exceeded"),
    QUOTA_EXCEEDED("QUOTA_EXCEEDED", "Quota exceeded"),
    MAINTENANCE("MAINTENANCE", "System under maintenance"),
    DEPRECATED("DEPRECATED", "Deprecated operation used"),
    UNKNOWN("UNKNOWN", "Unknown status");

    private final String code;
    private final String description;

    AuditStatus(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    @Override
    public String toString() {
        return code;
    }

    /**
     * Determine status from exception
     */
    public static AuditStatus fromException(Throwable throwable) {
        if (throwable == null) {
            return SUCCESS;
        }

        String exceptionName = throwable.getClass().getSimpleName().toLowerCase();
        
        if (exceptionName.contains("unauthorized") || exceptionName.contains("authentication")) {
            return UNAUTHORIZED;
        } else if (exceptionName.contains("forbidden") || exceptionName.contains("access")) {
            return FORBIDDEN;
        } else if (exceptionName.contains("notfound") || exceptionName.contains("notexist")) {
            return NOT_FOUND;
        } else if (exceptionName.contains("validation") || exceptionName.contains("invalid")) {
            return VALIDATION_ERROR;
        } else if (exceptionName.contains("timeout")) {
            return TIMEOUT;
        } else if (exceptionName.contains("network") || exceptionName.contains("connection")) {
            return NETWORK_ERROR;
        } else if (exceptionName.contains("database") || exceptionName.contains("sql")) {
            return DATABASE_ERROR;
        } else if (exceptionName.contains("security")) {
            return SECURITY_VIOLATION;
        } else {
            return SYSTEM_ERROR;
        }
    }

    /**
     * Check if status indicates success
     */
    public boolean isSuccess() {
        return this == SUCCESS || this == PARTIAL_SUCCESS;
    }

    /**
     * Check if status indicates failure
     */
    public boolean isFailure() {
        return !isSuccess() && this != PENDING && this != IN_PROGRESS;
    }

    /**
     * Check if status indicates an error
     */
    public boolean isError() {
        return this == FAILED || this == SYSTEM_ERROR || this == DATABASE_ERROR || 
               this == NETWORK_ERROR || this == EXTERNAL_SERVICE_ERROR || 
               this == CONFIGURATION_ERROR || this == BUSINESS_ERROR;
    }
}
