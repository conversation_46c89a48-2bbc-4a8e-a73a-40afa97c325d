package com.hb.crm.core.beans;

import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.CompoundIndex;
import org.springframework.data.mongodb.core.index.CompoundIndexes;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.DBRef;
import org.springframework.data.mongodb.core.mapping.Document;

import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@Document(collection = "blogView")
@CompoundIndexes({
    @CompoundIndex(name = "blog_user_idx", def = "{'blog': 1, 'user': 1}", unique = true),
    @CompoundIndex(name = "blog_date_idx", def = "{'blog': 1, 'viewDate': -1}"),
    @CompoundIndex(name = "user_date_idx", def = "{'user': 1, 'viewDate': -1}")
})
public class BlogView {
    @Id
    private String id;
    
    @DBRef
    private Blog blog;
    
    @DBRef
    private User user;
    
    @Indexed
    private LocalDateTime viewDate;
    
    private String ipAddress;
    private String userAgent;
    private String referrer;
    private Long readTime; // in seconds
    
    public BlogView(Blog blog, User user) {
        this.blog = blog;
        this.user = user;
        this.viewDate = LocalDateTime.now();
    }
    
    public BlogView(Blog blog, String ipAddress) {
        this.blog = blog;
        this.ipAddress = ipAddress;
        this.viewDate = LocalDateTime.now();
    }
}
