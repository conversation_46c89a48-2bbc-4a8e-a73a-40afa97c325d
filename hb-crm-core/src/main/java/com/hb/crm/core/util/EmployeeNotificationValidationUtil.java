package com.hb.crm.core.util;

import com.hb.crm.core.Enums.DisableNotificationType;
import com.hb.crm.core.Enums.NotificationChannelType;
import com.hb.crm.core.Enums.EmployeeNotificationType;
import com.hb.crm.core.beans.Notification.EmployeeNotificationDisableSetting;
import com.hb.crm.core.beans.Notification.EmployeeNotificationMute;
import com.hb.crm.core.beans.Notification.EmployeeNotificationMuteUser;
import com.hb.crm.core.beans.Notification.EmployeeNotificationSetting;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * Utility class for validating employee notification settings and determining if notifications should be sent.
 * <p>
 * This class provides static methods to check if specific notification types are disabled
 * based on employee notification settings. It uses the mapping defined in Constants.java to
 * group notification types into categories that match the DisableNotificationType enum values.
 *
 * <AUTHOR> CRM Team
 * @see Constants for notification type groupings
 * @see DisableNotificationType for available disable categories
 * @see EmployeeNotificationSetting for employee notification preferences
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class EmployeeNotificationValidationUtil {

    /**
     * Comprehensive validation method that checks if a notification should be blocked for a specific channel.
     *
     * <p>
     * This method combines multiple validation checks:
     * 1. Checks if the notification type is disabled for the specific channel
     * 2. Check if the channel is globally disabled by employee
     * 3. Optionally, checks if the specific entity is muted
     * <p>
     *
     * @param notificationType The notification type to validate (must not be null)
     * @param channelType The notification channel type to check (must not be null)
     * @param notificationSetting The employee's notification settings (can be null)
     * @param notificationMute The notification mute settings for the entity (can be null)
     * @param notificationMuteUser The notification mute settings for the user (can be null)
     * @return true if the notification should be blocked for this channel, false otherwise
     *
     * @throws IllegalArgumentException if notificationType or channelType is null
     */
    public static boolean isNotificationBlockedForChannel(EmployeeNotificationType notificationType,
                                                         NotificationChannelType channelType,
                                                         EmployeeNotificationSetting notificationSetting,
                                                         EmployeeNotificationMute notificationMute,
                                                         EmployeeNotificationMuteUser notificationMuteUser) {
        if (notificationType == null)
            throw new IllegalArgumentException("EmployeeNotificationType cannot be null");

        if (channelType == null)
            throw new IllegalArgumentException("NotificationChannelType cannot be null");

        if (notificationSetting == null)
            return false;

        // Check if the notification type is disabled for this specific channel
        if (notificationSetting.getDisabledNotifications() != null &&
                isNotificationTypeDisabledForChannel(notificationType, channelType, notificationSetting.getDisabledNotifications()))
            return true;

        // check if the channel is globally disabled
        if(isChannelBlocked(channelType, notificationSetting))
            return true;

        // Check if the channel is muted for the entity
        if(notificationMute != null)
            return isEntityMutedForChannel(channelType, notificationMute);

        if(notificationMuteUser != null)
            return isUserMutedForTypeForChannel(channelType, notificationType, notificationMuteUser);

        return false;
    }

    /**
     * Checks if a notification type belongs to a specific disable notification category.
     * <p>
     * This method uses the Constants.java mappings to determine if a notification type
     * belongs to a specific category defined by DisableNotificationType.
     *
     * @param notificationType The notification type to categorize
     * @param disableNotificationType The disable category to check against
     * @return true if the notification type belongs to the specified category, false otherwise
     */
    public static boolean isNotificationTypeInCategory(EmployeeNotificationType notificationType,
                                                      DisableNotificationType disableNotificationType) {
        return switch (disableNotificationType) {
            case PACKAGE_NOTIFICATIONS -> Constants.EMPLOYEE_PACKAGE_NOTIFICATIONS.contains(notificationType);
            case PACKAGE_NOTIFICATIONS_TWM -> Constants.EMPLOYEE_PACKAGE_NOTIFICATIONS.contains(notificationType);
            case PACKAGE_NOTIFICATIONS_FOLLOW_ME -> Constants.EMPLOYEE_PACKAGE_NOTIFICATIONS.contains(notificationType);

            case POST_STORY_NOTIFICATIONS -> Constants.EMPLOYEE_POST_NOTIFICATIONS.contains(notificationType);
            case POST_NOTIFICATIONS -> Constants.EMPLOYEE_POST_NOTIFICATIONS.contains(notificationType);
            case STORY_NOTIFICATIONS -> false; // No employee story notifications yet
            case REEL_NOTIFICATIONS -> false; // No employee reel notifications yet
            case LIVESTREAMS_NOTIFICATIONS -> false; // No employee livestream notifications yet

            case CHAT_NOTIFICATIONS -> false; // No employee chat notifications yet
            case SUPPORT_CHAT_NOTIFICATIONS -> false; // No employee support chat notifications yet
            case GROUP_CHAT_NOTIFICATIONS -> false; // No employee group chat notifications yet

            case INTERACTION_NOTIFICATIONS -> false; // No employee interaction notifications yet
            case REACT_NOTIFICATIONS -> false; // No employee react notifications yet
            case COMMENT_NOTIFICATIONS -> false; // No employee comment notifications yet

            case ACTION_NOTIFICATIONS -> Constants.EMPLOYEE_USER_NOTIFICATIONS.contains(notificationType);
            case ADMIN_REGISTER_NOTIFICATIONS -> Constants.EMPLOYEE_ADMIN_REGISTER_NOTIFICATIONS.contains(notificationType);
        };
    }

    /**
     * Validates if a notification type is disabled for a specific channel based on the employee's notification settings.
     * <p>
     * This method checks if the given notification type belongs to any of the disabled
     * notification categories in the employee's settings for the specified channel.
     *
     * @param notificationType The notification type to validate (must not be null)
     * @param channelType The notification channel type to check (must not be null)
     * @param notificationDisableSettings The employee's notification settings containing disabled categories
     * @return true if the notification type is disabled for the specified channel, false otherwise
     *
     * @throws IllegalArgumentException if notificationType or channelType is null
     */
    public static boolean isNotificationTypeDisabledForChannel(EmployeeNotificationType notificationType,
                                                               NotificationChannelType channelType,
                                                               List<EmployeeNotificationDisableSetting> notificationDisableSettings) {

        if (notificationType == null) {
            throw new IllegalArgumentException("EmployeeNotificationType cannot be null");
        }

        if (channelType == null) {
            throw new IllegalArgumentException("NotificationChannelType cannot be null");
        }

        // Check each disabled notification setting
        for (EmployeeNotificationDisableSetting disableSetting : notificationDisableSettings) {
            // Check if this notification type belongs to the disabled category
            if (isNotificationTypeInCategory(notificationType, disableSetting.getDisableNotificationType())) {
                // Check if the channel is disabled for this category
                if (disableSetting.getChannelTypes() != null &&
                        disableSetting.getChannelTypes().contains(channelType)) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * Validates if the employee setting blocks the channel that sends the notification through.
     *
     * @param channelType The notification channel type to categorize
     * @param notificationSetting The setting of the employee
     * @return true if the channel is blocked from the employee, false otherwise
     */
    public static boolean isChannelBlocked(NotificationChannelType channelType, EmployeeNotificationSetting notificationSetting) {
        return switch (channelType) {
            case Email -> !notificationSetting.isEnableEmailNotification();
            case Sms -> !notificationSetting.isEnableSmsAppNotification();
            case WhatsApp -> !notificationSetting.isEnableWhatsAppNotification();
            case Push -> !notificationSetting.isEnablePushNotification();
            case InApp -> !notificationSetting.isEnableInAppNotification();
        };
    }

    /**
     * Validates if the entity is muted for the specific channel.
     *
     * @param channelType The notification channel type to check
     * @param notificationMute The notification mute settings for the entity
     * @return true if the entity is muted for this channel, false otherwise
     */
    public static boolean isEntityMutedForChannel(NotificationChannelType channelType, EmployeeNotificationMute notificationMute) {
        return notificationMute.getChannels() != null && notificationMute.getChannels().contains(channelType);
    }

    /**
     * Validates if the user is muted for the specific notification type and channel.
     *
     * @param channelType The notification channel type to check
     * @param notificationType The notification type to check
     * @param notificationMuteUser The notification mute user settings
     * @return true if the user is muted for this type and channel, false otherwise
     */
    public static boolean isUserMutedForTypeForChannel(NotificationChannelType channelType,
                                                      EmployeeNotificationType notificationType,
                                                      EmployeeNotificationMuteUser notificationMuteUser) {
        if (notificationMuteUser.getMuteDisableSetting() == null) {
            return false;
        }

        for (EmployeeNotificationDisableSetting disableSetting : notificationMuteUser.getMuteDisableSetting()) {
            // Check if this notification type belongs to the disabled category
            if (isNotificationTypeInCategory(notificationType, disableSetting.getDisableNotificationType())) {
                // Check if the channel is disabled for this category
                if (disableSetting.getChannelTypes() != null &&
                        disableSetting.getChannelTypes().contains(channelType)) {
                    return true;
                }
            }
        }

        return false;
    }
}
