package com.hb.crm.core.beans.chat;

import com.hb.crm.core.dtos.chat.response.ConversationDto;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.CompoundIndex;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

@Document
@Getter
@Setter
@NoArgsConstructor
@CompoundIndex(name = "queue_timestamp_idx", def = "{'timestamp': 1}")
public class QueuedConversation {
    @Id
    private String id;

    @Indexed
    private long timestamp;

    @Indexed
    private long addedToQueueTimeStamp;
    
    private ConversationDto conversationData;
}