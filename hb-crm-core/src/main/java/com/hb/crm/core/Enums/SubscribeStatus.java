package com.hb.crm.core.Enums;

public enum SubscribeStatus {
    confirmed,
    unconfirmed,
    submitted,
    readyToPay,
    //mean the cancelation requested by the user and wait for addmin approval
    //if the money already paid we will refund the money and cancel the subscription if admin approve
    //if the money not paid we will cancel the subscription WITHOUT admin approval
    pendingCancellation,
    cancelled,
    paid,
    draft,
    NotSubscribed,
    Influencer
}
