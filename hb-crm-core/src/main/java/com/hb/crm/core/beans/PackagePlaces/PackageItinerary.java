package com.hb.crm.core.beans.PackagePlaces;

import com.hb.crm.core.Enums.ActivityPriceType;
import com.hb.crm.core.Enums.State;
import com.hb.crm.core.beans.ActivityCategory;
import com.hb.crm.core.beans.Preferences;
import com.hb.crm.core.dtos.ActivityContentDto;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class PackageItinerary {

    private String name;
    private String details;
    private LocalDateTime start;
    private LocalDateTime end;
    private  String referenceId;
    private State state ;
    private ActivityContentDto activityContent;
    private ActivityCategory Category;
    private Object OtherDetail;
    private Integer minUnits = 1;                    // minimum seats / tickets required
    private Preferences unitLabel ;             // optional label (ticket, seat, ride…)
    private BigDecimal unitPrice;                    // optional price-per-unit
    private ActivityPriceType priceType = ActivityPriceType.FIXED; // FIXED or ESTIMATE
    private boolean payOnArrival = false;            // if true: user pays at activity
    private boolean includedInPackage = false;
}
