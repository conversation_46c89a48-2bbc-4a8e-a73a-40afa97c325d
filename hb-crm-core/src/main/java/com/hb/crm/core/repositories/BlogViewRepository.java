package com.hb.crm.core.repositories;

import com.hb.crm.core.beans.Blog;
import com.hb.crm.core.beans.BlogView;
import com.hb.crm.core.beans.User;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.Aggregation;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface BlogViewRepository extends MongoRepository<BlogView, String> {

    Optional<BlogView> findByBlogAndUser(Blog blog, User user);
    
    boolean existsByBlogAndUser(Blog blog, User user);
    
    long countByBlog(Blog blog);
    long countByBlogAndViewDateAfter(Blog blog, LocalDateTime date);
    
    Page<BlogView> findByBlogOrderByViewDateDesc(Blog blog, Pageable pageable);
    Page<BlogView> findByUserOrderByViewDateDesc(User user, Pageable pageable);
    
    // Analytics - daily view counts for a blog
    @Aggregation(pipeline = {
        "{ $match: { 'blog.$id': ?0, 'viewDate': { $gte: ?1, $lte: ?2 } } }",
        "{ $group: { " +
        "  '_id': { " +
        "    'year': { $year: '$viewDate' }, " +
        "    'month': { $month: '$viewDate' }, " +
        "    'day': { $dayOfMonth: '$viewDate' } " +
        "  }, " +
        "  'count': { $sum: 1 }, " +
        "  'uniqueUsers': { $addToSet: '$user' } " +
        "} }",
        "{ $addFields: { 'uniqueUserCount': { $size: '$uniqueUsers' } } }",
        "{ $project: { 'uniqueUsers': 0 } }",
        "{ $sort: { '_id.year': 1, '_id.month': 1, '_id.day': 1 } }"
    })
    List<DailyViewStats> getDailyViewStats(String blogId, LocalDateTime startDate, LocalDateTime endDate);
    
    // Analytics - hourly view counts for a blog
    @Aggregation(pipeline = {
        "{ $match: { 'blog.$id': ?0, 'viewDate': { $gte: ?1, $lte: ?2 } } }",
        "{ $group: { " +
        "  '_id': { " +
        "    'year': { $year: '$viewDate' }, " +
        "    'month': { $month: '$viewDate' }, " +
        "    'day': { $dayOfMonth: '$viewDate' }, " +
        "    'hour': { $hour: '$viewDate' } " +
        "  }, " +
        "  'count': { $sum: 1 } " +
        "} }",
        "{ $sort: { '_id.year': 1, '_id.month': 1, '_id.day': 1, '_id.hour': 1 } }"
    })
    List<HourlyViewStats> getHourlyViewStats(String blogId, LocalDateTime startDate, LocalDateTime endDate);
    
    // Get most active readers for a blog
    @Aggregation(pipeline = {
        "{ $match: { 'blog.$id': ?0 } }",
        "{ $group: { " +
        "  '_id': '$user', " +
        "  'viewCount': { $sum: 1 }, " +
        "  'lastView': { $max: '$viewDate' }, " +
        "  'totalReadTime': { $sum: '$readTime' } " +
        "} }",
        "{ $sort: { 'viewCount': -1, 'lastView': -1 } }",
        "{ $limit: ?1 }"
    })
    List<ReaderStats> getMostActiveReaders(String blogId, int limit);
    
    // Clean up old view records (for data retention)
    @Query(value = "{ 'viewDate': { $lt: ?0 } }", delete = true)
    void deleteViewsOlderThan(LocalDateTime cutoffDate);
    
    // Interfaces for aggregation results
    interface DailyViewStats {
        DateId get_id();
        long getCount();
        long getUniqueUserCount();
        
        interface DateId {
            int getYear();
            int getMonth();
            int getDay();
        }
    }
    
    interface HourlyViewStats {
        HourId get_id();
        long getCount();
        
        interface HourId {
            int getYear();
            int getMonth();
            int getDay();
            int getHour();
        }
    }
    
    interface ReaderStats {
        User get_id();
        long getViewCount();
        LocalDateTime getLastView();
        Long getTotalReadTime();
    }
}
