package com.hb.crm.core.services;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.hb.crm.core.Enums.AuditAction;
import com.hb.crm.core.Enums.AuditEntityType;
import com.hb.crm.core.Enums.AuditStatus;
import com.hb.crm.core.beans.AopAudit.AuditContext;
import com.hb.crm.core.beans.AopAudit.AopAuditLog;
import com.hb.crm.core.dtos.PageDto;
import com.hb.crm.core.repositories.AopAuditLogRepository;
import com.hb.crm.core.services.interfaces.AuditService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;
import java.io.PrintWriter;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Implementation of AuditService for managing audit trail operations
 */
@Service
public class AuditServiceImpl implements AuditService {

    private static final Logger logger = LoggerFactory.getLogger(AuditServiceImpl.class);

    @Autowired
    private AopAuditLogRepository aopAuditLogRepository;

    @Autowired
    private ObjectMapper objectMapper;

    @Value("${audit.retention.days:365}")
    private int defaultRetentionDays;

    @Value("${audit.archive.enabled:true}")
    private boolean archiveEnabled;

    @Value("${audit.export.max-records:10000}")
    private int maxExportRecords;

    @Override
    public AopAuditLog saveAuditLog(AopAuditLog auditLog) {
        try {
            // Set default values if not provided
            if (auditLog.getTimestamp() == null) {
                auditLog.setTimestamp(LocalDateTime.now());
            }
            if (auditLog.getRetentionDays() == null) {
                auditLog.setRetentionDays(defaultRetentionDays);
            }
            if (auditLog.getArchived() == null) {
                auditLog.setArchived(false);
            }
            if (auditLog.getContainsSensitiveData() == null) {
                auditLog.setContainsSensitiveData(false);
            }
            if (auditLog.getRequiresApproval() == null) {
                auditLog.setRequiresApproval(false);
            }

            // Set environment if not provided
            if (auditLog.getEnvironment() == null) {
                auditLog.setEnvironment(determineEnvironment());
            }

            // Set risk level based on action and status
            if (auditLog.getRiskLevel() == null) {
                auditLog.setRiskLevel(determineRiskLevel(auditLog));
            }

            return aopAuditLogRepository.save(auditLog);

        } catch (Exception e) {
            logger.error("Failed to save audit log", e);
            throw new RuntimeException("Failed to save audit log", e);
        }
    }

    @Override
    public AopAuditLog createAuditLog(AuditAction action, AuditEntityType entityType, String entityId, String userId) {
        AopAuditLog auditLog = new AopAuditLog(action, entityType, entityId, userId);
        populateFromContext(auditLog);
        return saveAuditLog(auditLog);
    }

    @Override
    public AopAuditLog createAuditLog(AuditAction action, AuditEntityType entityType, String entityId, String userId, AuditStatus status) {
        AopAuditLog auditLog = new AopAuditLog(action, entityType, entityId, userId, status);
        populateFromContext(auditLog);
        return saveAuditLog(auditLog);
    }

    @Override
    public Optional<AopAuditLog> getAuditLogById(String id) {
        try {
            return aopAuditLogRepository.findById(id);
        } catch (Exception e) {
            logger.error("Failed to get audit log by ID: " + id, e);
            return Optional.empty();
        }
    }

    @Override
    public Page<AopAuditLog> getAuditLogsByUser(String userId, Pageable pageable) {
        return aopAuditLogRepository.findByUserIdOrderByTimestampDesc(userId, pageable);
    }

    @Override
    public Page<AopAuditLog> getAuditLogsByEmployee(String employeeId, Pageable pageable) {
        return aopAuditLogRepository.findByEmployeeIdOrderByTimestampDesc(employeeId, pageable);
    }

    @Override
    public Page<AopAuditLog> getAuditLogsByEntity(AuditEntityType entityType, String entityId, Pageable pageable) {
        return aopAuditLogRepository.findByEntityTypeAndEntityIdOrderByTimestampDesc(entityType, entityId, pageable);
    }

    @Override
    public Page<AopAuditLog> getAuditLogsByAction(AuditAction action, Pageable pageable) {
        return aopAuditLogRepository.findByActionOrderByTimestampDesc(action, pageable);
    }

    @Override
    public Page<AopAuditLog> getAuditLogsByStatus(AuditStatus status, Pageable pageable) {
        return aopAuditLogRepository.findByStatusOrderByTimestampDesc(status, pageable);
    }

    @Override
    public Page<AopAuditLog> getAuditLogsByDateRange(LocalDateTime startDate, LocalDateTime endDate, Pageable pageable) {
        return aopAuditLogRepository.findByTimestampBetweenOrderByTimestampDesc(startDate, endDate, pageable);
    }

    @Override
    public Page<AopAuditLog> getAuditLogsByEntityType(AuditEntityType entityType, Pageable pageable) {
        return aopAuditLogRepository.findByEntityTypeOrderByTimestampDesc(entityType, pageable);
    }

    @Override
    public Page<AopAuditLog> getAuditLogsByModule(String moduleName, Pageable pageable) {
        return aopAuditLogRepository.findByModuleNameOrderByTimestampDesc(moduleName, pageable);
    }

    @Override
    public Page<AopAuditLog> getAuditLogsByIpAddress(String ipAddress, Pageable pageable) {
        return aopAuditLogRepository.findByIpAddressOrderByTimestampDesc(ipAddress, pageable);
    }

    @Override
    public List<AopAuditLog> getAuditLogsByCorrelationId(String correlationId) {
        return aopAuditLogRepository.findByCorrelationIdOrderByTimestampAsc(correlationId);
    }

    @Override
    public List<AopAuditLog> getAuditLogsByTransactionId(String transactionId) {
        return aopAuditLogRepository.findByTransactionIdOrderByTimestampAsc(transactionId);
    }

    @Override
    public Page<AopAuditLog> getFailedAuditLogs(Pageable pageable) {
        List<AuditStatus> failedStatuses = Arrays.asList(
            AuditStatus.FAILED, AuditStatus.SYSTEM_ERROR, AuditStatus.DATABASE_ERROR,
            AuditStatus.NETWORK_ERROR, AuditStatus.EXTERNAL_SERVICE_ERROR,
            AuditStatus.CONFIGURATION_ERROR, AuditStatus.BUSINESS_ERROR
        );
        return aopAuditLogRepository.findByStatusInOrderByTimestampDesc(failedStatuses, pageable);
    }

    @Override
    public Page<AopAuditLog> getSecurityAuditLogs(Pageable pageable) {
        return aopAuditLogRepository.findSecurityRelevantLogsOrderByTimestampDesc(pageable);
    }

    @Override
    public Page<AopAuditLog> getComplianceAuditLogs(LocalDateTime startDate, LocalDateTime endDate, Pageable pageable) {
        return aopAuditLogRepository.findComplianceLogsInDateRange(startDate, endDate, pageable);
    }

    @Override
    public Page<AopAuditLog> searchAuditLogs(
            String userSearch,
            List<AuditEntityType> entityTypes,
            List<AuditAction> actions,
            List<AuditStatus> statuses,
            LocalDateTime startDate,
            LocalDateTime endDate,
            Boolean archived,
            Pageable pageable) {
        
        return aopAuditLogRepository.findWithComplexFilters(
            userSearch != null ? userSearch : "",
            entityTypes != null && !entityTypes.isEmpty() ? entityTypes : Arrays.asList(AuditEntityType.values()),
            actions != null && !actions.isEmpty() ? actions : Arrays.asList(AuditAction.values()),
            statuses != null && !statuses.isEmpty() ? statuses : Arrays.asList(AuditStatus.values()),
            startDate != null ? startDate : LocalDateTime.now().minusYears(1),
            endDate != null ? endDate : LocalDateTime.now(),
            archived != null ? archived : false,
            pageable
        );
    }

    @Override
    public Page<AopAuditLog> searchAuditLogsByText(String searchText, Pageable pageable) {
        return aopAuditLogRepository.findByTextSearchOrderByTimestampDesc(searchText, pageable);
    }

    @Override
    public Page<AopAuditLog> getErrorAuditLogs(LocalDateTime startDate, LocalDateTime endDate, Pageable pageable) {
        return aopAuditLogRepository.findErrorLogsInDateRange(startDate, endDate, pageable);
    }

    @Override
    public List<AopAuditLog> getRecentAuditLogsForEntity(AuditEntityType entityType, String entityId) {
        return aopAuditLogRepository.findTop10ByEntityTypeAndEntityIdOrderByTimestampDesc(entityType, entityId);
    }

    @Override
    public boolean hasAuditLogs(AuditEntityType entityType, String entityId) {
        return aopAuditLogRepository.existsByEntityTypeAndEntityId(entityType, entityId);
    }

    @Override
    public Optional<AopAuditLog> getLatestAuditLogForEntity(AuditEntityType entityType, String entityId) {
        return aopAuditLogRepository.findFirstByEntityTypeAndEntityIdOrderByTimestampDesc(entityType, entityId);
    }

    @Override
    public long countAuditLogsByUser(String userId, LocalDateTime startDate, LocalDateTime endDate) {
        return aopAuditLogRepository.countByUserIdAndTimestampBetween(userId, startDate, endDate);
    }

    @Override
    public long countFailedOperationsByUser(String userId, LocalDateTime startDate, LocalDateTime endDate) {
        return aopAuditLogRepository.countByUserIdAndStatusAndTimestampBetween(userId, AuditStatus.FAILED, startDate, endDate);
    }

    @Override
    public int archiveOldAuditLogs(LocalDateTime cutoffDate) {
        if (!archiveEnabled) {
            return 0;
        }

        try {
            List<AopAuditLog> logsToArchive = aopAuditLogRepository.findLogsToArchive(cutoffDate);
            int count = 0;
            
            for (AopAuditLog log : logsToArchive) {
                log.setArchived(true);
                log.setArchivedDate(LocalDateTime.now());
                aopAuditLogRepository.save(log);
                count++;
            }
            
            logger.info("Archived {} audit logs older than {}", count, cutoffDate);
            return count;
            
        } catch (Exception e) {
            logger.error("Failed to archive old audit logs", e);
            return 0;
        }
    }

    @Override
    public int deleteArchivedAuditLogs(LocalDateTime cutoffDate) {
        try {
            // This would typically be implemented with a custom query
            // For now, we'll use a simple approach
            Page<AopAuditLog> archivedLogs = aopAuditLogRepository.findByArchivedTrueOrderByTimestampDesc(
                PageRequest.of(0, 1000)
            );
            
            int count = 0;
            for (AopAuditLog log : archivedLogs.getContent()) {
                if (log.getTimestamp().isBefore(cutoffDate)) {
                    aopAuditLogRepository.delete(log);
                    count++;
                }
            }
            
            logger.info("Deleted {} archived audit logs older than {}", count, cutoffDate);
            return count;
            
        } catch (Exception e) {
            logger.error("Failed to delete archived audit logs", e);
            return 0;
        }
    }

    /**
     * Populate audit log from current context
     */
    private void populateFromContext(AopAuditLog auditLog) {
        AuditContext context = AuditContext.getCurrentContext();
        if (context != null) {
            auditLog.setRequestId(context.getRequestId());
            auditLog.setSessionId(context.getSessionId());
            auditLog.setCorrelationId(context.getCorrelationId());
            auditLog.setTransactionId(context.getTransactionId());
            auditLog.setIpAddress(context.getIpAddress());
            auditLog.setUserAgent(context.getUserAgent());
            auditLog.setHttpMethod(context.getHttpMethod());
            auditLog.setEndpoint(context.getEndpoint());
            auditLog.setModuleName(context.getModuleName());
            auditLog.setEnvironment(context.getEnvironment());
            auditLog.setTenantId(context.getTenantId());
            auditLog.setDeviceInfo(context.getDeviceInfo());
            auditLog.setBrowserInfo(context.getBrowserInfo());
            auditLog.setOsInfo(context.getOsInfo());
            auditLog.setLocation(context.getLocation());
            auditLog.setSourceSystem(context.getSourceSystem());
            auditLog.setAutomated(context.getAutomated());
            auditLog.setMetadata(context.getMetadata());
            auditLog.setRequestParameters(context.getRequestParameters());
        }
    }

    /**
     * Determine environment based on system properties or configuration
     */
    private String determineEnvironment() {
        String profile = System.getProperty("spring.profiles.active");
        if (profile != null) {
            if (profile.contains("prod")) return "PRODUCTION";
            if (profile.contains("test")) return "TEST";
            if (profile.contains("dev")) return "DEVELOPMENT";
        }
        return "UNKNOWN";
    }

    /**
     * Determine risk level based on action and status
     */
    private String determineRiskLevel(AopAuditLog auditLog) {
        // High risk actions
        if (auditLog.getAction() == AuditAction.DELETE ||
            auditLog.getAction() == AuditAction.PERMISSION_CHANGE ||
            auditLog.getAction() == AuditAction.ROLE_CHANGE ||
            auditLog.getAction() == AuditAction.CONFIGURATION_CHANGE) {
            return "HIGH";
        }

        // Critical risk statuses
        if (auditLog.getStatus() == AuditStatus.SECURITY_VIOLATION ||
            auditLog.getStatus() == AuditStatus.UNAUTHORIZED ||
            auditLog.getStatus() == AuditStatus.FORBIDDEN) {
            return "CRITICAL";
        }

        // Medium risk actions
        if (auditLog.getAction() == AuditAction.UPDATE ||
            auditLog.getAction() == AuditAction.CREATE ||
            auditLog.getAction() == AuditAction.LOGIN ||
            auditLog.getAction() == AuditAction.PAYMENT) {
            return "MEDIUM";
        }

        return "LOW";
    }

    @Override
    public Map<String, Object> getAuditStatistics(LocalDateTime startDate, LocalDateTime endDate) {
        Map<String, Object> stats = new HashMap<>();

        try {
            // Total audit logs in date range
            long totalLogs = aopAuditLogRepository.countByTimestampBetween(startDate, endDate);
            stats.put("totalLogs", totalLogs);

            // Count by status
            Map<String, Long> statusCounts = new HashMap<>();
            for (AuditStatus status : AuditStatus.values()) {
                long count = aopAuditLogRepository.countByStatusAndTimestampBetween(status, startDate, endDate);
                if (count > 0) {
                    statusCounts.put(status.name(), count);
                }
            }
            stats.put("statusCounts", statusCounts);

            // Count by action
            Map<String, Long> actionCounts = new HashMap<>();
            for (AuditAction action : AuditAction.values()) {
                long count = aopAuditLogRepository.countByActionAndTimestampBetween(action, startDate, endDate);
                if (count > 0) {
                    actionCounts.put(action.name(), count);
                }
            }
            stats.put("actionCounts", actionCounts);

            // Count by entity type
            Map<String, Long> entityTypeCounts = new HashMap<>();
            for (AuditEntityType entityType : AuditEntityType.values()) {
                long count = aopAuditLogRepository.countByEntityTypeAndTimestampBetween(entityType, startDate, endDate);
                if (count > 0) {
                    entityTypeCounts.put(entityType.name(), count);
                }
            }
            stats.put("entityTypeCounts", entityTypeCounts);

            // Failed operations count
            long failedCount = getFailedAuditLogs(PageRequest.of(0, 1)).getTotalElements();
            stats.put("failedOperations", failedCount);

            // Security events count
            long securityCount = getSecurityAuditLogs(PageRequest.of(0, 1)).getTotalElements();
            stats.put("securityEvents", securityCount);

        } catch (Exception e) {
            logger.error("Failed to generate audit statistics", e);
            stats.put("error", "Failed to generate statistics");
        }

        return stats;
    }

    @Override
    public List<AopAuditLog> getAuditLogsBySessionId(String sessionId) {
        return aopAuditLogRepository.findBySessionIdOrderByTimestampAsc(sessionId);
    }

    @Override
    public List<AopAuditLog> getAuditLogsByRequestId(String requestId) {
        return aopAuditLogRepository.findByRequestIdOrderByTimestampAsc(requestId);
    }

    @Override
    public byte[] exportAuditLogsToCSV(
            LocalDateTime startDate,
            LocalDateTime endDate,
            List<AuditEntityType> entityTypes,
            List<AuditAction> actions) {

        try {
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            PrintWriter writer = new PrintWriter(outputStream);

            // Write CSV header
            writer.println("Timestamp,Action,EntityType,EntityId,EntityName,UserId,Username,Status,IpAddress,Description,Duration");

            // Get audit logs with pagination to avoid memory issues
            Pageable pageable = PageRequest.of(0, maxExportRecords, Sort.by("timestamp").descending());
            Page<AopAuditLog> auditLogs = searchAuditLogs(null, entityTypes, actions, null, startDate, endDate, false, pageable);

            // Write data rows
            for (AopAuditLog log : auditLogs.getContent()) {
                writer.printf("%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s%n",
                    escapeCSV(log.getTimestamp() != null ? log.getTimestamp().toString() : ""),
                    escapeCSV(log.getAction() != null ? log.getAction().toString() : ""),
                    escapeCSV(log.getEntityType() != null ? log.getEntityType().toString() : ""),
                    escapeCSV(log.getEntityId() != null ? log.getEntityId() : ""),
                    escapeCSV(log.getEntityName() != null ? log.getEntityName() : ""),
                    escapeCSV(log.getUserId() != null ? log.getUserId() : ""),
                    escapeCSV(log.getUsername() != null ? log.getUsername() : ""),
                    escapeCSV(log.getStatus() != null ? log.getStatus().toString() : ""),
                    escapeCSV(log.getIpAddress() != null ? log.getIpAddress() : ""),
                    escapeCSV(log.getDescription() != null ? log.getDescription() : ""),
                    escapeCSV(log.getDuration() != null ? log.getDuration().toString() : "")
                );
            }

            writer.flush();
            writer.close();

            return outputStream.toByteArray();

        } catch (Exception e) {
            logger.error("Failed to export audit logs to CSV", e);
            throw new RuntimeException("Failed to export audit logs to CSV", e);
        }
    }

    @Override
    public byte[] exportAuditLogsToJSON(
            LocalDateTime startDate,
            LocalDateTime endDate,
            List<AuditEntityType> entityTypes,
            List<AuditAction> actions) {

        try {
            Pageable pageable = PageRequest.of(0, maxExportRecords, Sort.by("timestamp").descending());
            Page<AopAuditLog> auditLogs = searchAuditLogs(null, entityTypes, actions, null, startDate, endDate, false, pageable);

            Map<String, Object> exportData = new HashMap<>();
            exportData.put("exportDate", LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
            exportData.put("dateRange", Map.of("start", startDate, "end", endDate));
            exportData.put("totalRecords", auditLogs.getTotalElements());
            exportData.put("auditLogs", auditLogs.getContent());

            return objectMapper.writeValueAsBytes(exportData);

        } catch (Exception e) {
            logger.error("Failed to export audit logs to JSON", e);
            throw new RuntimeException("Failed to export audit logs to JSON", e);
        }
    }

    @Override
    public PageDto<AopAuditLog> getEntityAuditTrail(AuditEntityType entityType, String entityId, int page, int size) {
        Pageable pageable = PageRequest.of(page, size, Sort.by("timestamp").descending());
        Page<AopAuditLog> auditLogs = getAuditLogsByEntity(entityType, entityId, pageable);

        PageDto<AopAuditLog> pageDto = new PageDto<>();
        pageDto.setItems(auditLogs.getContent());
        pageDto.setTotalNoOfItems(auditLogs.getTotalElements());
        pageDto.setItemsPerPage(auditLogs.getSize());
        pageDto.setPageNumber(auditLogs.getNumber());

        return pageDto;
    }

    @Override
    public Map<String, Object> getUserActivitySummary(String userId, LocalDateTime startDate, LocalDateTime endDate) {
        Map<String, Object> summary = new HashMap<>();

        try {
            long totalActions = countAuditLogsByUser(userId, startDate, endDate);
            long failedActions = countFailedOperationsByUser(userId, startDate, endDate);

            summary.put("userId", userId);
            summary.put("totalActions", totalActions);
            summary.put("failedActions", failedActions);
            summary.put("successRate", totalActions > 0 ? ((double)(totalActions - failedActions) / totalActions) * 100 : 0);
            summary.put("dateRange", Map.of("start", startDate, "end", endDate));

            // Get recent activity
            Pageable recentPageable = PageRequest.of(0, 10, Sort.by("timestamp").descending());
            Page<AopAuditLog> recentActivity = getAuditLogsByUser(userId, recentPageable);
            summary.put("recentActivity", recentActivity.getContent());

        } catch (Exception e) {
            logger.error("Failed to generate user activity summary for user: " + userId, e);
            summary.put("error", "Failed to generate summary");
        }

        return summary;
    }

    /**
     * Escape CSV values
     */
    private String escapeCSV(String value) {
        if (value == null) return "";
        if (value.contains(",") || value.contains("\"") || value.contains("\n")) {
            return "\"" + value.replace("\"", "\"\"") + "\"";
        }
        return value;
    }

    @Override
    public Map<String, Object> getSystemActivitySummary(LocalDateTime startDate, LocalDateTime endDate) {
        return getAuditStatistics(startDate, endDate);
    }

    @Override
    public AopAuditLog approveAuditLog(String auditLogId, String approvedBy) {
        Optional<AopAuditLog> optionalLog = getAuditLogById(auditLogId);
        if (optionalLog.isPresent()) {
            AopAuditLog auditLog = optionalLog.get();
            auditLog.setApprovalStatus("APPROVED");
            auditLog.setApprovedBy(approvedBy);
            auditLog.setApprovedDate(LocalDateTime.now());
            return saveAuditLog(auditLog);
        }
        throw new RuntimeException("Audit log not found: " + auditLogId);
    }

    @Override
    public AopAuditLog rejectAuditLog(String auditLogId, String rejectedBy, String reason) {
        Optional<AopAuditLog> optionalLog = getAuditLogById(auditLogId);
        if (optionalLog.isPresent()) {
            AopAuditLog auditLog = optionalLog.get();
            auditLog.setApprovalStatus("REJECTED");
            auditLog.setApprovedBy(rejectedBy);
            auditLog.setApprovedDate(LocalDateTime.now());
            if (auditLog.getMetadata() == null) {
                auditLog.setMetadata(new HashMap<>());
            }
            auditLog.getMetadata().put("rejectionReason", reason);
            return saveAuditLog(auditLog);
        }
        throw new RuntimeException("Audit log not found: " + auditLogId);
    }

    @Override
    public Page<AopAuditLog> getAuditLogsRequiringApproval(Pageable pageable) {
        return aopAuditLogRepository.findByRequiresApprovalTrueAndApprovalStatusIsNullOrderByTimestampDesc(pageable);
    }

    @Override
    public int bulkArchiveAuditLogs(List<String> auditLogIds) {
        int count = 0;
        for (String id : auditLogIds) {
            Optional<AopAuditLog> optionalLog = getAuditLogById(id);
            if (optionalLog.isPresent()) {
                AopAuditLog auditLog = optionalLog.get();
                auditLog.setArchived(true);
                auditLog.setArchivedDate(LocalDateTime.now());
                saveAuditLog(auditLog);
                count++;
            }
        }
        return count;
    }

    @Override
    public int bulkDeleteAuditLogs(List<String> auditLogIds) {
        int count = 0;
        for (String id : auditLogIds) {
            try {
                aopAuditLogRepository.deleteById(id);
                count++;
            } catch (Exception e) {
                logger.warn("Failed to delete audit log: " + id, e);
            }
        }
        return count;
    }

    @Override
    public Page<AopAuditLog> getAuditLogsByRiskLevel(String riskLevel, Pageable pageable) {
        return aopAuditLogRepository.findByRiskLevelOrderByTimestampDesc(riskLevel, pageable);
    }

    @Override
    public Page<AopAuditLog> getAuditLogsByTag(String tag, Pageable pageable) {
        return aopAuditLogRepository.findByTagsContainingOrderByTimestampDesc(tag, pageable);
    }

    @Override
    public AopAuditLog addTagsToAuditLog(String auditLogId, List<String> tags) {
        Optional<AopAuditLog> optionalLog = getAuditLogById(auditLogId);
        if (optionalLog.isPresent()) {
            AopAuditLog auditLog = optionalLog.get();
            if (auditLog.getTags() == null) {
                auditLog.setTags(new ArrayList<>());
            }
            for (String tag : tags) {
                if (!auditLog.getTags().contains(tag)) {
                    auditLog.getTags().add(tag);
                }
            }
            return saveAuditLog(auditLog);
        }
        throw new RuntimeException("Audit log not found: " + auditLogId);
    }

    @Override
    public AopAuditLog removeTagsFromAuditLog(String auditLogId, List<String> tags) {
        Optional<AopAuditLog> optionalLog = getAuditLogById(auditLogId);
        if (optionalLog.isPresent()) {
            AopAuditLog auditLog = optionalLog.get();
            if (auditLog.getTags() != null) {
                auditLog.getTags().removeAll(tags);
            }
            return saveAuditLog(auditLog);
        }
        throw new RuntimeException("Audit log not found: " + auditLogId);
    }

    @Override
    public Page<AopAuditLog> getAuditLogsWithSensitiveData(Pageable pageable) {
        return aopAuditLogRepository.findByContainsSensitiveDataTrueOrderByTimestampDesc(pageable);
    }

    @Override
    public int anonymizeUserAuditLogs(String userId) {
        Page<AopAuditLog> userLogs = getAuditLogsByUser(userId, PageRequest.of(0, 1000));
        int count = 0;

        for (AopAuditLog log : userLogs.getContent()) {
            log.setUserId("[ANONYMIZED]");
            log.setUsername("[ANONYMIZED]");
            log.setIpAddress("[ANONYMIZED]");
            log.setUserAgent("[ANONYMIZED]");
            log.setContainsSensitiveData(true);
            saveAuditLog(log);
            count++;
        }

        return count;
    }

    @Override
    public Optional<AopAuditLog> getAuditLogByExternalReference(String externalReferenceId) {
        return aopAuditLogRepository.findByExternalReferenceId(externalReferenceId);
    }

    @Override
    public AopAuditLog createManualAuditLog(
            AuditAction action,
            AuditEntityType entityType,
            String entityId,
            String description,
            String createdBy) {

        AopAuditLog auditLog = new AopAuditLog(action, entityType, entityId, createdBy);
        auditLog.setDescription(description);
        auditLog.setAutomated(false);
        auditLog.addMetadata("manualEntry", true);
        auditLog.addMetadata("createdBy", createdBy);

        return saveAuditLog(auditLog);
    }

    @Override
    public Page<AopAuditLog> getAuditLogsByEnvironment(String environment, Pageable pageable) {
        return aopAuditLogRepository.findByEnvironmentOrderByTimestampDesc(environment, pageable);
    }

    @Override
    public boolean validateAuditLogIntegrity(String auditLogId) {
        // Basic validation - in production, you might implement cryptographic verification
        Optional<AopAuditLog> optionalLog = getAuditLogById(auditLogId);
        if (optionalLog.isPresent()) {
            AopAuditLog log = optionalLog.get();
            return log.getTimestamp() != null &&
                   log.getAction() != null &&
                   log.getEntityType() != null &&
                   log.getStatus() != null;
        }
        return false;
    }

    @Override
    public List<AopAuditLog> getComplianceReport(
            LocalDateTime startDate,
            LocalDateTime endDate,
            List<String> complianceFlags) {

        Page<AopAuditLog> complianceLogs = getComplianceAuditLogs(startDate, endDate,
            PageRequest.of(0, maxExportRecords));

        return complianceLogs.getContent().stream()
            .filter(log -> log.getComplianceFlags() != null &&
                          log.getComplianceFlags().stream().anyMatch(complianceFlags::contains))
            .collect(Collectors.toList());
    }
}
