package com.hb.crm.core.dtos.notification;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class GroupedEmployeeNotificationsDto {
    private List<EmployeeNotificationDto> todayNotifications;
    private List<EmployeeNotificationDto> yesterdayNotifications;
    private List<EmployeeNotificationDto> olderNotifications;
    private long totalNoOfItems;
    private int pageNumber;
    private int itemsPerPage;
}
