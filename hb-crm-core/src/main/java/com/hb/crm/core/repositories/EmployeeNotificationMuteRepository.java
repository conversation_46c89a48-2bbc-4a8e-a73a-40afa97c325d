package com.hb.crm.core.repositories;

import com.hb.crm.core.Enums.NotificationEntityType;
import com.hb.crm.core.beans.Notification.EmployeeNotificationMute;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface EmployeeNotificationMuteRepository extends MongoRepository<EmployeeNotificationMute, String> {

    /**
     * Find all notification mutes for a specific employee
     */
    List<EmployeeNotificationMute> findByEmployeeId(String employeeId);

    /**
     * Find all notification mutes for a specific employee with pagination
     */
    Page<EmployeeNotificationMute> findByEmployeeId(String employeeId, Pageable pageable);

    /**
     * Find all notification mutes for a specific employee and entity type
     */
    List<EmployeeNotificationMute> findByEmployeeIdAndEntityType(String employeeId, NotificationEntityType entityType);

    /**
     * Find all notification mutes for a specific employee and entity type with pagination
     */
    Page<EmployeeNotificationMute> findByEmployeeIdAndEntityType(String employeeId, NotificationEntityType entityType, Pageable pageable);

    /**
     * Find a specific notification mute by employee, entity ID and entity type
     */
    Optional<EmployeeNotificationMute> findByEmployeeIdAndEntityIdAndEntityType(
            String employeeId, String entityId, NotificationEntityType entityType);

    /**
     * Check if an employee mutes a specific entity
     */
    boolean existsByEmployeeIdAndEntityIdAndEntityType(String employeeId, String entityId, NotificationEntityType entityType);

    /**
     * Delete all notification mutes for a specific employee and entity type
     */
    void deleteByEmployeeIdAndEntityType(String employeeId, NotificationEntityType entityType);

    /**
     * Delete a specific notification mute
     */
    void deleteByEmployeeIdAndEntityIdAndEntityType(String employeeId, String entityId, NotificationEntityType entityType);
}
