package com.hb.crm.core.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.UUID;

public class ApplicationUtil {

	private static final Logger log = LoggerFactory.getLogger(ApplicationUtil.class);

	public static Pageable createPageRequest(int pageNumber, int  itemsPerPage, String sortColumn, String sortOrder) {
		return createPageRequest(pageNumber, itemsPerPage, null, sortColumn, sortOrder);
	}

	public static Pageable createPageRequest(int pageNumber, int  itemsPerPage, String sortEntity, String sortColumn, String sortOrder) {

		String sortEntityColumn = sortColumn;
		if(sortEntity!=null && !sortEntity.trim().isEmpty()){
			sortEntityColumn = sortEntity + "." + sortColumn;
		}

		if (sortColumn != null && !sortColumn.isEmpty()) {
			Sort sort = Sort.by(sortEntityColumn).ascending();
			if("DESC".equalsIgnoreCase(sortOrder)){
				sort = Sort.by(sortEntityColumn).descending();
			}
			return PageRequest.of(pageNumber, itemsPerPage,	sort);
		}else{
			return PageRequest.of(pageNumber, itemsPerPage);
		}
	}


	public static String formatString(String inputString) {
		if (inputString == null) {
			return "";
		} else
			return inputString.trim();
	}


	public static UUID getUUIDFromSTring(String uuid) {
		try{
			return UUID.fromString(uuid);
		} catch (IllegalArgumentException e){
			log.error("error in git uuis from string uuid{}", uuid );
		}
		return null;
	}


	public static HashMap<String, ArrayList<String>> filterParamsFormat(HashMap<String, String> paramsMap) {
		HashMap<String, ArrayList<String>> filters = new HashMap<>();
		paramsMap.forEach((key, val) -> {
			if(val != null) {
				String[] valStripped = val.split(",", Integer.MAX_VALUE);
				if(valStripped.length > 0) {
					filters.put(key, new ArrayList<String>(Arrays.asList(valStripped)));
				} else {
					filters.put(key, null);
				}
			}
		});

		return filters;
	}

}
