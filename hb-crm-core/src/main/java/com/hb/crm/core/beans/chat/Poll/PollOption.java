package com.hb.crm.core.beans.chat.Poll;

import lombok.Getter;
import lombok.Setter;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@Document
public class PollOption {

    @Id
    String id;
    String answer;
    List<Vote> votes = new ArrayList<>();
    double percentage;
    
    public int getVoteCount() {
        return votes != null ? votes.size() : 0;
    }
}
