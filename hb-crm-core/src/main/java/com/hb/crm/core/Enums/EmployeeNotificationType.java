package com.hb.crm.core.Enums;

public enum EmployeeNotificationType {

    // Package notifications
    CREATE_PACKAGE_ADMIN,
    CREATE_PACKAGE_INFLUENCER,
    PACKAGE_ACTIVATED,
    PACKA<PERSON>_EXPIRED,
    USER_SUBSCRIBE_PACKAGE,
    PAC<PERSON><PERSON>_FULL,
    PACKAGE_UPDATE_REQUEST,

    // Post notifications
    POST_REPORTED,

    // Login & Password notifications
    LOGIN_SUCCEEDED,
    LOGIN_FAILED,
    PASSWORD_CHANGED,
    PASSWORD_CHANGE_FAILED,

    // User notifications
    NEW_ADMIN_JOINED,
    NEW_INFLUENCER_JOINED,
    NEW_TRAVELER_CREATED,
    ACCOUNT_ACTIVATED,
    ACCOUNT_DEACTIVATED,

    // Report notification











}
