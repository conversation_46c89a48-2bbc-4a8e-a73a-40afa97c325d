package com.hb.crm.core.services;

import com.hb.crm.core.beans.AuditLog.AuditLog;
import com.hb.crm.core.dtos.PageDto;
import com.hb.crm.core.repositories.AuditLogRepository;
import com.hb.crm.core.repositories.custom.AuditLogCustomRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;


@Service
public class AuditLogService {

    @Autowired
    private AuditLogRepository auditLogRepository;

    @Autowired
    private AuditLogCustomRepository auditLogCustomRepository;

    @Transactional(readOnly = true)
    public PageDto<AuditLog> searchAuditLogs(
            String entityName,
            String entityId,
            String actionType,
            String userEmail,
            String userId,
            String userName,
            String ipAddress,
            LocalDateTime fromDate,
            LocalDateTime toDate,
            String newValueKey,
            String newValueSearchValue,
            String oldValueKey,
            String oldValueSearchValue,
            int page,
            int pageSize,
            String orderBy,
            Sort.Direction orderDirection
    ) {
        Pageable pageable = PageRequest.of(page, pageSize, Sort.by(orderDirection, orderBy));
        Page<AuditLog> logsPage;
        logsPage = auditLogCustomRepository.searchAuditLogs(
                entityName,
                entityId,
                actionType,
                userEmail,
                userId,
                userName,
                ipAddress,
                fromDate,
                toDate,
                newValueKey,
                newValueSearchValue,
                oldValueKey,
                oldValueSearchValue,
                pageable
        );

        return new PageDto<>(pageSize, logsPage.getTotalElements(), page, logsPage.getContent());
    }
}