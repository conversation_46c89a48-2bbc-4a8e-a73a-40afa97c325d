package com.hb.crm.core.repositories;

import com.hb.crm.core.Enums.AuditAction;
import com.hb.crm.core.Enums.AuditEntityType;
import com.hb.crm.core.Enums.AuditStatus;
import com.hb.crm.core.beans.AopAudit.AopAuditLog;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Repository interface for AuditLog entity
 * Provides methods for querying audit logs with various filters
 */
@Repository
public interface AopAuditLogRepository extends MongoRepository<AopAuditLog, String> {

    /**
     * Find audit logs by user ID
     */
    Page<AopAuditLog> findByUserIdOrderByTimestampDesc(String userId, Pageable pageable);

    /**
     * Find audit logs by employee ID
     */
    Page<AopAuditLog> findByEmployeeIdOrderByTimestampDesc(String employeeId, Pageable pageable);

    /**
     * Find audit logs by entity type and entity ID
     */
    Page<AopAuditLog> findByEntityTypeAndEntityIdOrderByTimestampDesc(
            AuditEntityType entityType, String entityId, Pageable pageable);

    /**
     * Find audit logs by action
     */
    Page<AopAuditLog> findByActionOrderByTimestampDesc(AuditAction action, Pageable pageable);

    /**
     * Find audit logs by status
     */
    Page<AopAuditLog> findByStatusOrderByTimestampDesc(AuditStatus status, Pageable pageable);

    /**
     * Find audit logs by date range
     */
    Page<AopAuditLog> findByTimestampBetweenOrderByTimestampDesc(
            LocalDateTime startDate, LocalDateTime endDate, Pageable pageable);

    /**
     * Find audit logs by user and date range
     */
    Page<AopAuditLog> findByUserIdAndTimestampBetweenOrderByTimestampDesc(
            String userId, LocalDateTime startDate, LocalDateTime endDate, Pageable pageable);

    /**
     * Find audit logs by entity type
     */
    Page<AopAuditLog> findByEntityTypeOrderByTimestampDesc(AuditEntityType entityType, Pageable pageable);

    /**
     * Find audit logs by module name
     */
    Page<AopAuditLog> findByModuleNameOrderByTimestampDesc(String moduleName, Pageable pageable);

    /**
     * Find audit logs by IP address
     */
    Page<AopAuditLog> findByIpAddressOrderByTimestampDesc(String ipAddress, Pageable pageable);

    /**
     * Find audit logs by correlation ID
     */
    List<AopAuditLog> findByCorrelationIdOrderByTimestampAsc(String correlationId);

    /**
     * Find audit logs by transaction ID
     */
    List<AopAuditLog> findByTransactionIdOrderByTimestampAsc(String transactionId);

    /**
     * Find audit logs by parent audit ID
     */
    List<AopAuditLog> findByParentAuditIdOrderByTimestampAsc(String parentAuditId);

    /**
     * Find failed audit logs
     */
    Page<AopAuditLog> findByStatusInOrderByTimestampDesc(List<AuditStatus> statuses, Pageable pageable);

    /**
     * Find audit logs with sensitive data
     */
    Page<AopAuditLog> findByContainsSensitiveDataTrueOrderByTimestampDesc(Pageable pageable);

    /**
     * Find audit logs requiring approval
     */
    Page<AopAuditLog> findByRequiresApprovalTrueAndApprovalStatusIsNullOrderByTimestampDesc(Pageable pageable);

    /**
     * Find archived audit logs
     */
    Page<AopAuditLog> findByArchivedTrueOrderByTimestampDesc(Pageable pageable);

    /**
     * Find non-archived audit logs
     */
    Page<AopAuditLog> findByArchivedFalseOrderByTimestampDesc(Pageable pageable);

    /**
     * Find audit logs by environment
     */
    Page<AopAuditLog> findByEnvironmentOrderByTimestampDesc(String environment, Pageable pageable);

    /**
     * Find audit logs by risk level
     */
    Page<AopAuditLog> findByRiskLevelOrderByTimestampDesc(String riskLevel, Pageable pageable);

    /**
     * Find audit logs by tags
     */
    Page<AopAuditLog> findByTagsContainingOrderByTimestampDesc(String tag, Pageable pageable);

    /**
     * Complex search query with multiple filters
     */
    @Query("{ $and: [ " +
            "{ $or: [ { 'userId': { $regex: ?0, $options: 'i' } }, { 'username': { $regex: ?0, $options: 'i' } } ] }, " +
            "{ 'entityType': { $in: ?1 } }, " +
            "{ 'action': { $in: ?2 } }, " +
            "{ 'status': { $in: ?3 } }, " +
            "{ 'timestamp': { $gte: ?4, $lte: ?5 } }, " +
            "{ 'archived': ?6 } " +
            "] }")
    Page<AopAuditLog> findWithComplexFilters(
            String userSearch,
            List<AuditEntityType> entityTypes,
            List<AuditAction> actions,
            List<AuditStatus> statuses,
            LocalDateTime startDate,
            LocalDateTime endDate,
            Boolean archived,
            Pageable pageable);

    /**
     * Search audit logs by text in description or details
     */
    @Query("{ $or: [ " +
            "{ 'description': { $regex: ?0, $options: 'i' } }, " +
            "{ 'details': { $regex: ?0, $options: 'i' } }, " +
            "{ 'entityName': { $regex: ?0, $options: 'i' } } " +
            "] }")
    Page<AopAuditLog> findByTextSearchOrderByTimestampDesc(String searchText, Pageable pageable);

    /**
     * Find audit logs for security analysis
     */
    @Query("{ $or: [ " +
            "{ 'action': { $in: ['LOGIN', 'LOGOUT', 'PASSWORD_CHANGE', 'PERMISSION_CHANGE', 'ROLE_CHANGE'] } }, " +
            "{ 'status': { $in: ['UNAUTHORIZED', 'FORBIDDEN', 'SECURITY_VIOLATION'] } }, " +
            "{ 'riskLevel': { $in: ['HIGH', 'CRITICAL'] } } " +
            "] }")
    Page<AopAuditLog> findSecurityRelevantLogsOrderByTimestampDesc(Pageable pageable);

    /**
     * Find audit logs for compliance reporting
     */
    @Query("{ $and: [ " +
            "{ 'timestamp': { $gte: ?0, $lte: ?1 } }, " +
            "{ 'complianceFlags': { $exists: true, $ne: [] } } " +
            "] }")
    Page<AopAuditLog> findComplianceLogsInDateRange(
            LocalDateTime startDate, LocalDateTime endDate, Pageable pageable);

    /**
     * Count audit logs by user in date range
     */
    long countByUserIdAndTimestampBetween(String userId, LocalDateTime startDate, LocalDateTime endDate);

    /**
     * Count failed operations by user in date range
     */
    long countByUserIdAndStatusAndTimestampBetween(
            String userId, AuditStatus status, LocalDateTime startDate, LocalDateTime endDate);

    /**
     * Find audit logs that need to be archived (older than retention period)
     */
    @Query("{ $and: [ " +
            "{ 'archived': false }, " +
            "{ 'timestamp': { $lt: ?0 } } " +
            "] }")
    List<AopAuditLog> findLogsToArchive(LocalDateTime cutoffDate);

    /**
     * Find audit logs by external reference ID
     */
    Optional<AopAuditLog> findByExternalReferenceId(String externalReferenceId);

    /**
     * Find recent audit logs for a specific entity
     */
    List<AopAuditLog> findTop10ByEntityTypeAndEntityIdOrderByTimestampDesc(
            AuditEntityType entityType, String entityId);

    /**
     * Find audit logs by session ID
     */
    List<AopAuditLog> findBySessionIdOrderByTimestampAsc(String sessionId);

    /**
     * Find audit logs by request ID
     */
    List<AopAuditLog> findByRequestIdOrderByTimestampAsc(String requestId);

    /**
     * Check if entity has any audit logs
     */
    boolean existsByEntityTypeAndEntityId(AuditEntityType entityType, String entityId);

    /**
     * Find latest audit log for an entity
     */
    Optional<AopAuditLog> findFirstByEntityTypeAndEntityIdOrderByTimestampDesc(
            AuditEntityType entityType, String entityId);

    /**
     * Find audit logs with errors in date range
     */
    @Query("{ $and: [ " +
            "{ 'timestamp': { $gte: ?0, $lte: ?1 } }, " +
            "{ 'errorMessage': { $exists: true, $ne: null } } " +
            "] }")
    Page<AopAuditLog> findErrorLogsInDateRange(
            LocalDateTime startDate, LocalDateTime endDate, Pageable pageable);

    /**
     * Count methods for statistics
     */
    long countByTimestampBetween(LocalDateTime startDate, LocalDateTime endDate);

    long countByStatusAndTimestampBetween(AuditStatus status, LocalDateTime startDate, LocalDateTime endDate);

    long countByActionAndTimestampBetween(AuditAction action, LocalDateTime startDate, LocalDateTime endDate);

    long countByEntityTypeAndTimestampBetween(AuditEntityType entityType, LocalDateTime startDate, LocalDateTime endDate);
}
