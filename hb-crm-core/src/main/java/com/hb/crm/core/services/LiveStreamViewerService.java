package com.hb.crm.core.services;

import com.hb.crm.core.beans.LiveStream.LiveStream;
import com.hb.crm.core.beans.User;
import com.hb.crm.core.dtos.LiveStream.ViewerCountUpdateDto;
import com.hb.crm.core.dtos.LiveStream.CurrentViewerDto;
import com.hb.crm.core.dtos.PageDto;
import com.hb.crm.core.repositories.LiveStream.LiveStreamRepository;
import com.hb.crm.core.repositories.UserRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class LiveStreamViewerService 
{

    private final LiveStreamRepository liveStreamRepository;
    private final UserRepository userRepository;
    private final LiveStreamSessionManager sessionManager;
    
    // In-memory storage for active viewers (you might want to use Redis for production)
    private final Map<String, Set<String>> activeViewers = new ConcurrentHashMap<>();

    public ViewerCountUpdateDto addViewer(String liveStreamId, String userId) 
    {
        
        // Add viewer to active viewers set
        activeViewers.computeIfAbsent(liveStreamId, k -> ConcurrentHashMap.newKeySet()).add(userId);
                
        // Update database
        int viewerCount = updateViewerCountInDatabase(liveStreamId);
        
        ViewerCountUpdateDto result = new ViewerCountUpdateDto(liveStreamId, viewerCount, "JOIN");
        
        return result;
    }

    public ViewerCountUpdateDto removeViewer(String liveStreamId, String userId) 
    {        
        // Remove viewer from active viewers set
        Set<String> viewers = activeViewers.get(liveStreamId);
        if (viewers != null) {
            viewers.remove(userId);
            if (viewers.isEmpty()) 
            {
                activeViewers.remove(liveStreamId);
            }
        }
        
        System.out.println("👥 Active viewers for stream " + liveStreamId + ": " + activeViewers.get(liveStreamId));
        
        // Update database
        int viewerCount = updateViewerCountInDatabase(liveStreamId);
       
        ViewerCountUpdateDto result = new ViewerCountUpdateDto(liveStreamId, viewerCount, "LEAVE");
        System.out.println("📉 Viewer count result: " + result);
        
        return result;
    }

    private int updateViewerCountInDatabase(String liveStreamId) 
    {
        System.out.println("💾 Updating database for stream: " + liveStreamId);
        
        try {
            LiveStream liveStream = liveStreamRepository.findById(liveStreamId)
                    .orElseThrow(() -> new IllegalArgumentException("Live stream not found"));
            
            System.out.println("✅ Found live stream: " + liveStream.getId() + " - " + liveStream.getTitle());
            
            Set<String> viewers = activeViewers.get(liveStreamId);
            int viewerCount = viewers != null ? viewers.size() : 0;
            
            System.out.println("📊 Calculated viewer count: " + viewerCount);
            System.out.println("📊 Previous viewer count: " + liveStream.getViewersCount());
            
            liveStream.setViewersCount(viewerCount);
            liveStreamRepository.save(liveStream);
            
            System.out.println("💾 Database updated successfully with count: " + viewerCount);
            
            return viewerCount;
        } catch (Exception e) {
            System.err.println("❌ Error updating database: " + e.getMessage());
            e.printStackTrace();
            throw e;
        }
    }

    public int getCurrentViewerCount(String liveStreamId) 
    {
        Set<String> viewers = activeViewers.get(liveStreamId).stream().filter(v -> !sessionManager.getSessionInfo(v).isStreamer()).collect(Collectors.toSet());
        return viewers != null ? viewers.size() : 0;
    }
    
    /**
     * Get current viewers with pagination using session manager for more accurate data
     */
    public PageDto<CurrentViewerDto> getCurrentViewers(String liveStreamId, int page, int size) 
    {
        try 
        {
            // Get all active sessions for the stream from session manager
            Set<String> sessionIds = sessionManager.getStreamSessions(liveStreamId);
            
            if (sessionIds == null || sessionIds.isEmpty()) 
            {
                return createEmptyPageDto(page, size);
            }
            
            // Get session info for each session and collect viewer data
            List<CurrentViewerDto> allViewers = new ArrayList<>();
            
            for (String sessionId : sessionIds) 
            {
                LiveStreamSessionManager.SessionInfo sessionInfo = sessionManager.getSessionInfo(sessionId);
                if (sessionInfo != null) {
                    try 
                    {
                        // Get user details from database
                        Optional<User> userOpt = userRepository.findById(sessionInfo.getUserId());
                        if (userOpt.isPresent() && !sessionInfo.isStreamer()) 
                        {
                            User user = userOpt.get();
                            
                            CurrentViewerDto viewerDto = new CurrentViewerDto(
                                sessionInfo.getUserId(),
                                sessionInfo.getUsername(),
                                user.getFirstName(),
                                user.getLastName(),
                                user.getProfileImage(),
                                sessionInfo.isStreamer()
                            );
                            
                            allViewers.add(viewerDto);
                        }
                    } 
                    catch (Exception e) 
                    {
                        System.err.println("Error getting user details for session " + sessionId + ": " + e.getMessage());
                    }
                }
            }
            
            // Sort viewers - streamers first, then by username
            allViewers.sort((a, b) ->
            {
                if (a.isStreamer() != b.isStreamer()) {
                    return a.isStreamer() ? -1 : 1; // Streamers first
                }
                return a.getUsername().compareToIgnoreCase(b.getUsername());
            });
            
            // Apply pagination
            int totalItems = allViewers.size();
            int startIndex = page * size;
            int endIndex = Math.min(startIndex + size, totalItems);
            
            List<CurrentViewerDto> paginatedViewers;
            if (startIndex >= totalItems) 
            {
                paginatedViewers = Collections.emptyList();
            } 
            else 
            {
                paginatedViewers = allViewers.subList(startIndex, endIndex);
            }
            
            PageDto<CurrentViewerDto> pageDto = new PageDto<>();
            pageDto.setItems(paginatedViewers);
            pageDto.setTotalNoOfItems(totalItems);
            pageDto.setPageNumber(page);
            pageDto.setItemsPerPage(size);
            
            return pageDto;
            
        } 
        catch (Exception e) 
        {
            System.err.println("Error getting current viewers: " + e.getMessage());
            e.printStackTrace();
            return createEmptyPageDto(page, size);
        }
    }
    
    private PageDto<CurrentViewerDto> createEmptyPageDto(int page, int size) 
    {
        PageDto<CurrentViewerDto> pageDto = new PageDto<>();
        pageDto.setItems(Collections.emptyList());
        pageDto.setTotalNoOfItems(0);
        pageDto.setPageNumber(page);
        pageDto.setItemsPerPage(size);
        return pageDto;
    }
}