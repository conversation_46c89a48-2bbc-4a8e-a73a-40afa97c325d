package com.hb.crm.core.dtos.LiveStream;

import lombok.Data;

import java.time.LocalDateTime;

import com.hb.crm.core.Enums.LiveStreamStatus;
import com.hb.crm.core.Enums.Mp4ConversionStatus;

@Data
public class LiveStreamResponseDto {
    private String id;
    private String title;
    private String influencerId;
    private String influencerName;
    private String influencerUsername;
    private String packageId;
    private String packageName;
    private String playbackUrl;
    private String channelArn;
    private String streamKey;
    private String ingestEndpoint;
    private LiveStreamStatus status;
    private LocalDateTime createdAt;
    private LocalDateTime endedAt;
    private int numberOfReactions = 0;
    private int numberOfComments = 0;
    private int viewersCount = 0;
    private boolean isStreaming;
    
    // Privacy control
    private boolean isPrivate = false;

    public boolean getIsStreaming()
    {
        return isStreaming;
    }
    public void setIsStreaming(boolean Streaming)
    {
        this.isStreaming = Streaming;
    }
    
    // MP4 Conversion fields
    private Mp4ConversionStatus mp4ConversionStatus;
    private String mp4Key; // null until conversion finished
    private String thumbnailClipUrl;
    private String thumbnailImageUrl;
}