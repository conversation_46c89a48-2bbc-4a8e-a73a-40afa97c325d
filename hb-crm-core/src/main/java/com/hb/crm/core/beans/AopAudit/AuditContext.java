package com.hb.crm.core.beans.AopAudit;

import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * Context class to hold audit information during request processing
 * Uses ThreadLocal to maintain context per request thread
 */
@Getter
@Setter
public class AuditContext {

    private String requestId;
    private String sessionId;
    private String correlationId;
    private String transactionId;
    private String userId;
    private String username;
    private String userType;
    private String employeeId;
    private String ipAddress;
    private String userAgent;
    private String httpMethod;
    private String endpoint;
    private String moduleName;
    private String environment;
    private String tenantId;
    private LocalDateTime requestStartTime;
    private Map<String, Object> metadata;
    private Map<String, Object> requestParameters;
    private String deviceInfo;
    private String browserInfo;
    private String osInfo;
    private String location;
    private String sourceSystem;
    private Boolean automated;

    // ThreadLocal to maintain context per request
    private static final ThreadLocal<AuditContext> contextHolder = new ThreadLocal<>();

    public AuditContext() {
        this.requestId = UUID.randomUUID().toString();
        this.correlationId = UUID.randomUUID().toString();
        this.requestStartTime = LocalDateTime.now();
        this.metadata = new HashMap<>();
        this.requestParameters = new HashMap<>();
        this.automated = false;
    }

    /**
     * Get current audit context
     */
    public static AuditContext getCurrentContext() {
        return contextHolder.get();
    }

    /**
     * Set current audit context
     */
    public static void setCurrentContext(AuditContext context) {
        contextHolder.set(context);
    }

    /**
     * Clear current audit context
     */
    public static void clearCurrentContext() {
        contextHolder.remove();
    }

    /**
     * Create a new audit context
     */
    public static AuditContext createContext() {
        AuditContext context = new AuditContext();
        setCurrentContext(context);
        return context;
    }

    /**
     * Create a new audit context with user information
     */
    public static AuditContext createContext(String userId, String username, String userType) {
        AuditContext context = createContext();
        context.setUserId(userId);
        context.setUsername(username);
        context.setUserType(userType);
        return context;
    }

    /**
     * Create a new audit context with employee information
     */
    public static AuditContext createEmployeeContext(String employeeId, String username) {
        AuditContext context = createContext();
        context.setEmployeeId(employeeId);
        context.setUsername(username);
        context.setUserType("Employee");
        return context;
    }

    /**
     * Add metadata to the context
     */
    public void addMetadata(String key, Object value) {
        if (this.metadata == null) {
            this.metadata = new HashMap<>();
        }
        this.metadata.put(key, value);
    }

    /**
     * Add request parameter to the context
     */
    public void addRequestParameter(String key, Object value) {
        if (this.requestParameters == null) {
            this.requestParameters = new HashMap<>();
        }
        this.requestParameters.put(key, value);
    }

    /**
     * Get metadata value
     */
    public Object getMetadata(String key) {
        return this.metadata != null ? this.metadata.get(key) : null;
    }

    /**
     * Get request parameter value
     */
    public Object getRequestParameter(String key) {
        return this.requestParameters != null ? this.requestParameters.get(key) : null;
    }

    /**
     * Check if context has user information
     */
    public boolean hasUserInfo() {
        return userId != null || employeeId != null;
    }

    /**
     * Get effective user ID (userId or employeeId)
     */
    public String getEffectiveUserId() {
        return userId != null ? userId : employeeId;
    }

    /**
     * Get effective user type
     */
    public String getEffectiveUserType() {
        if (userType != null) {
            return userType;
        }
        return employeeId != null ? "Employee" : "User";
    }

    /**
     * Calculate request duration in milliseconds
     */
    public Long getRequestDuration() {
        if (requestStartTime == null) {
            return null;
        }
        return java.time.Duration.between(requestStartTime, LocalDateTime.now()).toMillis();
    }

    /**
     * Create a child context for nested operations
     */
    public AuditContext createChildContext() {
        AuditContext childContext = new AuditContext();
        
        // Copy parent context information
        childContext.setSessionId(this.sessionId);
        childContext.setCorrelationId(this.correlationId);
        childContext.setUserId(this.userId);
        childContext.setUsername(this.username);
        childContext.setUserType(this.userType);
        childContext.setEmployeeId(this.employeeId);
        childContext.setIpAddress(this.ipAddress);
        childContext.setUserAgent(this.userAgent);
        childContext.setHttpMethod(this.httpMethod);
        childContext.setModuleName(this.moduleName);
        childContext.setEnvironment(this.environment);
        childContext.setTenantId(this.tenantId);
        childContext.setDeviceInfo(this.deviceInfo);
        childContext.setBrowserInfo(this.browserInfo);
        childContext.setOsInfo(this.osInfo);
        childContext.setLocation(this.location);
        childContext.setSourceSystem(this.sourceSystem);
        childContext.setAutomated(this.automated);
        
        // Copy metadata
        if (this.metadata != null) {
            childContext.setMetadata(new HashMap<>(this.metadata));
        }
        
        return childContext;
    }

    /**
     * Convert context to string for logging
     */
    @Override
    public String toString() {
        return "AuditContext{" +
                "requestId='" + requestId + '\'' +
                ", sessionId='" + sessionId + '\'' +
                ", correlationId='" + correlationId + '\'' +
                ", userId='" + userId + '\'' +
                ", username='" + username + '\'' +
                ", userType='" + userType + '\'' +
                ", employeeId='" + employeeId + '\'' +
                ", ipAddress='" + ipAddress + '\'' +
                ", httpMethod='" + httpMethod + '\'' +
                ", endpoint='" + endpoint + '\'' +
                ", moduleName='" + moduleName + '\'' +
                '}';
    }
}
