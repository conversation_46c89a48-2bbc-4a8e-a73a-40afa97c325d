package com.hb.crm.core.repositories;

import com.hb.crm.core.Enums.ReactionType;
import com.hb.crm.core.beans.Blog;
import com.hb.crm.core.beans.BlogReaction;
import com.hb.crm.core.beans.User;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.Aggregation;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface BlogReactionRepository extends MongoRepository<BlogReaction, String> {

    Optional<BlogReaction> findByBlogAndUser(Blog blog, User user);
    
    List<BlogReaction> findByBlogOrderByCreateDateDesc(Blog blog);
    Page<BlogReaction> findByBlogOrderByCreateDateDesc(Blog blog, Pageable pageable);
    
    List<BlogReaction> findByBlogAndReactionTypeOrderByCreateDateDesc(Blog blog, ReactionType reactionType);
    
    long countByBlog(Blog blog);
    long countByBlogAndReactionType(Blog blog, ReactionType reactionType);
    
    boolean existsByBlogAndUser(Blog blog, User user);
    
    void deleteByBlogAndUser(Blog blog, User user);
    
    // Aggregation to get reaction counts by type for a blog
    @Aggregation(pipeline = {
        "{ $match: { 'blog.$id': ?0 } }",
        "{ $group: { " +
        "  '_id': '$reactionType', " +
        "  'count': { $sum: 1 } " +
        "} }",
        "{ $sort: { 'count': -1 } }"
    })
    List<ReactionCountDto> getReactionCountsByBlog(String blogId);
    
    // Interface for reaction count aggregation result
    interface ReactionCountDto {
        ReactionType get_id();
        long getCount();
    }
}
