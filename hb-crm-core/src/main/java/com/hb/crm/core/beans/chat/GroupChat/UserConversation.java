package com.hb.crm.core.beans.chat.GroupChat;

import com.hb.crm.core.beans.User;
import lombok.Getter;
import lombok.Setter;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.DBRef;
import org.springframework.data.mongodb.core.mapping.Document;

import java.time.LocalDateTime;

@Getter
@Setter
@Document
public class UserConversation {

    @Id
    private String id;
    private boolean isMuted = false;

    @DBRef(lazy = true)
    private User user;

    @DBRef(lazy = true)
    private GroupConversation conversation;

    private LocalDateTime startTime;
    private LocalDateTime endTime;
}
