package com.hb.crm.core.dtos;

import com.hb.crm.core.Enums.Gender;
import com.hb.crm.core.beans.Role;
import lombok.Data;
import java.util.List;

@Data
public class EmployeeLightDto {

    private String id;
    private String username;
    private String fullName;
    private String mobile;
    private String profilePicture;
    private String email;
    private List<Role> roles;
    private Gender gender;
}
