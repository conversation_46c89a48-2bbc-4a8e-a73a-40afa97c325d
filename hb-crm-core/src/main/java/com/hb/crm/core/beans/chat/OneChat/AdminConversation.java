package com.hb.crm.core.beans.chat.OneChat;

import com.hb.crm.core.beans.Employee;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.DBRef;
import org.springframework.data.mongodb.core.mapping.Document;

import java.time.LocalDateTime;

@Getter
@Setter
@Document
@NoArgsConstructor
@AllArgsConstructor
public class AdminConversation {

    @Id
    String id;
    @DBRef(lazy = true)
    Employee employee;
    @DBRef(lazy = true)
    Conversation conversation;

    LocalDateTime startTime;
    LocalDateTime endTime;
    Integer happinessRate;
}
