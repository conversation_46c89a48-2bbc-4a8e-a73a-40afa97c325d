package com.hb.crm.core.beans.chat.OneChat;

import com.hb.crm.core.Enums.ReactionType;
import com.hb.crm.core.Enums.chat.ChatMessageStatus;
import com.hb.crm.core.Enums.chat.ChatMessageType;
import com.hb.crm.core.beans.User;
import com.hb.crm.core.beans.chat.Poll.Poll;
import lombok.Getter;
import lombok.Setter;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.DBRef;
import org.springframework.data.mongodb.core.mapping.Document;

import java.time.LocalDateTime;
import java.util.List;

@Setter
@Getter
@Document
public class ChatMessage {

    @Id
    String id;
    String url;
    String text;
    String replyId;
    ChatMessageType type;
    LocalDateTime dateTime;
    ChatMessageStatus status;
    boolean deletedByMe = false;
    List<ReactionType> reaction;

    boolean pinned;
    LocalDateTime pinExpiryDate;

    Poll poll;

    @DBRef(lazy = true)
    Conversation conversation;

    @DBRef(lazy = true)
    User user;
}
