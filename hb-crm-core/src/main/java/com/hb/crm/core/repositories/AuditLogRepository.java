package com.hb.crm.core.repositories;


import com.hb.crm.core.beans.AuditLog.AuditLog;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;

import java.time.LocalDateTime;


public interface AuditLogRepository extends MongoRepository<AuditLog, Long> {

    @Query("{ " +
            "$and: [" +
            "  { $or: [ { ?0: null }, { entityName: ?0 } ] }," +
            "  { $or: [ { ?1: null }, { entityId: ?1 } ] }," +
            "  { $or: [ { ?2: null }, { actionType: ?2 } ] }," +
            "  { $or: [ { ?3: null }, { userEmail: { $regex: ?3, $options: 'i' } } ] }," +
            "  { $or: [ { ?4: null }, { userId: ?4 } ] }," +
            "  { $or: [ { ?5: null }, { userName: { $regex: ?5, $options: 'i' } } ] }," +
            "  { $or: [ { ?6: null }, { ipAddress: { $regex: ?6, $options: 'i' } } ] }," +
            "  { $or: [" +
            "       { $and: [ { ?7: null }, { ?8: null } ] }, " +
            "       { $and: [ { createdAt: { $gte: ?7 } }, { createdAt: { $lte: ?8 } } ] }, " +
            "       { createdAt: { $gte: ?7 } }, " +
            "       { createdAt: { $lte: ?8 } }" +
            "    ]" +
            "  }," +
            "  { $or: [" +
            "       { ?9: null }, " +
            "       { $and: [ { ?10: { $exists: true } }, { newValue.?10: { $regex: ?9, $options: 'i' } } ] }, " +
            "       { $and: [ { ?10: null }, { newValue: { $regex: ?9, $options: 'i' } } ] }" +
            "    ]" +
            "  }," +
            "  { $or: [" +
            "       { ?11: null }, " +
            "       { $and: [ { ?12: { $exists: true } }, { oldValue.?12: { $regex: ?11, $options: 'i' } } ] }, " +
            "       { $and: [ { ?12: null }, { oldValue: { $regex: ?11, $options: 'i' } } ] }" +
            "    ]" +
            "  }" +
            "]" +
            "}")
    Page<AuditLog> searchAuditLogs(
            String entityName,
            String entityId,
            String actionType,
            String userEmail,
            String userId,
            String userName,
            String ipAddress,
            LocalDateTime fromDate,
            LocalDateTime toDate,
            String newValueSearchValue,  // ?9
            String newValueKey,          // ?10
            String oldValueSearchValue,  // ?11
            String oldValueKey,          // ?12
            Pageable pageable
    );
}
