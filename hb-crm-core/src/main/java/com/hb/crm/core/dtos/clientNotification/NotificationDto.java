package com.hb.crm.core.dtos.clientNotification;

import com.hb.crm.core.Enums.IconType;
import com.hb.crm.core.Enums.NotificationChannelType;
import com.hb.crm.core.Enums.NotificationEntityType;
import com.hb.crm.core.Enums.NotificationType;
import com.hb.crm.core.beans.User;
import com.hb.crm.core.dtos.SimpleUserinfoDto;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.DBRef;


import java.time.LocalDateTime;
import java.util.HashMap;

@Data
public class NotificationDto {

    private String id;
    private String subject;
    private String body;
    private HashMap<String, String> payload;
    private boolean read;
    private NotificationType type;
    private NotificationChannelType channelType;
    private SimpleUserinfoDto user;
    private LocalDateTime lastTriedAt;
    private LocalDateTime createdAt = LocalDateTime.now();
    private LocalDateTime readAt = LocalDateTime.now();
    private String icon;
    private String image;
    private IconType iconType;
    private String entitySlug;
    private String username;
    private String entityId;
    private NotificationEntityType entityType;
    private boolean hidden;
    private boolean isMuted;
    private boolean sent;
    private long retryCount;
}
