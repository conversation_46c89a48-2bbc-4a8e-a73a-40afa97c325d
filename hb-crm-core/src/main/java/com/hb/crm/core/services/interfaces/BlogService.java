package com.hb.crm.core.services.interfaces;

import com.hb.crm.core.Enums.BlogStatus;
import com.hb.crm.core.Enums.ReactionType;
import com.hb.crm.core.beans.Blog;

import com.hb.crm.core.beans.Employee;
import com.hb.crm.core.dtos.PageDto;
import org.springframework.data.domain.Page;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import com.hb.crm.core.dtos.blog.CreateBlogDto;
import com.hb.crm.core.dtos.blog.CreateBlogWithMediaDto;
import com.hb.crm.core.dtos.blog.UpdateBlogDto;

/**
 * Blog Service Interface - Comprehensive blog management service
 *
 * This service provides all blog-related operations including:
 * - CRUD operations with proper validation and security
 * - Advanced search and filtering capabilities
 * - Analytics and view tracking for performance insights
 * - Reaction and engagement management
 * - Bulk operations for administrative tasks
 * - SEO optimization features
 *
 * Performance Considerations:
 * - All listing methods support pagination to handle large datasets
 * - Search operations use MongoDB text indexes for fast results
 * - Analytics operations use aggregation pipelines for efficiency
 * - View tracking is optimized to prevent duplicate counting
 *
 * Security Features:
 * - Employee permission validation for all modification operations
 * - Employee author ownership verification for blog management
 * - Role-based access control for administrative functions
 *
 * <AUTHOR> CRM Team
 * @version 1.0
 * @since 2024-01-01
 */
public interface BlogService {
    
    // CRUD Operations

    /**
     * Creates a new blog post from DTO with automatic slug generation and timestamp setting
     * @param createBlogDto The blog creation DTO
     * @param authorId The ID of the employee author creating the blog
     * @return The created blog with generated ID and timestamps
     */
    Blog createBlog(CreateBlogDto createBlogDto, String authorId);

    /**
     * Updates an existing blog post with validation and slug regeneration if needed
     * @param blog The blog entity with updated information
     * @return The updated blog entity
     * @throws RuntimeException if blog not found
     */
    Blog updateBlog(Blog blog);

    /**
     * Updates an existing blog post from DTO with validation and slug regeneration if needed
     * @param blogId The ID of the blog to update
     * @param updateBlogDto The blog update DTO
     * @return The updated blog entity
     * @throws RuntimeException if blog not found
     */
    Blog updateBlog(String blogId, UpdateBlogDto updateBlogDto);

    /**
     * Retrieves a blog by its unique identifier (excludes soft-deleted blogs)
     * @param id The blog ID
     * @return Optional containing the blog if found and not deleted
     */
    Optional<Blog> getBlogById(String id);

    /**
     * Retrieves a blog by its unique identifier, including soft-deleted blogs
     * @param id The blog ID
     * @return Optional containing the blog if found, regardless of deletion status
     */
    Optional<Blog> getBlogByIdIncludingDeleted(String id);

    /**
     * Retrieves a blog by its URL slug
     * @param slug The blog slug
     * @return Optional containing the blog if found
     */
    Optional<Blog> getBlogBySlug(String slug);

    /**
     * Permanently deletes a blog from the database
     * @param id The blog ID to delete
     */
    void deleteBlog(String id);

    /**
     * Soft deletes a blog (marks as deleted but preserves data)
     * @param id The blog ID to soft delete
     */
    void softDeleteBlog(String id);

    /**
     * Restores a soft-deleted blog
     * @param id The blog ID to restore
     * @return The restored blog entity
     */
    Blog restoreBlog(String id);

    // Status Management

    /**
     * Publishes a draft blog and sets the published date
     * @param id The blog ID to publish
     * @return The published blog
     */
    Blog publishBlog(String id);

    /**
     * Unpublishes a blog (changes status back to draft)
     * @param id The blog ID to unpublish
     * @return The unpublished blog
     */
    Blog unpublishBlog(String id);

    /**
     * Archives a blog (removes from active content but keeps published)
     * @param id The blog ID to archive
     * @return The archived blog
     */
    Blog archiveBlog(String id);

    /**
     * Changes the status of a blog to any valid status
     * @param id The blog ID
     * @param status The new status to set
     * @return The updated blog
     */
    Blog changeBlogStatus(String id, BlogStatus status);
    
    // Search and Filtering
    PageDto<Blog> searchBlogs(String searchTerm, int page, int size);
    PageDto<Blog> getBlogsByStatus(BlogStatus status, int page, int size);
    PageDto<Blog> getBlogsByAuthor(String authorId, BlogStatus status, int page, int size);

    // Advanced Search with Filters
    /**
     * Retrieves all blogs with comprehensive filtering, pagination, and fuzzy search.
     *
     * This method provides a unified API for blog retrieval with support for:
     * - Fuzzy text search across title, content, and slug
     * - Status filtering (including all statuses)
     * - Soft-deleted blog inclusion for admin purposes
     * - Flexible sorting by various fields
     * - Efficient pagination for large datasets
     *
     * @param searchTerm Optional fuzzy search term for title/content/slug (null for no search)
     * @param status Optional status filter (null for all statuses)
     * @param includeDeleted Whether to include soft-deleted blogs (admin feature, default: false)
     * @param page Page number (0-based)
     * @param size Number of items per page (must be > 0)
     * @param sortBy Field to sort by (default: updatedDate if null/empty)
     * @param sortDirection Sort direction: ASC or DESC (default: DESC if invalid)
     * @return PageDto containing filtered and paginated blogs
     * @throws IllegalArgumentException if page < 0 or size <= 0
     * @since 1.0
     */
    PageDto<Blog> getAllBlogsWithFilters(String searchTerm, BlogStatus status, Boolean includeDeleted, int page, int size, String sortBy, String sortDirection);


    PageDto<Blog> getRecentBlogs(int page, int size);
    
    // Advanced Search
    PageDto<Blog> searchBlogsAdvanced(Map<String, Object> filters, int page, int size);
    PageDto<Blog> getBlogsByDateRange(LocalDateTime startDate, LocalDateTime endDate, int page, int size);

    
    // Related Content
    List<Blog> getRelatedBlogs(String blogId, int limit);
    List<Blog> getMostViewedBlogs(int limit);
    List<Blog> getMostLikedBlogs(int limit);
    List<Blog> getRecentPopularBlogs(int days, int minViews, int limit);
    
    // Slug Management
    String generateSlug(String title);
    String generateUniqueSlug(String title);
    boolean isSlugUnique(String slug);
    
    // View Tracking
    void recordView(String blogId, String userId, String ipAddress, String userAgent, String referrer);
    void recordAnonymousView(String blogId, String ipAddress, String userAgent, String referrer);
    long getViewCount(String blogId);
    
    // Reactions
    void addReaction(String blogId, String userId, ReactionType reactionType);
    void removeReaction(String blogId, String userId);
    void updateReaction(String blogId, String userId, ReactionType reactionType);
    Map<ReactionType, Long> getReactionCounts(String blogId);
    boolean hasUserReacted(String blogId, String userId);
    ReactionType getUserReaction(String blogId, String userId);
    
    // Comments
    long getCommentCount(String blogId);
    void updateCommentCount(String blogId);
    
    // Analytics
    Map<String, Object> getBlogAnalytics(String blogId, LocalDateTime startDate, LocalDateTime endDate);
    Map<String, Object> getAuthorAnalytics(String authorId, LocalDateTime startDate, LocalDateTime endDate);
    List<Map<String, Object>> getDailyViewStats(String blogId, LocalDateTime startDate, LocalDateTime endDate);
    List<Map<String, Object>> getTopReaders(String blogId, int limit);
    
    // Bulk Operations
    void bulkUpdateStatus(List<String> blogIds, BlogStatus status);
    void bulkDelete(List<String> blogIds);
    void bulkArchive(List<String> blogIds);
    void bulkSoftDelete(List<String> blogIds);
    
    // Content Management
    Blog duplicateBlog(String blogId, String newTitle);
    void updateBlogCounts(String blogId);
    void refreshBlogSearchIndex(String blogId);
    
    // Validation
    boolean canEmployeeEditBlog(String blogId, String employeeId);
    boolean canEmployeeDeleteBlog(String blogId, String employeeId);
    boolean canEmployeePublishBlog(String blogId, String employeeId);
}
