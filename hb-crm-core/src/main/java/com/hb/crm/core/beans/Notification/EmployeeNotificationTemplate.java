package com.hb.crm.core.beans.Notification;

import com.hb.crm.core.Enums.NotificationChannelType;
import com.hb.crm.core.Enums.EmployeeNotificationType;
import lombok.Getter;
import lombok.Setter;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.CompoundIndex;
import org.springframework.data.mongodb.core.index.TextIndexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.List;

@Getter
@Setter
@Document
@CompoundIndex(def = "{'push.subject': 'text'," +
        " 'push.body': 'text', 'inApp.subject': 'text'," +
        " 'inApp.body': 'text', 'email.subject': 'text'," +
        " 'email.body': 'text', 'sms.body': 'text', 'whatsApp.body': 'text'}",
        name = "employee_notification_template_text_index")
public class EmployeeNotificationTemplate {

    @Id
    private String id;
    private List<NotificationChannelType> channelTypes;
    private EmployeeNotificationType notificationType;
    private boolean status = false;
    private EmployeeNotificationTemplatePush push;
    private EmployeeNotificationTemplateInApp inApp;
    private EmployeeNotificationTemplateEmail email;
    private EmployeeNotificationTemplateSms sms;
    private EmployeeNotificationTemplateWhatsApp whatsApp;
}
