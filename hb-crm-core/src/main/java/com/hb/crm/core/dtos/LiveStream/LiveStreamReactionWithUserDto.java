package com.hb.crm.core.dtos.LiveStream;

import com.hb.crm.core.Enums.ReactionType;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class LiveStreamReactionWithUserDto {
    private String id;
    private String userId;
    private String username;
    private String firstName;
    private String lastName;
    private String profileImage;
    private String liveStreamId;
    private ReactionType reactionType;
    private Date createDate;
    private Date updateDate;
}
