package com.hb.crm.core.services;

import com.hb.crm.core.Enums.BlogStatus;
import com.hb.crm.core.Enums.ReactionType;
import com.hb.crm.core.beans.*;
import com.hb.crm.core.dtos.PageDto;
import com.hb.crm.core.repositories.*;
import com.hb.crm.core.services.interfaces.BlogService;
import com.hb.crm.core.services.interfaces.QueryNormalizeService;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.Normalizer;
import java.time.LocalDateTime;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import com.hb.crm.core.dtos.blog.CreateBlogDto;
import com.hb.crm.core.dtos.blog.CreateBlogWithMediaDto;
import com.hb.crm.core.dtos.blog.UpdateBlogDto;
import com.hb.crm.core.dtos.blog.MediaWrapperDto;
import com.hb.crm.core.dtos.CreateMediaDto;
import com.hb.crm.core.dtos.RefranceModelDto;
import com.hb.crm.core.Enums.MediaType;
import com.hb.crm.core.Enums.ImageCategory;
import org.bson.types.ObjectId;
import org.joda.time.DateTime;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import com.hb.crm.core.beans.MediaWrapper;

@Service
@RequiredArgsConstructor
public class BlogServiceImpl implements BlogService {
    
    private static final Logger logger = LoggerFactory.getLogger(BlogServiceImpl.class);
    
    private final BlogRepository blogRepository;

    private final BlogReactionRepository blogReactionRepository;
    private final BlogViewRepository blogViewRepository;
    private final EmployeeRepository employeeRepository;
    private final CommentRepository commentRepository;
    private final MongoTemplate mongoTemplate;
    private final UserRepository userRepository;
    private final MediaRepository mediaRepository;
    private final QueryNormalizeService queryNormalizeService;
    
    

    /**
     * Creates a new blog post from DTO with automatic slug generation and timestamp setting.
     *
     * This method converts the CreateBlogDto to a Blog entity, sets the employee author,
     * and delegates to the main createBlog method for processing.
     *
     * @param createBlogDto The blog creation DTO containing blog data
     * @param authorId The unique identifier of the employee author creating the blog
     * @return The created blog with generated ID and timestamps
     * @throws RuntimeException if employee author not found
     * @since 1.0
     */
    @Override
    @Transactional
    public Blog createBlog(CreateBlogDto createBlogDto, String authorId) {
        logger.info("Creating new blog from DTO with title: {} for employee author: {}", createBlogDto.getTitle(), authorId);

        // Validate media if provided
        if (createBlogDto.getMedia() != null && !createBlogDto.getMedia().isEmpty()) {
            for (var media : createBlogDto.getMedia()) {
                if (media.getMediaType().equals(MediaType.video)) {
                    if (media.getVideoDurationMS() == null || media.getVideoDurationMS().equals(java.math.BigDecimal.ZERO)) {
                        throw new RuntimeException("Each video must have a duration greater than 0 milliseconds.");
                    }
                }
            }
        }

        // Find employee author
        Optional<Employee> author = employeeRepository.findById(authorId);
        if (author.isEmpty()) {
            throw new RuntimeException("Employee author not found with id: " + authorId);
        }

        // Convert DTO to entity
        Blog blog = convertCreateDtoToEntity(createBlogDto);
        blog.setAuthor(author.get());

        // Delegate to main createBlog method
        return createBlog(blog);
    }

    /**
     * Updates an existing blog post with new content and metadata.
     *
     * This method performs a complete update of blog content while preserving
     * system-managed fields like creation date, view counts, and reaction counts.
     * The slug is automatically regenerated if the title changes.
     *
     * @param blog The blog entity with updated content (must have valid ID)
     * @return The updated blog entity
     * @throws RuntimeException if blog with given ID is not found
     *
     * @see #generateUniqueSlug(String) for slug regeneration when title changes
     * @since 1.0
     */
    @Override
    @Transactional
    public Blog updateBlog(Blog blog) {
        logger.info("Updating blog with id: {}", blog.getId());

        Optional<Blog> existingBlog = blogRepository.findById(blog.getId());
        if (existingBlog.isEmpty()) {
            throw new RuntimeException("Blog not found with id: " + blog.getId());
        }

        Blog existing = existingBlog.get();

        // Update fields
        existing.setTitle(blog.getTitle());
        existing.setContent(blog.getContent());
        
        existing.setTags(blog.getTags());
        existing.setMedia(blog.getMedia());
        existing.setAllowComments(blog.isAllowComments());
        existing.setAllowReactions(blog.isAllowReactions());
        existing.setUpdatedDate(LocalDateTime.now());

        // Update slug if title changed
        if (!existing.getTitle().equals(blog.getTitle())) {
            String newSlug = generateUniqueSlug(blog.getTitle());
            existing.setSlug(newSlug);
        }

        return blogRepository.save(existing);
    }

    /**
     * Updates an existing blog post from DTO with validation and slug regeneration if needed.
     *
     * This method converts the UpdateBlogDto to update the existing blog entity,
     * preserving the original employee author and creation metadata.
     *
     * @param blogId The unique identifier of the blog to update
     * @param updateBlogDto The blog update DTO containing updated data
     * @return The updated blog entity
     * @throws RuntimeException if blog not found
     * @since 1.0
     */
    @Override
    @Transactional
    public Blog updateBlog(String blogId, UpdateBlogDto updateBlogDto) {
        logger.info("Updating blog from DTO with id: {}", blogId);

        Optional<Blog> existingBlog = blogRepository.findById(blogId);
        if (existingBlog.isEmpty()) {
            throw new RuntimeException("Blog not found with id: " + blogId);
        }

        Blog existing = existingBlog.get();
        String originalTitle = existing.getTitle();

        // Update fields from DTO
        existing.setTitle(updateBlogDto.getTitle());
        existing.setContent(updateBlogDto.getContent());
        existing.setAllowComments(updateBlogDto.isAllowComments());
        existing.setAllowReactions(updateBlogDto.isAllowReactions());
        existing.setUpdatedDate(LocalDateTime.now());

        // Update status if provided
        if (updateBlogDto.getStatus() != null) {
            existing.setStatus(updateBlogDto.getStatus());
        }

        // Convert and set media if provided
        var generatedMedia = generateMediaWithWrapper(updateBlogDto.getMedia());

        // Update slug if title changed
        if (!originalTitle.equals(updateBlogDto.getTitle())) {
            String newSlug = generateUniqueSlug(updateBlogDto.getTitle());
            existing.setSlug(newSlug);
        }

        return blogRepository.save(existing);
    }

    /**
     * Retrieves a blog post by its unique identifier.
     *
     * This method returns only non-deleted blogs for public access.
     * For administrative access that needs to retrieve soft-deleted blogs,
     * use getBlogByIdIncludingDeleted() instead.
     *
     * @param id The unique identifier of the blog
     * @return Optional containing the blog if found and not deleted, empty otherwise
     * @since 1.0
     */
    @Override
    public Optional<Blog> getBlogById(String id) {
        Optional<Blog> blog = blogRepository.findById(id);
        return blog.filter(b -> !b.isDeleted());
    }

    /**
     * Retrieves a blog post by its unique identifier, including soft-deleted blogs.
     *
     * This method is intended for administrative purposes where access to
     * soft-deleted blogs is required (e.g., for restoration or audit purposes).
     *
     * @param id The unique identifier of the blog
     * @return Optional containing the blog if found, regardless of deletion status
     * @since 1.0
     */
    @Override
    public Optional<Blog> getBlogByIdIncludingDeleted(String id) {
        return blogRepository.findById(id);
    }

    /**
     * Retrieves a blog post by its SEO-friendly slug.
     *
     * This method is commonly used for public-facing URLs where the slug
     * provides a employee-friendly and SEO-optimized identifier.
     * Only returns non-deleted blogs for public access.
     *
     * @param slug The SEO-friendly slug of the blog
     * @return Optional containing the blog if found and not deleted, empty otherwise
     * @since 1.0
     */
    @Override
    public Optional<Blog> getBlogBySlug(String slug) {
        return blogRepository.findBySlugAndDeletedFalse(slug);
    }

    /**
     * Permanently deletes a blog post from the database.
     *
     * WARNING: This operation is irreversible. Consider using softDeleteBlog()
     * for data retention and recovery capabilities.
     *
     * @param id The unique identifier of the blog to delete
     * @see #softDeleteBlog(String) for reversible deletion
     * @since 1.0
     */
    @Override
    @Transactional
    public void deleteBlog(String id) {
        logger.info("Deleting blog with id: {}", id);
        blogRepository.deleteById(id);
    }

    /**
     * Soft deletes a blog post by marking it as deleted without removing from database.
     *
     * This method preserves the blog data for potential recovery while hiding it
     * from public view. The blog status is changed to 'deleted' and a deletion
     * timestamp is recorded.
     *
     * @param id The unique identifier of the blog to soft delete
     * @see #deleteBlog(String) for permanent deletion
     * @since 1.0
     */
    @Override
    @Transactional
    public void softDeleteBlog(String id) {
        logger.info("Soft deleting blog with id: {}", id);

        Query query = new Query(Criteria.where("id").is(id));
        Update update = new Update()
                .set("deleted", true)
                .set("deletedDate", LocalDateTime.now())
                .set("status", BlogStatus.deleted);

        mongoTemplate.updateFirst(query, update, Blog.class);
    }
    
    /**
     * Publishes a draft blog post, making it visible to the public.
     *
     * This method changes the blog status to 'published' and sets the
     * publication date if not already set.
     *
     * @param id The unique identifier of the blog to publish
     * @return The updated blog entity with published status
     * @throws RuntimeException if blog is not found
     * @see #changeBlogStatus(String, BlogStatus) for the underlying implementation
     * @since 1.0
     */
    @Override
    @Transactional
    public Blog publishBlog(String id) {
        return changeBlogStatus(id, BlogStatus.published);
    }

    /**
     * Unpublishes a blog post, changing its status back to draft.
     *
     * This removes the blog from public view while preserving all content
     * and allowing further editing.
     *
     * @param id The unique identifier of the blog to unpublish
     * @return The updated blog entity with draft status
     * @throws RuntimeException if blog is not found
     * @see #changeBlogStatus(String, BlogStatus) for the underlying implementation
     * @since 1.0
     */
    @Override
    @Transactional
    public Blog unpublishBlog(String id) {
        return changeBlogStatus(id, BlogStatus.draft);
    }

    /**
     * Archives a blog post, removing it from active content while preserving it.
     *
     * Archived blogs are not visible in public listings but remain accessible
     * via direct links and can be restored later.
     *
     * @param id The unique identifier of the blog to archive
     * @return The updated blog entity with archived status
     * @throws RuntimeException if blog is not found
     * @see #changeBlogStatus(String, BlogStatus) for the underlying implementation
     * @since 1.0
     */
    @Override
    @Transactional
    public Blog archiveBlog(String id) {
        return changeBlogStatus(id, BlogStatus.archived);
    }

    /**
     * Changes the status of a blog post to the specified status.
     *
     * This is the core method for all status changes. It handles special logic
     * for publication dates and ensures proper timestamps are maintained.
     *
     * @param id The unique identifier of the blog
     * @param status The new status to set
     * @return The updated blog entity
     * @throws RuntimeException if blog is not found
     * @since 1.0
     */
    @Override
    @Transactional
    public Blog changeBlogStatus(String id, BlogStatus status) {
        logger.info("Changing blog status to {} for id: {}", status, id);

        // Use getBlogByIdIncludingDeleted for administrative operations
        Optional<Blog> blogOpt = getBlogByIdIncludingDeleted(id);
        if (blogOpt.isEmpty()) {
            throw new RuntimeException("Blog not found with id: " + id);
        }

        Blog blog = blogOpt.get();

        // Prevent status changes on soft-deleted blogs unless restoring
        if (blog.isDeleted()) {
            throw new RuntimeException("Cannot change status of soft-deleted blog. Restore first: " + id);
        }

        blog.setStatus(status);
        blog.setUpdatedDate(LocalDateTime.now());

        // Set published date when publishing
        if (status == BlogStatus.published && blog.getPublishedDate() == null) {
            blog.setPublishedDate(LocalDateTime.now());
        }

        return blogRepository.save(blog);
    }
    
    /**
     * Searches published blogs using full-text search across title, content, and excerpt.
     *
     * This method leverages MongoDB's text index for efficient searching with
     * relevance scoring. Results are sorted by publication date (newest first).
     *
     * @param searchTerm The search query string
     * @param page The page number (0-based)
     * @param size The number of results per page
     * @return PageDto containing matching published blogs
     * @since 1.0
     */
    @Override
    public PageDto<Blog> searchBlogs(String searchTerm, int page, int size) {
        Pageable pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "publishedDate"));
        Page<Blog> blogPage = blogRepository.searchByTitleContentOrExcerpt(
                BlogStatus.published, searchTerm, pageable);

        return convertToPageDto(blogPage);
    }

    /**
     * Retrieves blogs filtered by status with pagination.
     *
     * This method returns non-deleted blogs with the specified status,
     * sorted by publication date in descending order.
     *
     * @param status The blog status to filter by
     * @param page The page number (0-based)
     * @param size The number of results per page
     * @return PageDto containing blogs with the specified status
     * @since 1.0
     */
    @Override
    public PageDto<Blog> getBlogsByStatus(BlogStatus status, int page, int size) {
        Pageable pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "publishedDate"));
        Page<Blog> blogPage = blogRepository.findByStatusAndDeletedFalseOrderByPublishedDateDesc(status, pageable);

        return convertToPageDto(blogPage);
    }

    /**
     * Retrieves blogs by a specific employee author with status filtering.
     *
     * This method returns non-deleted blogs authored by the specified employee with the given status,
     * sorted by update date to show the most recently modified blogs first.
     *
     * @param authorId The unique identifier of the employee author
     * @param status The blog status to filter by
     * @param page The page number (0-based)
     * @param size The number of results per page
     * @return PageDto containing the employee author's non-deleted blogs, empty if author not found
     * @since 1.0
     */
    @Override
    public PageDto<Blog> getBlogsByAuthor(String authorId, BlogStatus status, int page, int size) {
        Optional<Employee> author = employeeRepository.findById(authorId);

        if (author.isEmpty())
            return new PageDto<>();

        Pageable pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "updatedDate"));

        Page<Blog> blogPage;
        if (status == null)
            blogPage = blogRepository.findByAuthorAndDeletedFalseOrderByUpdatedDateDesc(author.get(), pageable);
        else
            blogPage = blogRepository.findByAuthorAndStatusAndDeletedFalseOrderByUpdatedDateDesc(
                    author.get(), status, pageable);

        return convertToPageDto(blogPage);
    }
    

    

    
    @Override
    public PageDto<Blog> getRecentBlogs(int page, int size) {
        return getBlogsByStatus(BlogStatus.published, page, size);
    }

    @Override
    public PageDto<Blog> getAllBlogsWithFilters(String searchTerm, BlogStatus status, Boolean includeDeleted,
                                               int page, int size, String sortBy, String sortDirection) {
        logger.info("Getting blogs with filters - searchTerm: {}, status: {}, includeDeleted: {}, page: {}, size: {}",
                   searchTerm, status, includeDeleted, page, size);

        // Build dynamic criteria
        Criteria criteria = new Criteria();

        // Handle soft-deleted blogs
        if (includeDeleted == null || !includeDeleted) {
            criteria = criteria.and("deleted").is(false);
        }

        // Handle status filter
        if (status != null) {
            criteria = criteria.and("status").is(status);
        }

        // Handle fuzzy search
        if (searchTerm != null && !searchTerm.trim().isEmpty()) {
            String escapedSearchTerm = searchTerm.trim();
            criteria = criteria.orOperator(
                Criteria.where("title").regex(escapedSearchTerm, "i"),
                Criteria.where("content").regex(escapedSearchTerm, "i"),
                Criteria.where("slug").regex(escapedSearchTerm, "i")
            );
        }

        // Handle sorting
        String sortField = (sortBy != null && !sortBy.trim().isEmpty()) ? sortBy : "updatedDate";
        Sort.Direction direction = "ASC".equalsIgnoreCase(sortDirection) ?
            Sort.Direction.ASC : Sort.Direction.DESC;
        Sort sort = Sort.by(direction, sortField);

        // Create pageable
        Pageable pageable = PageRequest.of(page, size, sort);

        // Execute query
        Query query = new Query(criteria).with(pageable);
        List<Blog> blogs = mongoTemplate.find(query, Blog.class);

        // Get total count for pagination
        long totalCount = mongoTemplate.count(new Query(criteria), Blog.class);

        // Convert to PageDto
        PageDto<Blog> result = new PageDto<>();
        result.setItems(blogs);
        result.setTotalNoOfItems(totalCount);
        result.setPageNumber(page);
        result.setItemsPerPage(size);

        logger.info("Found {} blogs out of {} total", blogs.size(), totalCount);
        return result;
    }
    
    /**
     * Generates a SEO-friendly slug from a blog title.
     *
     * This method creates a URL-safe string by:
     * - Normalizing Unicode characters and removing diacritical marks
     * - Converting to lowercase
     * - Replacing spaces and special characters with hyphens
     * - Removing consecutive hyphens and leading/trailing hyphens
     *
     * @param title The blog title to convert to a slug
     * @return A SEO-friendly slug, or "untitled" if title is null/empty
     * @see #generateUniqueSlug(String) for ensuring uniqueness
     * @since 1.0
     */
    @Override
    public String generateSlug(String title) {
        if (title == null || title.trim().isEmpty()) {
            return "untitled";
        }

        // Normalize and clean the title
        String normalized = Normalizer.normalize(title.trim(), Normalizer.Form.NFD);
        String slug = Pattern.compile("\\p{InCombiningDiacriticalMarks}+").matcher(normalized).replaceAll("");

        // Convert to lowercase and replace spaces/special chars with hyphens
        slug = slug.toLowerCase()
                .replaceAll("[^a-z0-9\\s-]", "")
                .replaceAll("\\s+", "-")
                .replaceAll("-+", "-")
                .replaceAll("^-|-$", "");

        return slug.isEmpty() ? "untitled" : slug;
    }

    /**
     * Generates a unique slug by appending a counter if the base slug already exists.
     *
     * This method ensures slug uniqueness across all non-deleted blogs by checking the database
     * and incrementing a counter until a unique slug is found. Soft-deleted blogs are ignored
     * to allow slug reuse after deletion.
     *
     * @param title The blog title to convert to a unique slug
     * @return A unique SEO-friendly slug
     * @see #generateSlug(String) for the base slug generation
     * @since 1.0
     */
    @Override
    public String generateUniqueSlug(String title) {
        String baseSlug = generateSlug(title);
        String uniqueSlug = baseSlug;
        int counter = 1;

        while (blogRepository.existsBySlugAndDeletedFalse(uniqueSlug)) {
            uniqueSlug = baseSlug + "-" + counter;
            counter++;
        }

        return uniqueSlug;
    }

    /**
     * Checks if a given slug is unique across all non-deleted blogs.
     *
     * This method only considers non-deleted blogs for uniqueness checking,
     * allowing slug reuse after soft deletion.
     *
     * @param slug The slug to check for uniqueness
     * @return true if the slug is unique among non-deleted blogs, false if it already exists
     * @since 1.0
     */
    @Override
    public boolean isSlugUnique(String slug) {
        return !blogRepository.existsBySlugAndDeletedFalse(slug);
    }
    
    /**
     * Records a view for a blog by an authenticated user.
     *
     * This method tracks user engagement by recording view events with metadata
     * for analytics. Each user can only be counted once per blog to prevent
     * duplicate view counting.
     *
     * @param blogId The unique identifier of the blog being viewed
     * @param userId The unique identifier of the user viewing the blog
     * @param ipAddress The IP address of the viewer
     * @param userAgent The browser user agent string
     * @param referrer The referring URL (can be null)
     *
     * @see #recordAnonymousView(String, String, String, String) for anonymous views
     * @since 1.0
     */
    @Override
    @Transactional
    public void recordView(String blogId, String userId, String ipAddress, String userAgent, String referrer) {
        Optional<Blog> blogOpt = blogRepository.findById(blogId);
        if (blogOpt.isEmpty()) {
            return;
        }

        Blog blog = blogOpt.get();
        Optional<User> userOpt = userRepository.findById(userId);

        if (userOpt.isPresent()) {
            User user = userOpt.get();

            // Check if user already viewed this blog
            if (!blogViewRepository.existsByBlogAndUser(blog, user)) {
                BlogView view = new BlogView(blog, user);
                view.setIpAddress(ipAddress);
                view.setUserAgent(userAgent);
                view.setReferrer(referrer);
                blogViewRepository.save(view);

                // Increment view count
                incrementViewCount(blogId);
            }
        }
    }

    /**
     * Records an anonymous view for a blog from a non-authenticated user.
     *
     * This method tracks views from users who are not logged in, using IP address
     * as the primary identifier. Each view is counted regardless of duplicates
     * from the same IP.
     *
     * @param blogId The unique identifier of the blog being viewed
     * @param ipAddress The IP address of the anonymous viewer
     * @param userAgent The browser user agent string
     * @param referrer The referring URL (can be null)
     *
     * @see #recordView(String, String, String, String, String) for authenticated views
     * @since 1.0
     */
    @Override
    @Transactional
    public void recordAnonymousView(String blogId, String ipAddress, String userAgent, String referrer) {
        Optional<Blog> blogOpt = blogRepository.findById(blogId);
        if (blogOpt.isEmpty()) {
            return;
        }

        Blog blog = blogOpt.get();
        BlogView view = new BlogView(blog, ipAddress);
        view.setUserAgent(userAgent);
        view.setReferrer(referrer);
        blogViewRepository.save(view);

        // Increment view count
        incrementViewCount(blogId);
    }

    /**
     * Retrieves the total view count for a specific blog.
     *
     * @param blogId The unique identifier of the blog
     * @return The total number of views, or 0 if blog not found
     * @since 1.0
     */
    @Override
    public long getViewCount(String blogId) {
        Optional<Blog> blog = blogRepository.findById(blogId);
        return blog.map(Blog::getViewCount).orElse(0);
    }

    /**
     * Adds or updates a user's reaction to a blog post.
     *
     * This method handles both new reactions and updates to existing reactions.
     * If the user has already reacted to the blog, their reaction type is updated.
     * The blog's like count is automatically maintained for like reactions.
     *
     * @param blogId The unique identifier of the blog
     * @param userId The unique identifier of the user reacting
     * @param reactionType The type of reaction (like, love, wow, etc.)
     *
     * @see #removeReaction(String, String) for removing reactions
     * @see #getUserReaction(String, String) for checking existing reactions
     * @since 1.0
     */
    @Override
    @Transactional
    public void addReaction(String blogId, String userId, ReactionType reactionType) {
        Optional<Blog> blogOpt = blogRepository.findById(blogId);
        Optional<User> userOpt = userRepository.findById(userId);

        if (blogOpt.isEmpty() || userOpt.isEmpty()) {
            return;
        }

        Blog blog = blogOpt.get();
        User user = userOpt.get();

        // Check if reaction already exists
        Optional<BlogReaction> existingReaction = blogReactionRepository.findByBlogAndUser(blog, user);

        if (existingReaction.isPresent()) {
            // Update existing reaction
            BlogReaction reaction = existingReaction.get();
            reaction.setReactionType(reactionType);
            reaction.setUpdateDate(new Date());
            blogReactionRepository.save(reaction);
        } else {
            // Create new reaction
            BlogReaction reaction = new BlogReaction(blog, user);
            reaction.setReactionType(reactionType);
            blogReactionRepository.save(reaction);

            // Increment like count if it's a like
            if (reactionType == ReactionType.like) {
                incrementLikeCount(blogId);
            }
        }
    }

    /**
     * Removes a user's reaction from a blog post.
     *
     * This method removes the user's existing reaction and updates the blog's
     * like count if the removed reaction was a like.
     *
     * @param blogId The unique identifier of the blog
     * @param userId The unique identifier of the user whose reaction to remove
     *
     * @see #addReaction(String, String, ReactionType) for adding reactions
     * @since 1.0
     */
    @Override
    @Transactional
    public void removeReaction(String blogId, String userId) {
        Optional<Blog> blogOpt = blogRepository.findById(blogId);
        Optional<User> userOpt = userRepository.findById(userId);

        if (blogOpt.isEmpty() || userOpt.isEmpty()) {
            return;
        }

        Blog blog = blogOpt.get();
        User user = userOpt.get();

        Optional<BlogReaction> reaction = blogReactionRepository.findByBlogAndUser(blog, user);
        if (reaction.isPresent()) {
            ReactionType reactionType = reaction.get().getReactionType();
            blogReactionRepository.deleteByBlogAndUser(blog, user);

            // Decrement like count if it was a like
            if (reactionType == ReactionType.like) {
                decrementLikeCount(blogId);
            }
        }
    }

    @Override
    @Transactional
    public void updateReaction(String blogId, String userId, ReactionType reactionType) {
        addReaction(blogId, userId, reactionType);
    }

    @Override
    public Map<ReactionType, Long> getReactionCounts(String blogId) {
        List<BlogReactionRepository.ReactionCountDto> counts =
                blogReactionRepository.getReactionCountsByBlog(blogId);

        return counts.stream()
                .collect(Collectors.toMap(
                        BlogReactionRepository.ReactionCountDto::get_id,
                        BlogReactionRepository.ReactionCountDto::getCount
                ));
    }

    @Override
    public boolean hasUserReacted(String blogId, String userId) {
        Optional<Blog> blogOpt = blogRepository.findById(blogId);
        Optional<User> userOpt = userRepository.findById(userId);

        if (blogOpt.isEmpty() || userOpt.isEmpty()) {
            return false;
        }

        return blogReactionRepository.existsByBlogAndUser(blogOpt.get(), userOpt.get());
    }

    @Override
    public ReactionType getUserReaction(String blogId, String userId) {
        Optional<Blog> blogOpt = blogRepository.findById(blogId);
        Optional<User> userOpt = userRepository.findById(userId);

        if (blogOpt.isEmpty() || userOpt.isEmpty()) {
            return null;
        }

        Optional<BlogReaction> reaction = blogReactionRepository.findByBlogAndUser(
                blogOpt.get(), userOpt.get());

        return reaction.map(BlogReaction::getReactionType).orElse(null);
    }

    @Override
    public long getCommentCount(String blogId) {
        Optional<Blog> blog = blogRepository.findById(blogId);
        return blog.map(Blog::getCommentCount).orElse(0);
    }

    @Override
    @Transactional
    public void updateCommentCount(String blogId) {
        Query query = new Query(Criteria.where("blog.$id").is(blogId));
        long commentCount = mongoTemplate.count(query, Comment.class);

        Query blogQuery = new Query(Criteria.where("id").is(blogId));
        Update update = new Update().set("commentCount", commentCount);
        mongoTemplate.updateFirst(blogQuery, update, Blog.class);
    }

    /**
     * Atomically increments the view count for a blog post.
     *
     * This method uses MongoDB's atomic increment operation to ensure
     * thread-safe counter updates even under high concurrency.
     *
     * @param blogId The unique identifier of the blog
     * @since 1.0
     */
    private void incrementViewCount(String blogId) {
        Query query = new Query(Criteria.where("id").is(blogId));
        Update update = new Update().inc("viewCount", 1);
        mongoTemplate.updateFirst(query, update, Blog.class);
    }

    /**
     * Atomically increments the like count for a blog post.
     *
     * This method uses MongoDB's atomic increment operation to ensure
     * accurate like counting even with concurrent reactions.
     *
     * @param blogId The unique identifier of the blog
     * @since 1.0
     */
    private void incrementLikeCount(String blogId) {
        Query query = new Query(Criteria.where("id").is(blogId));
        Update update = new Update().inc("likeCount", 1);
        mongoTemplate.updateFirst(query, update, Blog.class);
    }

    /**
     * Atomically decrements the like count for a blog post.
     *
     * This method uses MongoDB's atomic decrement operation to ensure
     * accurate like counting when reactions are removed.
     *
     * @param blogId The unique identifier of the blog
     * @since 1.0
     */
    private void decrementLikeCount(String blogId) {
        Query query = new Query(Criteria.where("id").is(blogId));
        Update update = new Update().inc("likeCount", -1);
        mongoTemplate.updateFirst(query, update, Blog.class);
    }

    // Implement remaining methods with basic implementations
    @Override
    public PageDto<Blog> searchBlogsAdvanced(Map<String, Object> filters, int page, int size) {
        // TODO: Implement advanced search with filters
        return getBlogsByStatus(BlogStatus.published, page, size);
    }

    /**
     * Retrieves published blogs within a specific date range.
     *
     * This method returns non-deleted published blogs that were published
     * between the specified start and end dates.
     *
     * @param startDate The start date for the range (inclusive)
     * @param endDate The end date for the range (inclusive)
     * @param page The page number (0-based)
     * @param size The number of results per page
     * @return PageDto containing published non-deleted blogs in the date range
     * @since 1.0
     */
    @Override
    public PageDto<Blog> getBlogsByDateRange(LocalDateTime startDate, LocalDateTime endDate, int page, int size) {
        Pageable pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "publishedDate"));
        Page<Blog> blogPage = blogRepository.findByStatusAndDeletedFalseAndPublishedDateBetweenOrderByPublishedDateDesc(
                BlogStatus.published, startDate, endDate, pageable);
        return convertToPageDto(blogPage);
    }



    @Override
    public List<Blog> getRelatedBlogs(String blogId, int limit) {
        Optional<Blog> blogOpt = blogRepository.findById(blogId);
        if (blogOpt.isEmpty()) {
            return Collections.emptyList();
        }

        Blog blog = blogOpt.get();
        List<String> tagIds = blog.getTags() != null ?
                blog.getTags().stream().map(Tag::getId).collect(Collectors.toList()) :
                Collections.emptyList();

        return blogRepository.findRelatedBlogs(blogId, tagIds, limit);
    }

    @Override
    public List<Blog> getMostViewedBlogs(int limit) {
        return blogRepository.findMostViewedBlogs(limit);
    }

    @Override
    public List<Blog> getMostLikedBlogs(int limit) {
        return blogRepository.findMostLikedBlogs(limit);
    }

    @Override
    public List<Blog> getRecentPopularBlogs(int days, int minViews, int limit) {
        LocalDateTime sinceDate = LocalDateTime.now().minusDays(days);
        Pageable pageable = PageRequest.of(0, limit);
        Page<Blog> page = blogRepository.findRecentPopularBlogs(
                BlogStatus.published, sinceDate, minViews, pageable);
        return page.getContent();
    }

    /**
     * Retrieves comprehensive analytics for a specific blog.
     *
     * This method returns analytics data including view counts, engagement metrics,
     * and daily view statistics for the specified date range. Analytics are available
     * for both active and soft-deleted blogs for administrative purposes.
     *
     * @param blogId The unique identifier of the blog
     * @param startDate The start date for analytics data
     * @param endDate The end date for analytics data
     * @return Map containing analytics data, empty if blog not found
     * @since 1.0
     */
    @Override
    public Map<String, Object> getBlogAnalytics(String blogId, LocalDateTime startDate, LocalDateTime endDate) {
        Map<String, Object> analytics = new HashMap<>();

        // Use getBlogByIdIncludingDeleted for analytics access
        Optional<Blog> blogOpt = getBlogByIdIncludingDeleted(blogId);
        if (blogOpt.isEmpty()) {
            return analytics;
        }

        Blog blog = blogOpt.get();
        analytics.put("viewCount", blog.getViewCount());
        analytics.put("likeCount", blog.getLikeCount());
        analytics.put("commentCount", blog.getCommentCount());
        analytics.put("shareCount", blog.getShareCount());
        analytics.put("deleted", blog.isDeleted());
        analytics.put("status", blog.getStatus());

        // Add daily view stats
        List<BlogViewRepository.DailyViewStats> dailyStats =
                blogViewRepository.getDailyViewStats(blogId, startDate, endDate);
        analytics.put("dailyViews", dailyStats);

        return analytics;
    }

    @Override
    public Map<String, Object> getAuthorAnalytics(String authorId, LocalDateTime startDate, LocalDateTime endDate) {
        Map<String, Object> analytics = new HashMap<>();

        Optional<Employee> authorOpt = employeeRepository.findById(authorId);
        if (authorOpt.isEmpty()) {
            return analytics;
        }

        Employee author = authorOpt.get();
        long totalBlogs = blogRepository.countByAuthorAndStatusAndDeletedFalse(author, BlogStatus.published);
        analytics.put("totalPublishedBlogs", totalBlogs);

        return analytics;
    }

    @Override
    public List<Map<String, Object>> getDailyViewStats(String blogId, LocalDateTime startDate, LocalDateTime endDate) {
        List<BlogViewRepository.DailyViewStats> stats =
                blogViewRepository.getDailyViewStats(blogId, startDate, endDate);

        return stats.stream().map(stat -> {
            Map<String, Object> map = new HashMap<>();
            map.put("date", stat.get_id());
            map.put("views", stat.getCount());
            map.put("uniqueUsers", stat.getUniqueUserCount());
            return map;
        }).collect(Collectors.toList());
    }

    @Override
    public List<Map<String, Object>> getTopReaders(String blogId, int limit) {
        List<BlogViewRepository.ReaderStats> stats =
                blogViewRepository.getMostActiveReaders(blogId, limit);

        return stats.stream().map(stat -> {
            Map<String, Object> map = new HashMap<>();
            map.put("user", stat.get_id());
            map.put("viewCount", stat.getViewCount());
            map.put("lastView", stat.getLastView());
            map.put("totalReadTime", stat.getTotalReadTime());
            return map;
        }).collect(Collectors.toList());
    }

    /**
     * Updates the status of multiple blogs in a single atomic operation.
     *
     * This method efficiently updates multiple non-deleted blogs using MongoDB's updateMulti
     * operation. When publishing blogs, the published date is automatically set.
     * Only non-deleted blogs are updated to prevent accidental modification of soft-deleted content.
     *
     * @param blogIds List of blog IDs to update
     * @param status The new status to set for all blogs
     *
     * @see #changeBlogStatus(String, BlogStatus) for single blog status updates
     * @since 1.0
     */
    @Override
    @Transactional
    public void bulkUpdateStatus(List<String> blogIds, BlogStatus status) {
        Query query = new Query(Criteria.where("id").in(blogIds).and("deleted").is(false));
        Update update = new Update()
                .set("status", status)
                .set("updatedDate", LocalDateTime.now());

        if (status == BlogStatus.published) {
            update.set("publishedDate", LocalDateTime.now());
        }

        mongoTemplate.updateMulti(query, update, Blog.class);
    }

    /**
     * Soft deletes multiple blogs in a single operation.
     *
     * This method marks multiple blogs as deleted without removing them from the database,
     * allowing for data retention and potential recovery. Only non-deleted blogs are affected.
     * This provides a safer alternative to permanent deletion with recovery capabilities.
     *
     * @param blogIds List of blog IDs to soft delete
     *
     * @see #softDeleteBlog(String) for single blog soft deletion
     * @see #restoreBlog(String) for blog recovery
     * @since 1.0
     */
    @Override
    @Transactional
    public void bulkDelete(List<String> blogIds) {
        blogRepository.deleteAllById(blogIds);
    }

    /**
     * Archives multiple blogs by changing their status to archived.
     *
     * This is a convenience method that uses bulkUpdateStatus internally
     * to archive multiple blogs efficiently.
     *
     * @param blogIds List of blog IDs to archive
     *
     * @see #bulkUpdateStatus(List, BlogStatus) for the underlying implementation
     * @since 1.0
     */
    @Override
    @Transactional
    public void bulkArchive(List<String> blogIds) {
        bulkUpdateStatus(blogIds, BlogStatus.archived);
    }

    /**
     * Soft deletes multiple blogs in a single atomic operation.
     *
     * This method marks multiple blogs as deleted without removing them from the database,
     * allowing for data retention and potential recovery. Only non-deleted blogs are affected.
     *
     * @param blogIds List of blog IDs to soft delete
     *
     * @see #softDeleteBlog(String) for single blog soft deletion
     * @since 1.0
     */
    @Override
    @Transactional
    public void bulkSoftDelete(List<String> blogIds) {
        Query query = new Query(Criteria.where("id").in(blogIds));
        Update update = new Update()
                .set("deleted", true)
                .set("deletedDate", LocalDateTime.now())
                .set("status", BlogStatus.deleted);

        mongoTemplate.updateMulti(query, update, Blog.class);
    }

    /**
     * Creates a duplicate copy of an existing blog with a new title.
     *
     * This method copies all content, metadata, and settings from the original blog
     * while creating a new blog entity with a fresh ID, slug, and timestamps.
     * The duplicate starts as a draft regardless of the original's status.
     *
     * @param blogId The unique identifier of the blog to duplicate
     * @param newTitle The title for the duplicated blog
     * @return The newly created duplicate blog
     * @throws RuntimeException if the original blog is not found
     *
     * @see #createBlog(Blog) for the blog creation process
     * @since 1.0
     */
    @Override
    @Transactional
    public Blog duplicateBlog(String blogId, String newTitle) {
        Optional<Blog> originalOpt = blogRepository.findById(blogId);
        if (originalOpt.isEmpty()) {
            throw new RuntimeException("Blog not found with id: " + blogId);
        }

        Blog original = originalOpt.get();
        Blog duplicate = new Blog();

        // Copy fields
        duplicate.setTitle(newTitle);
        duplicate.setContent(original.getContent());
        duplicate.setAuthor(original.getAuthor());
        duplicate.setTags(original.getTags());
        duplicate.setMedia(original.getMedia());
        duplicate.setAllowComments(original.isAllowComments());
        duplicate.setAllowReactions(original.isAllowReactions());

        return createBlog(duplicate);
    }

    /**
     * Restores a soft-deleted blog by removing the deletion flag and timestamp.
     *
     * This method allows recovery of soft-deleted blogs by setting deleted=false
     * and clearing the deletedDate. The blog status is changed back to draft.
     *
     * @param id The unique identifier of the blog to restore
     * @throws RuntimeException if blog is not found or not soft-deleted
     * @since 1.0
     */
    @Override
    @Transactional
    public Blog restoreBlog(String id) {
        logger.info("Restoring soft-deleted blog with id: {}", id);

        Optional<Blog> blogOpt = getBlogByIdIncludingDeleted(id);
        if (blogOpt.isEmpty()) {
            throw new RuntimeException("Blog not found with id: " + id);
        }

        Blog blog = blogOpt.get();
        if (!blog.isDeleted()) {
            throw new RuntimeException("Blog is not soft-deleted, cannot restore: " + id);
        }

        blog.setDeleted(false);
        blog.setDeletedDate(null);
        blog.setStatus(BlogStatus.draft);
        blog.setUpdatedDate(LocalDateTime.now());

        return blogRepository.save(blog);
    }

    /**
     * Updates various counters for a blog (comments, reactions, etc.).
     *
     * This method refreshes the cached counter values by recalculating them
     * from the actual data in the database.
     *
     * @param blogId The unique identifier of the blog to update counts for
     * @since 1.0
     */
    @Override
    @Transactional
    public void updateBlogCounts(String blogId) {
        updateCommentCount(blogId);
        // Update other counts as needed
    }

    /**
     * Refreshes the search index for a specific blog.
     *
     * This method is used when external search engines (like Elasticsearch)
     * are integrated to ensure the search index is up-to-date.
     *
     * @param blogId The unique identifier of the blog to refresh in search index
     * @since 1.0
     */
    @Override
    public void refreshBlogSearchIndex(String blogId) {
        // TODO: Implement search index refresh if using external search engine
        logger.info("Refreshing search index for blog: {}", blogId);
    }

    /**
     * Checks if an employee has permission to edit a specific blog.
     *
     * This method verifies blog ownership by comparing the blog's employee author
     * with the requesting employee. Only the blog author can edit their own blogs.
     *
     * @param blogId The unique identifier of the blog
     * @param employeeId The unique identifier of the employee requesting edit access
     * @return true if the employee can edit the blog, false otherwise
     * @since 1.0
     */
    @Override
    public boolean canEmployeeEditBlog(String blogId, String employeeId) {
        Optional<Blog> blogOpt = blogRepository.findById(blogId);
        if (blogOpt.isEmpty()) {
            return false;
        }

        Blog blog = blogOpt.get();
        return blog.getAuthor() != null && blog.getAuthor().getId().equals(employeeId);
    }

    /**
     * Checks if an employee has permission to delete a specific blog.
     *
     * Currently uses the same logic as edit permissions - only the blog
     * employee author can delete their own blogs.
     *
     * @param blogId The unique identifier of the blog
     * @param employeeId The unique identifier of the employee requesting delete access
     * @return true if the employee can delete the blog, false otherwise
     * @see #canEmployeeEditBlog(String, String) for the underlying permission logic
     * @since 1.0
     */
    @Override
    public boolean canEmployeeDeleteBlog(String blogId, String employeeId) {
        return canEmployeeEditBlog(blogId, employeeId);
    }

    /**
     * Checks if an employee has permission to publish a specific blog.
     *
     * Currently uses the same logic as edit permissions - only the blog
     * employee author can publish their own blogs.
     *
     * @param blogId The unique identifier of the blog
     * @param employeeId The unique identifier of the employee requesting publish access
     * @return true if the employee can publish the blog, false otherwise
     * @see #canEmployeeEditBlog(String, String) for the underlying permission logic
     * @since 1.0
     */
    @Override
    public boolean canEmployeePublishBlog(String blogId, String employeeId) {
        return canEmployeeEditBlog(blogId, employeeId);
    }
    
    
    ///////////////////////////////////////////////////// Helpers //////////////////////////////////////////////////////

    /**
     * Creates a new blog post with automatic slug generation and initialization.
     *
     * This method handles the complete blog creation process including:
     * - Automatic slug generation from title if not provided
     * - Setting creation and update timestamps
     * - Initializing default status as draft
     * - Setting all counters to zero
     *
     * @param blog The blog entity to create (must have title and employee author)
     * @return The saved blog entity with generated ID and slug
     * @throws RuntimeException if blog validation fails
     *
     * @see #generateUniqueSlug(String) for slug generation logic
     * @since 1.0
     */
    @Transactional
    public Blog createBlog(Blog blog) {
        logger.info("Creating new blog with title: {}", blog.getTitle());

        // Generate unique slug if not provided
        if (blog.getSlug() == null || blog.getSlug().isEmpty()) {
            blog.setSlug(generateUniqueSlug(blog.getTitle()));
        }

        // Set timestamps
        LocalDateTime now = LocalDateTime.now();
        blog.setCreatedDate(now);
        blog.setUpdatedDate(now);

        // Set default status if not provided
        if (blog.getStatus() == null) {
            blog.setStatus(BlogStatus.draft);
        }

        // Initialize counters
        blog.setViewCount(0);
        blog.setCommentCount(0);
        blog.setLikeCount(0);
        blog.setShareCount(0);

        return blogRepository.save(blog);
    }

    /**
     * Converts Spring's Page object to project's PageDto format
     *
     * @param page Spring Page object
     * @return PageDto with proper field mapping
     */
    private PageDto<Blog> convertToPageDto(Page<Blog> page) {
        PageDto<Blog> pageDto = new PageDto<>();
        pageDto.setItems(page.getContent());                    // content -> items
        pageDto.setTotalNoOfItems(page.getTotalElements());     // totalElements -> totalNoOfItems
        pageDto.setPageNumber(page.getNumber());                // number -> pageNumber
        pageDto.setItemsPerPage(page.getSize());                // size -> itemsPerPage
        return pageDto;
    }

    /**
     * Converts CreateBlogDto to Blog entity.
     *
     * @param createBlogDto The DTO to convert
     * @return Blog entity with data from DTO
     */
    private Blog convertCreateDtoToEntity(CreateBlogDto createBlogDto) {
        Blog blog = new Blog();
        blog.setTitle(createBlogDto.getTitle());
        blog.setSlug(createBlogDto.getSlug());
        blog.setContent(createBlogDto.getContent());
        blog.setStatus(createBlogDto.getStatus());

        // Convert media if provided
        if (createBlogDto.getMedia() != null) {
            blog.setMedia(generateMediaWithWrapper(createBlogDto.getMedia()));
        }

        return blog;
    }

    /**
     * Converts CreateBlogWithMediaDto to Blog entity.
     *
     * @param createBlogDto The DTO to convert
     * @return Blog entity with data from DTO (without media, which is handled separately)
     */
    private Blog convertCreateWithMediaDtoToEntity(CreateBlogWithMediaDto createBlogDto) {
        Blog blog = new Blog();
        blog.setTitle(createBlogDto.getTitle());
        blog.setSlug(createBlogDto.getSlug());
        blog.setContent(createBlogDto.getContent());
        blog.setStatus(createBlogDto.getStatus());
        blog.setAllowComments(createBlogDto.isAllowComments());
        blog.setAllowReactions(createBlogDto.isAllowReactions());
        // Note: Media is handled separately via generateMediaWithWrapper
        return blog;
    }

    /**
     * Generates MediaWrapper entities with associated Media entities from CreateMediaDto list.
     * This method follows the same pattern as the post service for media handling.
     *
     * @param mediasRequest List of CreateMediaDto objects
     * @return List of MediaWrapper entities with saved Media references
     */
    public List<MediaWrapper> generateMediaWithWrapper(List<CreateMediaDto> mediasRequest) {
        if (mediasRequest == null || mediasRequest.isEmpty()) {
            return new ArrayList<>();
        }

        List<MediaWrapper> createdMedias = new ArrayList<>();
        for (CreateMediaDto createMediaDto : mediasRequest) {
            // Create and populate Media entity
            Media newMedia = new Media();
            newMedia.setMediaType(createMediaDto.getMediaType());
            newMedia.setSource(createMediaDto.getSource());
            newMedia.setVideoUrl(createMediaDto.getVideoUrl());
            newMedia.setVideoSize(createMediaDto.getVideoSize());
            newMedia.setCreationDate(DateTime.now().toDate());
            newMedia.setLastUpdate(DateTime.now().toDate());
            newMedia.setThumbnailCaptureUrl(createMediaDto.getThumbnailCaptureUrl());
            newMedia.setThumbnailClipUrl(createMediaDto.getThumbnailClipUrl());
            newMedia.setTrimmedVideoUrl(createMediaDto.getTrimmedVideoUrl());
            newMedia.setOverlays(createMediaDto.getOverlays());
            newMedia.setTitle(createMediaDto.getTitle());
            newMedia.setLatitude(createMediaDto.getLatitude());
            newMedia.setLongtuid(createMediaDto.getLongtuid());
            newMedia.setVideoDuration(createMediaDto.getVideoDuration());
            newMedia.setVideoDurationMS(createMediaDto.getVideoDurationMS());
            newMedia.setDescription(createMediaDto.getDescription());
            newMedia.setClipStartTimecode(createMediaDto.getClipStartTimecode());
            newMedia.setClipEndTimecode(createMediaDto.getClipEndTimecode());
            newMedia.setStartTime(createMediaDto.getStartTime());
            newMedia.setEndTime(createMediaDto.getEndTime());
            newMedia.setTags(createMediaDto.getTags());

            // Handle tagged users if provided
            if (createMediaDto.getTaggedUsers() != null && !createMediaDto.getTaggedUsers().isEmpty()) {
                List<ObjectId> usersId = createMediaDto.getTaggedUsers().stream()
                        .map(RefranceModelDto::getId)
                        .map(ObjectId::new)
                        .collect(Collectors.toList());
                Aggregation taggedquery = queryNormalizeService.getUsersList(Criteria.where("_id").in(usersId));
                List<User> users = mongoTemplate.aggregate(taggedquery, "user", User.class).getMappedResults();
                newMedia.setTaggedUsers(users);
            }

            // Set image category for images
            if (createMediaDto.getMediaType() == MediaType.image) {
                try {
                    String category = createMediaDto.getSource().split("/")[1];
                    ImageCategory imageCategory = ImageCategory.valueOf(category);
                    newMedia.setImageCategory(imageCategory);
                } catch (Exception ignored) {
                    // Ignore if category cannot be determined
                }
            }

            // Save Media entity
            Media savedMedia = mediaRepository.save(newMedia);

            // Create MediaWrapper that references the saved Media
            MediaWrapper mediaWrapper = new MediaWrapper();
            mediaWrapper.setMedia(savedMedia);
            mediaWrapper.setUrl(newMedia.getMediaType() == MediaType.video ? newMedia.getVideoUrl() : newMedia.getSource());
            mediaWrapper.setType(createMediaDto.getMediaType());
            mediaWrapper.setCaption(createMediaDto.getTitle());
            mediaWrapper.setMainImage(createMediaDto.isMainImage());

            createdMedias.add(mediaWrapper);
        }

        return createdMedias;
    }
}
