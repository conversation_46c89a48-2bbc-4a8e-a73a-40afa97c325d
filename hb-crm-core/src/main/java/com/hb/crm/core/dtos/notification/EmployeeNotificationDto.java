package com.hb.crm.core.dtos.notification;

import com.hb.crm.core.Enums.*;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Map;

@Data
public class EmployeeNotificationDto {
    private String id;
    private String subject;
    private String body;
    private boolean sent;
    private boolean read;
    private boolean hidden;
    private boolean muted;
    private int retryCount;
    private LocalDateTime readAt;
    private LocalDateTime sentAt;
    private LocalDateTime lastTriedAt;
    private LocalDateTime createdAt;
    private String employeeId;
    private EmployeeNotificationType type;
    private NotificationChannelType channelType;
    private String entityId;
    private String image;
    private Map<String, String> payload;
    private String icon;
    private NotificationEntityType entityType;
    private String entitySlug;
    private String username;
    private IconType iconType;
    private UserType navigatedUsertype;
}
