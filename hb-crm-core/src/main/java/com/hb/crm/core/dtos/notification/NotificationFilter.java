// com.hb.crm.core.dtos.notification.NotificationFilter.java
package com.hb.crm.core.dtos.notification;

import lombok.Getter;
import lombok.Setter;

@Getter @Setter
public class NotificationFilter {
    // "all" | "sent" | "unsent" (from your Angular control). Null/blank => all
    private String sent;

    // free text: body OR subject OR user.username (case-insensitive)
    private String searchText;

    // exact equals (optional)
    private String channel;  // maps to channelType
    private String type;     // maps to type

    // "Opened" | "Delivered" | "Failed" | "" (derived from sent/read)
    private String status;

    // optional explicit sorting from the table (prop names match Mongo field names)
    private String sortProp; // e.g. "lastTriedAt" | "subject" | "user.username"
    private String sortDir;  // "asc" | "desc"
}
