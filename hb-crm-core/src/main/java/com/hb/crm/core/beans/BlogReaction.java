package com.hb.crm.core.beans;

import lombok.Getter;
import lombok.Setter;
import org.springframework.data.mongodb.core.mapping.DBRef;
import org.springframework.data.mongodb.core.mapping.Document;

@Setter
@Getter
@Document(collection = "blogReaction")
public class BlogReaction extends Reaction {
    @DBRef
    private Blog blog;
    
    public BlogReaction() {
        super();
    }
    
    public BlogReaction(Blog blog, User user) {
        super();
        this.blog = blog;
        this.setUser(user);
    }
}
