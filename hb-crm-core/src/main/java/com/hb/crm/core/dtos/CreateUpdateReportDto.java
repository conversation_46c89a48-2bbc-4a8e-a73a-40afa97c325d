package com.hb.crm.core.dtos;

import com.hb.crm.core.Enums.ReportEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class CreateUpdateReportDto {

    @Schema(description = "Detailed reason for reporting the entity.")
    private String reason;
    private String userId;

    @Schema(description = "Unique identifier of the entity being reported.")
    private List<String> entityId;

    @Schema(
            description = "Type of the entity being reported.",
            example = "Post"
    )
    private ReportEntity entityName;
}