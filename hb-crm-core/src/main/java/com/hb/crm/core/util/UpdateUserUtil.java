package com.hb.crm.core.util;

import com.hb.crm.core.searchBeans.searchPackage;
import com.hb.crm.core.searchBeans.searchPost;
import com.hb.crm.core.searchBeans.searchStory;
import com.hb.crm.core.searchBeans.simpleUserInfo;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

@Service
public class UpdateUserUtil {
    private final MongoTemplate mongoTemplate;

    public UpdateUserUtil(@Qualifier("mongoTemplate2") MongoTemplate mongoTemplate) {
        this.mongoTemplate = mongoTemplate;
    }

    @Async
    public void updateSimpleUserInfoInSearchPosts(String userId, simpleUserInfo updatedUserInfo) {
        // Update in 'user' field
        Query queryUserField = Query.query(Criteria.where("user._id").is(new ObjectId(userId)));
        Update updateUser = new Update()
                .set("user.username", updatedUserInfo.getUsername())
                .set("user.firstName", updatedUserInfo.getFirstName())
                .set("user.lastName", updatedUserInfo.getLastName())
                .set("user.usertype", updatedUserInfo.getUsertype())
                .set("user.coverImage", updatedUserInfo.getCoverImage())
                .set("user.profileImage", updatedUserInfo.getProfileImage());


        Query packageQueryUserField = Query.query(Criteria.where("infulancer._id").is(new ObjectId(userId)));
        Update packageUpdateUser = new Update()
                .set("infulancer.username", updatedUserInfo.getUsername())
                .set("infulancer.firstName", updatedUserInfo.getFirstName())
                .set("infulancer.lastName", updatedUserInfo.getLastName())
                .set("infulancer.usertype", updatedUserInfo.getUsertype())
                .set("infulancer.coverImage", updatedUserInfo.getCoverImage())
                .set("infulancer.profileImage", updatedUserInfo.getProfileImage());
        mongoTemplate.updateMulti(queryUserField, updateUser, searchPost.class);
        mongoTemplate.updateMulti(packageQueryUserField, packageUpdateUser, searchPackage.class);
        mongoTemplate.updateMulti(queryUserField, updateUser, searchStory.class);

        // Update inside 'taggedUsers' list
        Query queryTaggedUsers = Query.query(Criteria.where("taggedUsers._id").is(new ObjectId(userId)));
        Update updateTagged = new Update()
                .set("taggedUsers.$[elem].username", updatedUserInfo.getUsername())
                .set("taggedUsers.$[elem].firstName", updatedUserInfo.getFirstName())
                .set("taggedUsers.$[elem].lastName", updatedUserInfo.getLastName())
                .set("taggedUsers.$[elem].usertype", updatedUserInfo.getUsertype())
                .set("taggedUsers.$[elem].coverImage", updatedUserInfo.getCoverImage())
                .set("taggedUsers.$[elem].profileImage", updatedUserInfo.getProfileImage())
                .filterArray(Criteria.where("elem._id").is(new ObjectId(userId))); // For positional all updates
        mongoTemplate.updateMulti(queryUserField, updateUser, searchPost.class);
         mongoTemplate.updateMulti(queryTaggedUsers, updateTagged, searchStory.class);
    }

}
