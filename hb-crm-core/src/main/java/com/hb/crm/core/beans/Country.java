package com.hb.crm.core.beans;

import lombok.Getter;
import lombok.Setter;
import org.springframework.data.annotation.Id;

import java.util.List;

@Getter
@Setter
public class Country {

    @Id
    private String id;
    private String name;
    private String description;
    private String mapUrl;
    private List<MediaWrapper> medias;
    private Double latitude; // Latitude for map location
    private Double longitude; // Longitude for map location
    private boolean airportSynced = false;
//: Add fields like double radiusKm and double[] boundingBox to Country, City, and Area for caching.
    private Double radiusKm;
    private double[] boundingBox;
    private String countryCode;

    public Country() {
    }

    public Country(String id, String name) {
        this.id = id;
        this.name = name;
    }
    public Country(String id) {
        this.id = id;
    }
}
