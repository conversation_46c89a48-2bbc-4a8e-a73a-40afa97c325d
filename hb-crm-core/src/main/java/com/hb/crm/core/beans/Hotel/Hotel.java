package com.hb.crm.core.beans.Hotel;


import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

public class Hotel {
    private ArrayList<Room> rooms;
    private String propertyId;
    private String propertyReferenceId;
    private String name;
    private  float latitude;
    private  float longitude;
    private List<String> addressLine;
    private  String email;


    public String getAddressLink() {
        return addressLink;
    }

    public void setAddressLink(String addressLink) {
        this.addressLink = addressLink;
    }


    private  String addressLink;
    private  String city;
    private String cityId;
    private String countryId;
    private  String stateProvince;
    private  String countryCode;
    private  ArrayList<String> description;
    private ArrayList<Amenity> amenities;
    private ArrayList<Image> images;
    private  String amenityDescriptions;
    private  BigDecimal nightlyAveragePrice;
    private  BigDecimal price;

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public List<SocialMediaLink> getSocialMediaLinks() {
        return socialMediaLinks;
    }

    public void setSocialMediaLinks(List<SocialMediaLink> socialMediaLinks) {
        this.socialMediaLinks = socialMediaLinks;
    }

    private List<SocialMediaLink> socialMediaLinks;
    private String checkinDate;
    private String checkoutDate;

    public String getCheckinDate() {
        return checkinDate;
    }

    public void setCheckinDate(String checkinDate) {
        this.checkinDate = checkinDate;
    }

    public String getCheckoutDate() {
        return checkoutDate;
    }

    public void setCheckoutDate(String checkoutDate) {
        this.checkoutDate = checkoutDate;
    }

    public BigDecimal getNightlyAveragePrice() {
        return nightlyAveragePrice;
    }

    public void setNightlyAveragePrice(BigDecimal nightlyAveragePrice) {
        this.nightlyAveragePrice = nightlyAveragePrice;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public String getAmenityDescriptions() {
        return amenityDescriptions;
    }

    public void setAmenityDescriptions(String amenityDescriptions) {
        this.amenityDescriptions = amenityDescriptions;
    }

    public float getLatitude() {
        return latitude;
    }

    public void setLatitude(float latitude) {
        this.latitude = latitude;
    }

    public float getLongitude() {
        return longitude;
    }

    public void setLongitude(float longitude) {
        this.longitude = longitude;
    }

    public List<String> getAddressLine() {
        return addressLine;
    }

    public void setAddressLine(List<String> addressLine) {
        this.addressLine = addressLine;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getStateProvince() {
        return stateProvince;
    }

    public void setStateProvince(String stateProvince) {
        this.stateProvince = stateProvince;
    }

    public String getCountryCode() {
        return countryCode;
    }

    public void setCountryCode(String countryCode) {
        this.countryCode = countryCode;
    }

    public ArrayList<String> getDescription() {
        return description;
    }

    public void setDescription(ArrayList<String> description) {
        this.description = description;
    }

    public ArrayList<Amenity> getAmenities() {
        return amenities;
    }

    public void setAmenities(ArrayList<Amenity> amenities) {
        this.amenities = amenities;
    }

    public ArrayList<Image> getImages() {
        return images;
    }

    public void setImages(ArrayList<Image> images) {
        this.images = images;
    }

    public ArrayList<Room> getRooms() {
        return rooms;
    }

    public void setRooms(ArrayList<Room> rooms) {
        this.rooms = rooms;
    }

    public String getPropertyId() {
        return propertyId;
    }

    public void setPropertyId(String propertyId) {
        this.propertyId = propertyId;
    }

    public String getPropertyReferenceId() {
        return propertyReferenceId;
    }

    public void setPropertyReferenceId(String propertyReferenceId) {
        this.propertyReferenceId = propertyReferenceId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCityId() {
        return cityId;
    }

    public void setCityId(String cityId) {
        this.cityId = cityId;
    }

    public String getCountryId() {
        return countryId;
    }

    public void setCountryId(String countryId) {
        this.countryId = countryId;
    }
}


