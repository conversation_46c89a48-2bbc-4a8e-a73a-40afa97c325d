package com.hb.crm.core.config.AOP;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.data.mongodb.config.EnableMongoAuditing;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;

/**
 * Configuration class for audit trail functionality
 * Enables AOP, MongoDB auditing, and async processing
 */
@Configuration
@EnableAspectJAutoProxy
@EnableMongoAuditing
@EnableAsync
@EnableScheduling
public class AuditConfiguration {

    /**
     * Configuration properties for audit settings
     */
    @Bean
    @ConfigurationProperties(prefix = "audit")
    public AuditProperties auditProperties() {
        return new AuditProperties();
    }

    /**
     * Thread pool executor for async audit processing
     */
    @Bean(name = "auditTaskExecutor")
    public Executor auditTaskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(2);
        executor.setMaxPoolSize(5);
        executor.setQueueCapacity(100);
        executor.setThreadNamePrefix("audit-");
        executor.setRejectedExecutionHandler(new java.util.concurrent.ThreadPoolExecutor.CallerRunsPolicy());
        executor.initialize();
        return executor;
    }

    /**
     * Audit properties configuration class
     */
    public static class AuditProperties {
        private boolean enabled = true;
        private boolean async = true;
        private boolean captureArguments = true;
        private boolean captureReturnValue = false;
        private int maxDataSize = 10000;
        private int retentionDays = 365;
        private boolean archiveEnabled = true;
        private int exportMaxRecords = 10000;
        private String environment = "UNKNOWN";
        private boolean enableSecurityAuditing = true;
        private boolean enablePerformanceAuditing = true;
        private boolean enableDataChangeAuditing = true;
        private boolean enableComplianceAuditing = true;

        // Getters and setters
        public boolean isEnabled() {
            return enabled;
        }

        public void setEnabled(boolean enabled) {
            this.enabled = enabled;
        }

        public boolean isAsync() {
            return async;
        }

        public void setAsync(boolean async) {
            this.async = async;
        }

        public boolean isCaptureArguments() {
            return captureArguments;
        }

        public void setCaptureArguments(boolean captureArguments) {
            this.captureArguments = captureArguments;
        }

        public boolean isCaptureReturnValue() {
            return captureReturnValue;
        }

        public void setCaptureReturnValue(boolean captureReturnValue) {
            this.captureReturnValue = captureReturnValue;
        }

        public int getMaxDataSize() {
            return maxDataSize;
        }

        public void setMaxDataSize(int maxDataSize) {
            this.maxDataSize = maxDataSize;
        }

        public int getRetentionDays() {
            return retentionDays;
        }

        public void setRetentionDays(int retentionDays) {
            this.retentionDays = retentionDays;
        }

        public boolean isArchiveEnabled() {
            return archiveEnabled;
        }

        public void setArchiveEnabled(boolean archiveEnabled) {
            this.archiveEnabled = archiveEnabled;
        }

        public int getExportMaxRecords() {
            return exportMaxRecords;
        }

        public void setExportMaxRecords(int exportMaxRecords) {
            this.exportMaxRecords = exportMaxRecords;
        }

        public String getEnvironment() {
            return environment;
        }

        public void setEnvironment(String environment) {
            this.environment = environment;
        }

        public boolean isEnableSecurityAuditing() {
            return enableSecurityAuditing;
        }

        public void setEnableSecurityAuditing(boolean enableSecurityAuditing) {
            this.enableSecurityAuditing = enableSecurityAuditing;
        }

        public boolean isEnablePerformanceAuditing() {
            return enablePerformanceAuditing;
        }

        public void setEnablePerformanceAuditing(boolean enablePerformanceAuditing) {
            this.enablePerformanceAuditing = enablePerformanceAuditing;
        }

        public boolean isEnableDataChangeAuditing() {
            return enableDataChangeAuditing;
        }

        public void setEnableDataChangeAuditing(boolean enableDataChangeAuditing) {
            this.enableDataChangeAuditing = enableDataChangeAuditing;
        }

        public boolean isEnableComplianceAuditing() {
            return enableComplianceAuditing;
        }

        public void setEnableComplianceAuditing(boolean enableComplianceAuditing) {
            this.enableComplianceAuditing = enableComplianceAuditing;
        }
    }
}
