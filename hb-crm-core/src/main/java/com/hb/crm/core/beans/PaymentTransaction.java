package com.hb.crm.core.beans;

import com.hb.crm.core.CombinedKeys.SubscribeKey;
import com.hb.crm.core.Enums.PaymentGateway;
import com.hb.crm.core.Enums.TransactionStatus;
import lombok.Getter;
import lombok.Setter;
import org.springframework.data.annotation.Id;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Getter
@Setter
public class PaymentTransaction {
    @Id
    private String id;
    private SubscribeKey subscribeId;
    private BigDecimal amount;
    private TransactionStatus status; // e.g. INITIATED, SUCCESS, FAILURE
    private String paymentGatewayReference;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    private String failureMessage ;
    private String bdfUrl ;
    private PaymentGateway paymentGatewayMethod;
//paymentPhoneNumber
    private String paymentPhoneNumber;
    
    // Refund-related fields
    private String refundId;
    private BigDecimal refundAmount;
    private String refundReason;
    private LocalDateTime refundedAt;
    
    // Cancellation-related fields
    private String cancellationReason;
    private LocalDateTime canceledAt;

}
