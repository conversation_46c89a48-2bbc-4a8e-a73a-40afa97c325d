package com.hb.crm.core.repositories.custom;

import com.hb.crm.core.beans.AuditLog.AuditLog;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public class AuditLogCustomRepository {

    private final MongoTemplate mongoTemplate;

    public AuditLogCustomRepository(MongoTemplate mongoTemplate) {
        this.mongoTemplate = mongoTemplate;
    }

    public Page<AuditLog> searchAuditLogs(
            String entityName,
            String entityId,
            String actionType,
            String userEmail,
            String userId,
            String userName,
            String ipAddress,
            LocalDateTime fromDate,
            LocalDateTime toDate,
            String newValueSearchValue,
            String newValueKey,
            String oldValueSearchValue,
            String oldValueKey,
            Pageable pageable
    ) {
        Query query = new Query();

        // Fuzzy search on main fields
        if (entityName != null) query.addCriteria(Criteria.where("entityName").regex(entityName, "i"));
        if (entityId != null) query.addCriteria(Criteria.where("entityId").regex(entityId, "i"));
        if (actionType != null) query.addCriteria(Criteria.where("actionType").regex(actionType, "i"));
        if (userEmail != null) query.addCriteria(Criteria.where("userEmail").regex(userEmail, "i"));
        if (userId != null) query.addCriteria(Criteria.where("userId").regex(userId, "i"));
        if (userName != null) query.addCriteria(Criteria.where("userName").regex(userName, "i"));
        if (ipAddress != null) query.addCriteria(Criteria.where("ipAddress").regex(ipAddress, "i"));

        // Date range
        if (fromDate != null && toDate != null) query.addCriteria(Criteria.where("createdAt").gte(fromDate).lte(toDate));
        else if (fromDate != null) query.addCriteria(Criteria.where("createdAt").gte(fromDate));
        else if (toDate != null) query.addCriteria(Criteria.where("createdAt").lte(toDate));

        // Fuzzy search in newValue map
        if (newValueSearchValue != null) {
            if (newValueKey != null)
                query.addCriteria(Criteria.where("newValue." + newValueKey).regex(newValueSearchValue, "i"));
            else
                query.addCriteria(Criteria.where("newValue").regex(newValueSearchValue, "i"));
        }

        // Fuzzy search in oldValue map
        if (oldValueSearchValue != null) {
            if (oldValueKey != null)
                query.addCriteria(Criteria.where("oldValue." + oldValueKey).regex(oldValueSearchValue, "i"));
            else
                query.addCriteria(Criteria.where("oldValue").regex(oldValueSearchValue, "i"));
        }

        // Pagination
        long count = mongoTemplate.count(query, AuditLog.class);
        query.with(pageable);

        List<AuditLog> list = mongoTemplate.find(query, AuditLog.class);
        return new PageImpl<>(list, pageable, count);
    }
}
