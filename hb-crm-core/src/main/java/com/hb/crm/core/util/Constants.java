package com.hb.crm.core.util;

import com.hb.crm.core.Enums.NotificationType;
import com.hb.crm.core.Enums.EmployeeNotificationType;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import java.util.List;

import static com.hb.crm.core.Enums.NotificationType.*;
import static com.hb.crm.core.Enums.EmployeeNotificationType.*;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class Constants {

    public static final String PRIVATE_TOPIC_PREFIX = "chat/private/";
    public static final String GROUP_TOPIC_PREFIX = "chat/group/";


    // Package Notifications
    public static final List<NotificationType> PACKAGE_NOTIFICATIONS = List.of(
            MatchedMoodsNewPackage,
            FollowedInfluencerNewPackage,
            InfluencerNewPackage,
            InfluencerUpdatePackage,
            PackageCapacitySubscribedUsers,
            PackageCapacityFavouriteUsers
    );
    public static final List<NotificationType> PACKAGE_NOTIFICATIONS_TWM = List.of(
            MatchedMoodsNewPackage,
            FollowedInfluencerNewPackage,
            InfluencerNewPackage,
            InfluencerUpdatePackage,
            PackageCapacitySubscribedUsers,
            PackageCapacityFavouriteUsers
    );
    public static final List<NotificationType> PACKAGE_NOTIFICATIONS_FOLLOW_ME = List.of(

    );




    // Posts, Stories, Reels, and Livestreams Notifications
    public static final List<NotificationType> POST_STORY_NOTIFICATIONS = List.of(
            NewStoryFromFollowedInfluencer,
            NewPostFromFollowedInfluencer,
            NewPostFromFavouritePackageUser,
            NewStoryFromFavouritePackageUser
    );
    public static final List<NotificationType> POST_NOTIFICATIONS = List.of(
            NewPostFromFollowedInfluencer,
            NewPostFromFavouritePackageUser
    );

    public static final List<NotificationType> STORY_NOTIFICATIONS = List.of(
            NewStoryFromFollowedInfluencer,
            NewStoryFromFavouritePackageUser
    );
    public static final List<NotificationType> REEL_NOTIFICATIONS = List.of(

    );
    public static final List<NotificationType> LIVESTREAMS_NOTIFICATIONS = List.of(

    );




    // Chat Notifications
    public static final List<NotificationType> CHAT_NOTIFICATIONS = List.of(
            ChatMessage,
            ChatMessageVideo,
            ChatMessageAudio,
            ChatMessageFile,
            ChatMessagePoll,
            ChatMessageImage,
            GroupChatMessage,
            GroupChatMessageVideo,
            GroupChatMessageAudio,
            GroupChatMessageFile,
            GroupChatMessagePoll,
            GroupChatMessageImage
    );
    public static final List<NotificationType> SUPPORT_CHAT_NOTIFICATIONS = List.of(
            ChatMessage,
            ChatMessageVideo,
            ChatMessageAudio,
            ChatMessageFile,
            ChatMessagePoll,
            ChatMessageImage
    );
    public static final List<NotificationType> GROUP_CHAT_NOTIFICATIONS = List.of(
            GroupChatMessage,
            GroupChatMessageVideo,
            GroupChatMessageAudio,
            GroupChatMessageFile,
            GroupChatMessagePoll,
            GroupChatMessageImage
    );


    // Interaction Notifications
    public static final List<NotificationType> INTERACTION_NOTIFICATIONS = List.of(
            CommentedOnLikedPost,
            CommentedOnMyPost,
            ReactOnLikedPost,
            ReactOnLikedStory,
            ReactOnMyComment,
            ReactOnMyPost,
            ReactOnMyReel,
            ReactOnMyStory,
            ReplyComment
    );
    public static final List<NotificationType> REACT_NOTIFICATIONS = List.of(
            ReactOnLikedPost,
            ReactOnLikedStory,
            ReactOnMyComment,
            ReactOnMyPost,
            ReactOnMyReel,
            ReactOnMyStory
    );
    public static final List<NotificationType> COMMENT_NOTIFICATIONS = List.of(
            CommentedOnLikedPost,
            CommentedOnMyPost,
            ReplyComment
    );





    // Other Notifications
    public static final List<NotificationType> ACTION_NOTIFICATIONS = List.of(
            SubmittedSubscribe,
            Follow,
            PaymentSucceeded,
            AcceptPackage,
            RejectPackage,
            PostPackage
    );
    public static final List<NotificationType> ADMIN_REGISTER_NOTIFICATIONS = List.of(
            SuccessLoginAdmin,
            FailedLoginAdmin,
            SuccessPasswordChange,
            FailedPasswordChange,
            LiveStarted
    );

    // Employee Notification Type Groupings
    public static final List<EmployeeNotificationType> EMPLOYEE_PACKAGE_NOTIFICATIONS = List.of(
            CREATE_PACKAGE_ADMIN,
            CREATE_PACKAGE_INFLUENCER,
            PACKAGE_ACTIVATED,
            PACKAGE_EXPIRED,
            USER_SUBSCRIBE_PACKAGE,
            PACKAGE_FULL,
            PACKAGE_UPDATE_REQUEST
    );

    public static final List<EmployeeNotificationType> EMPLOYEE_POST_NOTIFICATIONS = List.of(
            POST_REPORTED
    );

    public static final List<EmployeeNotificationType> EMPLOYEE_ADMIN_REGISTER_NOTIFICATIONS = List.of(
            LOGIN_SUCCEEDED,
            LOGIN_FAILED,
            PASSWORD_CHANGED,
            PASSWORD_CHANGE_FAILED
    );

    public static final List<EmployeeNotificationType> EMPLOYEE_USER_NOTIFICATIONS = List.of(
            NEW_ADMIN_JOINED,
            NEW_INFLUENCER_JOINED,
            NEW_TRAVELER_CREATED,
            ACCOUNT_ACTIVATED,
            ACCOUNT_DEACTIVATED
    );

}
