package com.hb.crm.core.repositories;

import com.hb.crm.core.beans.Notification.EmployeeNotificationTemplate;
import com.hb.crm.core.Enums.EmployeeNotificationType;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface EmployeeNotificationTemplateRepository extends MongoRepository<EmployeeNotificationTemplate, String> {

    List<EmployeeNotificationTemplate> findByNotificationType(EmployeeNotificationType notificationType);

    List<EmployeeNotificationTemplate> findByStatusTrue();

    Optional<EmployeeNotificationTemplate> findByNotificationTypeAndStatusTrue(EmployeeNotificationType notificationType);

    // Fallback fuzzy search using regex for partial matches
    @Query("{'$or': [" +
           "{'push.subject': {'$regex': ?0, '$options': 'i'}}, " +
           "{'push.body': {'$regex': ?0, '$options': 'i'}}, " +
           "{'inApp.subject': {'$regex': ?0, '$options': 'i'}}, " +
           "{'inApp.body': {'$regex': ?0, '$options': 'i'}}, " +
           "{'email.subject': {'$regex': ?0, '$options': 'i'}}, " +
           "{'email.body': {'$regex': ?0, '$options': 'i'}}, " +
           "{'sms.body': {'$regex': ?0, '$options': 'i'}}, " +
           "{'whatsApp.body': {'$regex': ?0, '$options': 'i'}}" +
           "]}")
    Page<EmployeeNotificationTemplate> findByFuzzySearch(String searchTerm, Pageable pageable);

    long countByStatus(boolean status);

    /**
     * Count active templates by notification type
     */
    long countByNotificationTypeAndStatusTrue(EmployeeNotificationType notificationType);
}
