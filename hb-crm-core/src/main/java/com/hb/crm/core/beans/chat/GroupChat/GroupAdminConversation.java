package com.hb.crm.core.beans.chat.GroupChat;

import com.hb.crm.core.beans.Employee;
import lombok.Getter;
import lombok.Setter;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.DBRef;
import org.springframework.data.mongodb.core.mapping.Document;

import java.time.LocalDateTime;

@Getter
@Setter
@Document
public class GroupAdminConversation {
    @Id
    String id;
    @DBRef(lazy = true)
    Employee employee;
    @DBRef(lazy = true)
    GroupConversation conversation;

    LocalDateTime startTime;
    LocalDateTime endTime;
}
