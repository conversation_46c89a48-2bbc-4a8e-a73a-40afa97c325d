package com.hb.crm.core.config.AOP;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.hb.crm.core.Enums.AuditAction;
import com.hb.crm.core.Enums.AuditEntityType;
import com.hb.crm.core.Enums.AuditStatus;
import com.hb.crm.core.beans.AopAudit.AuditContext;
import com.hb.crm.core.beans.AopAudit.AopAuditLog;
import com.hb.crm.core.services.interfaces.AuditService;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import jakarta.servlet.http.HttpServletRequest;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;

/**
 * Comprehensive AOP Aspect for automatic audit trail generation
 * Intercepts service method calls and captures audit information
 */
@Aspect
@Component
public class AuditAspect {

    private static final Logger logger = LoggerFactory.getLogger(AuditAspect.class);

    @Autowired
    private AuditService auditService;

    @Autowired
    private ObjectMapper objectMapper;

//    @Value("${audit.enabled:true}")
    private boolean auditEnabled = false;

    @Value("${audit.async:true}")
    private boolean asyncAudit;

    @Value("${audit.capture-arguments:true}")
    private boolean captureArguments;

    @Value("${audit.capture-return-value:false}")
    private boolean captureReturnValue;

    @Value("${audit.max-data-size:10000}")
    private int maxDataSize;

    // Excluded packages/classes from auditing
    private static final Set<String> EXCLUDED_PACKAGES = Set.of(
            "com.hb.crm.core.config",
            "com.hb.crm.core.services.AuditServiceImpl",
            "com.hb.crm.core.services.interfaces.AuditService",
            "com.hb.crm.core.repositories.AuditLogRepository"
    );

    // Sensitive fields that should not be logged
    private static final Set<String> SENSITIVE_FIELDS = Set.of(
            "password", "token", "secret", "key", "credential", "authorization",
            "ssn", "creditcard", "cvv", "pin", "otp", "privatekey"
    );

    /**
     * Pointcut for all service layer methods
     */
    @Pointcut("execution(* com.hb.crm..services..*(..)) && " +
            "!execution(* com.hb.crm.core.services.interfaces.AuditService.*(..))")
    public void serviceLayer() {}

    /**
     * Pointcut for all repository save/delete methods
     */
    @Pointcut("execution(* com.hb.crm..repositories..*(..)) && " +
            "!execution(* com.hb.crm.core.repositories.AuditLogRepository.*(..))")
    public void repositoryMutations() {}

    /**
     * Pointcut for controller methods
     */
    @Pointcut("execution(* com.hb.crm..controllers..*(..))")
    public void controllerLayer() {}

    /**
     * Around advice for service layer methods
     */
    @Around("serviceLayer() && !repositoryMutations()")
    public Object auditServiceMethods(ProceedingJoinPoint joinPoint) throws Throwable {
        if (!auditEnabled || isExcluded(joinPoint)) {
            return joinPoint.proceed();
        }

        return executeWithAudit(joinPoint, determineActionFromMethod(joinPoint));
    }

    /**
     * Around advice for repository mutation methods
     */
    @Around("repositoryMutations()")
    public Object auditRepositoryMutations(ProceedingJoinPoint joinPoint) throws Throwable {
        if (!auditEnabled || isExcluded(joinPoint)) {
            return joinPoint.proceed();
        }

        return executeWithAudit(joinPoint, determineActionFromRepositoryMethod(joinPoint));
    }

    /**
     * Before advice for controller methods to set up audit context
     */
    @Before("controllerLayer()")
    public void setupAuditContext(JoinPoint joinPoint) {
        if (!auditEnabled) {
            return;
        }

        try {
            AuditContext context = AuditContext.createContext();
            populateContextFromRequest(context);
            context.setModuleName(extractModuleName(joinPoint));
            context.setEndpoint(extractEndpoint(joinPoint));
        } catch (Exception e) {
            logger.warn("Failed to setup audit context", e);
        }
    }

    /**
     * After advice for controller methods to clean up audit context
     */
    @After("controllerLayer()")
    public void cleanupAuditContext() {
        AuditContext.clearCurrentContext();
    }

    /**
     * Execute method with audit logging
     */
    private Object executeWithAudit(ProceedingJoinPoint joinPoint, AuditAction action) throws Throwable {
        LocalDateTime startTime = LocalDateTime.now();
        AopAuditLog auditLog = null;
        Object result = null;
        Throwable exception = null;

        try {
            // Create audit log entry
            auditLog = createAuditLog(joinPoint, action, startTime);
            
            // Capture method arguments if enabled
            if (captureArguments) {
                captureMethodArguments(auditLog, joinPoint);
            }

            // Execute the method
            result = joinPoint.proceed();

            // Capture return value if enabled and not sensitive
            if (captureReturnValue && result != null) {
                captureReturnValue(auditLog, result);
            }

            // Mark as successful
            auditLog.setStatus(AuditStatus.SUCCESS);
            
            return result;

        } catch (Throwable throwable) {
            exception = throwable;
            if (auditLog != null) {
                auditLog.setStatus(AuditStatus.fromException(throwable));
                auditLog.setErrorMessage(throwable.getMessage());
                if (throwable.getStackTrace() != null && throwable.getStackTrace().length > 0) {
                    auditLog.setStackTrace(Arrays.toString(Arrays.copyOf(throwable.getStackTrace(), 5)));
                }
            }
            throw throwable;

        } finally {
            if (auditLog != null) {
                // Calculate duration
                long duration = java.time.Duration.between(startTime, LocalDateTime.now()).toMillis();
                auditLog.setDuration(duration);

                // Extract entity information from result or arguments
                extractEntityInformation(auditLog, joinPoint, result);

                // Save audit log
                saveAuditLog(auditLog);
            }
        }
    }

    /**
     * Create audit log entry
     */
    private AopAuditLog createAuditLog(ProceedingJoinPoint joinPoint, AuditAction action, LocalDateTime timestamp) {
        AuditContext context = AuditContext.getCurrentContext();
        
        AopAuditLog auditLog = new AopAuditLog();
        auditLog.setTimestamp(timestamp);
        auditLog.setAction(action);
        auditLog.setClassName(joinPoint.getTarget().getClass().getSimpleName());
        auditLog.setMethodName(joinPoint.getSignature().getName());
        
        // Set context information if available
        if (context != null) {
            auditLog.setRequestId(context.getRequestId());
            auditLog.setSessionId(context.getSessionId());
            auditLog.setCorrelationId(context.getCorrelationId());
            auditLog.setTransactionId(context.getTransactionId());
            auditLog.setUserId(context.getUserId());
            auditLog.setUsername(context.getUsername());
            auditLog.setUserType(context.getUserType());
            auditLog.setEmployeeId(context.getEmployeeId());
            auditLog.setIpAddress(context.getIpAddress());
            auditLog.setUserAgent(context.getUserAgent());
            auditLog.setHttpMethod(context.getHttpMethod());
            auditLog.setEndpoint(context.getEndpoint());
            auditLog.setModuleName(context.getModuleName());
            auditLog.setEnvironment(context.getEnvironment());
            auditLog.setTenantId(context.getTenantId());
            auditLog.setDeviceInfo(context.getDeviceInfo());
            auditLog.setBrowserInfo(context.getBrowserInfo());
            auditLog.setOsInfo(context.getOsInfo());
            auditLog.setLocation(context.getLocation());
            auditLog.setSourceSystem(context.getSourceSystem());
            auditLog.setAutomated(context.getAutomated());
            auditLog.setMetadata(context.getMetadata());
            auditLog.setRequestParameters(context.getRequestParameters());
        }

        // Set default values
        auditLog.setStatus(AuditStatus.IN_PROGRESS);
        auditLog.setArchived(false);
        auditLog.setContainsSensitiveData(false);
        auditLog.setRequiresApproval(false);

        return auditLog;
    }

    /**
     * Determine audit action from method name
     */
    private AuditAction determineActionFromMethod(ProceedingJoinPoint joinPoint) {
        String methodName = joinPoint.getSignature().getName().toLowerCase();
        
        if (methodName.startsWith("create") || methodName.startsWith("add") || methodName.startsWith("insert")) {
            return AuditAction.CREATE;
        } else if (methodName.startsWith("update") || methodName.startsWith("modify") || methodName.startsWith("edit")) {
            return AuditAction.UPDATE;
        } else if (methodName.startsWith("delete") || methodName.startsWith("remove")) {
            return AuditAction.DELETE;
        } else if (methodName.startsWith("get") || methodName.startsWith("find") || methodName.startsWith("search") || methodName.startsWith("list")) {
            return AuditAction.READ;
        } else if (methodName.startsWith("login")) {
            return AuditAction.LOGIN;
        } else if (methodName.startsWith("logout")) {
            return AuditAction.LOGOUT;
        } else if (methodName.startsWith("approve")) {
            return AuditAction.APPROVE;
        } else if (methodName.startsWith("reject")) {
            return AuditAction.REJECT;
        } else if (methodName.startsWith("publish")) {
            return AuditAction.PUBLISH;
        } else if (methodName.startsWith("archive")) {
            return AuditAction.ARCHIVE;
        } else if (methodName.startsWith("restore")) {
            return AuditAction.RESTORE;
        } else if (methodName.startsWith("assign")) {
            return AuditAction.ASSIGN;
        } else if (methodName.startsWith("activate")) {
            return AuditAction.ACTIVATE;
        } else if (methodName.startsWith("deactivate")) {
            return AuditAction.DEACTIVATE;
        } else if (methodName.startsWith("upload")) {
            return AuditAction.UPLOAD;
        } else if (methodName.startsWith("download")) {
            return AuditAction.DOWNLOAD;
        } else if (methodName.startsWith("share")) {
            return AuditAction.SHARE;
        } else if (methodName.startsWith("subscribe")) {
            return AuditAction.SUBSCRIBE;
        } else if (methodName.startsWith("unsubscribe")) {
            return AuditAction.UNSUBSCRIBE;
        } else if (methodName.startsWith("pay") || methodName.contains("payment")) {
            return AuditAction.PAYMENT;
        } else if (methodName.startsWith("refund")) {
            return AuditAction.REFUND;
        } else if (methodName.contains("notification")) {
            return AuditAction.NOTIFICATION_SENT;
        } else if (methodName.contains("email")) {
            return AuditAction.EMAIL_SENT;
        } else if (methodName.contains("sms")) {
            return AuditAction.SMS_SENT;
        } else {
            return AuditAction.CUSTOM;
        }
    }

    /**
     * Determine audit action from repository method name
     */
    private AuditAction determineActionFromRepositoryMethod(ProceedingJoinPoint joinPoint) {
        String methodName = joinPoint.getSignature().getName().toLowerCase();
        
        if (methodName.startsWith("save") || methodName.startsWith("insert")) {
            return AuditAction.CREATE;
        } else if (methodName.startsWith("delete") || methodName.startsWith("remove")) {
            return AuditAction.DELETE;
        } else {
            return AuditAction.UPDATE;
        }
    }

    /**
     * Extract entity information from method arguments or result
     */
    private void extractEntityInformation(AopAuditLog auditLog, ProceedingJoinPoint joinPoint, Object result) {
        try {
            // Try to extract from method arguments first
            Object[] args = joinPoint.getArgs();
            if (args != null && args.length > 0) {
                for (Object arg : args) {
                    if (arg != null && isEntity(arg)) {
                        setEntityInfo(auditLog, arg);
                        break;
                    }
                }
            }

            // If not found in arguments, try result
            if (auditLog.getEntityType() == null && result != null && isEntity(result)) {
                setEntityInfo(auditLog, result);
            }

        } catch (Exception e) {
            logger.debug("Failed to extract entity information", e);
        }
    }

    /**
     * Check if object is an entity
     */
    private boolean isEntity(Object obj) {
        return obj.getClass().isAnnotationPresent(Document.class) ||
               obj.getClass().getPackage().getName().contains(".beans");
    }

    /**
     * Set entity information in audit log
     */
    private void setEntityInfo(AopAuditLog auditLog, Object entity) {
        try {
            auditLog.setEntityType(AuditEntityType.fromClassName(entity.getClass().getName()));
            
            // Try to get ID field
            String entityId = extractEntityId(entity);
            if (entityId != null) {
                auditLog.setEntityId(entityId);
            }

            // Try to get name field
            String entityName = extractEntityName(entity);
            if (entityName != null) {
                auditLog.setEntityName(entityName);
            }

        } catch (Exception e) {
            logger.debug("Failed to set entity info", e);
        }
    }

    /**
     * Extract entity ID using reflection
     */
    private String extractEntityId(Object entity) {
        try {
            Field[] fields = entity.getClass().getDeclaredFields();
            for (Field field : fields) {
                if (field.getName().equals("id") || field.isAnnotationPresent(org.springframework.data.annotation.Id.class)) {
                    field.setAccessible(true);
                    Object value = field.get(entity);
                    return value != null ? value.toString() : null;
                }
            }
        } catch (Exception e) {
            logger.debug("Failed to extract entity ID", e);
        }
        return null;
    }

    /**
     * Extract entity name using reflection
     */
    private String extractEntityName(Object entity) {
        try {
            Field[] fields = entity.getClass().getDeclaredFields();
            for (Field field : fields) {
                String fieldName = field.getName().toLowerCase();
                if (fieldName.equals("name") || fieldName.equals("title") || fieldName.equals("username")) {
                    field.setAccessible(true);
                    Object value = field.get(entity);
                    return value != null ? value.toString() : null;
                }
            }
        } catch (Exception e) {
            logger.debug("Failed to extract entity name", e);
        }
        return null;
    }

    /**
     * Capture method arguments
     */
    private void captureMethodArguments(AopAuditLog auditLog, ProceedingJoinPoint joinPoint) {
        try {
            Object[] args = joinPoint.getArgs();
            if (args != null && args.length > 0) {
                Map<String, Object> parameters = new HashMap<>();
                Method method = ((org.aspectj.lang.reflect.MethodSignature) joinPoint.getSignature()).getMethod();
                String[] paramNames = extractParameterNames(method);

                for (int i = 0; i < args.length && i < paramNames.length; i++) {
                    if (args[i] != null) {
                        Object sanitizedValue = sanitizeValue(paramNames[i], args[i]);
                        if (sanitizedValue != null) {
                            parameters.put(paramNames[i], sanitizedValue);
                        }
                    }
                }

                if (!parameters.isEmpty()) {
                    auditLog.setRequestParameters(parameters);
                }
            }
        } catch (Exception e) {
            logger.debug("Failed to capture method arguments", e);
        }
    }

    /**
     * Capture return value
     */
    private void captureReturnValue(AopAuditLog auditLog, Object returnValue) {
        try {
            Object sanitizedValue = sanitizeValue("returnValue", returnValue);
            if (sanitizedValue != null) {
                Map<String, Object> responseData = new HashMap<>();
                responseData.put("returnValue", sanitizedValue);
                auditLog.setResponseData(responseData);
            }
        } catch (Exception e) {
            logger.debug("Failed to capture return value", e);
        }
    }

    /**
     * Sanitize value to remove sensitive information
     */
    private Object sanitizeValue(String fieldName, Object value) {
        if (value == null) {
            return null;
        }

        // Check if field name indicates sensitive data
        String lowerFieldName = fieldName.toLowerCase();
        for (String sensitiveField : SENSITIVE_FIELDS) {
            if (lowerFieldName.contains(sensitiveField)) {
                return "[REDACTED]";
            }
        }

        try {
            // Convert to JSON string and check size
            String jsonString = objectMapper.writeValueAsString(value);
            if (jsonString.length() > maxDataSize) {
                return "[DATA_TOO_LARGE]";
            }

            // For complex objects, create a simplified representation
            if (isEntity(value)) {
                Map<String, Object> simplified = new HashMap<>();
                simplified.put("class", value.getClass().getSimpleName());
                simplified.put("id", extractEntityId(value));
                simplified.put("name", extractEntityName(value));
                return simplified;
            }

            return value;

        } catch (Exception e) {
            logger.debug("Failed to sanitize value", e);
            return "[SERIALIZATION_ERROR]";
        }
    }

    /**
     * Extract parameter names from method
     */
    private String[] extractParameterNames(Method method) {
        // Simple implementation - in production, consider using Spring's ParameterNameDiscoverer
        String[] names = new String[method.getParameterCount()];
        for (int i = 0; i < names.length; i++) {
            names[i] = "param" + i;
        }
        return names;
    }

    /**
     * Populate audit context from HTTP request
     */
    private void populateContextFromRequest(AuditContext context) {
        try {
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (attributes != null) {
                HttpServletRequest request = attributes.getRequest();

                context.setIpAddress(getClientIpAddress(request));
                context.setUserAgent(request.getHeader("User-Agent"));
                context.setHttpMethod(request.getMethod());
                context.setEndpoint(request.getRequestURI());
                context.setSessionId(request.getSession(false) != null ? request.getSession().getId() : null);

                // Extract additional headers
                String deviceInfo = request.getHeader("X-Device-Info");
                if (deviceInfo != null) {
                    context.setDeviceInfo(deviceInfo);
                }

                String browserInfo = extractBrowserInfo(request.getHeader("User-Agent"));
                context.setBrowserInfo(browserInfo);

                String osInfo = extractOSInfo(request.getHeader("User-Agent"));
                context.setOsInfo(osInfo);
            }
        } catch (Exception e) {
            logger.debug("Failed to populate context from request", e);
        }
    }

    /**
     * Get client IP address from request
     */
    private String getClientIpAddress(HttpServletRequest request) {
        String[] headerNames = {
            "X-Forwarded-For", "X-Real-IP", "Proxy-Client-IP",
            "WL-Proxy-Client-IP", "HTTP_X_FORWARDED_FOR", "HTTP_X_FORWARDED",
            "HTTP_X_CLUSTER_CLIENT_IP", "HTTP_CLIENT_IP", "HTTP_FORWARDED_FOR",
            "HTTP_FORWARDED", "HTTP_VIA", "REMOTE_ADDR"
        };

        for (String header : headerNames) {
            String ip = request.getHeader(header);
            if (ip != null && !ip.isEmpty() && !"unknown".equalsIgnoreCase(ip)) {
                // Handle multiple IPs in X-Forwarded-For
                if (ip.contains(",")) {
                    ip = ip.split(",")[0].trim();
                }
                return ip;
            }
        }

        return request.getRemoteAddr();
    }

    /**
     * Extract browser information from User-Agent
     */
    private String extractBrowserInfo(String userAgent) {
        if (userAgent == null) return null;

        if (userAgent.contains("Chrome")) return "Chrome";
        if (userAgent.contains("Firefox")) return "Firefox";
        if (userAgent.contains("Safari")) return "Safari";
        if (userAgent.contains("Edge")) return "Edge";
        if (userAgent.contains("Opera")) return "Opera";

        return "Unknown";
    }

    /**
     * Extract OS information from User-Agent
     */
    private String extractOSInfo(String userAgent) {
        if (userAgent == null) return null;

        if (userAgent.contains("Windows")) return "Windows";
        if (userAgent.contains("Mac OS")) return "macOS";
        if (userAgent.contains("Linux")) return "Linux";
        if (userAgent.contains("Android")) return "Android";
        if (userAgent.contains("iOS")) return "iOS";

        return "Unknown";
    }

    /**
     * Extract module name from join point
     */
    private String extractModuleName(JoinPoint joinPoint) {
        String className = joinPoint.getTarget().getClass().getName();
        if (className.contains(".admin.")) return "Admin";
        if (className.contains(".client.")) return "Client";
        if (className.contains(".core.")) return "Core";
        return "Unknown";
    }

    /**
     * Extract endpoint from join point
     */
    private String extractEndpoint(JoinPoint joinPoint) {
        try {
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (attributes != null) {
                return attributes.getRequest().getRequestURI();
            }
        } catch (Exception e) {
            logger.debug("Failed to extract endpoint", e);
        }
        return joinPoint.getSignature().getName();
    }

    /**
     * Check if join point should be excluded from auditing
     */
    private boolean isExcluded(ProceedingJoinPoint joinPoint) {
        String className = joinPoint.getTarget().getClass().getName();
        return EXCLUDED_PACKAGES.stream().anyMatch(className::startsWith);
    }

    /**
     * Save audit log asynchronously or synchronously
     */
    private void saveAuditLog(AopAuditLog auditLog) {
        try {
            if (asyncAudit) {
                CompletableFuture.runAsync(() -> {
                    try {
                        auditService.saveAuditLog(auditLog);
                    } catch (Exception e) {
                        logger.error("Failed to save audit log asynchronously", e);
                    }
                });
            } else {
                auditService.saveAuditLog(auditLog);
            }
        } catch (Exception e) {
            logger.error("Failed to save audit log", e);
        }
    }
}
