package com.hb.crm.core.Audit;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.hb.crm.core.Enums.UserRole;
import com.hb.crm.core.Enums.UserType;
import com.hb.crm.core.beans.AopAudit.AuditContext;
import com.hb.crm.core.beans.AuditLog.AuditLog;
import com.hb.crm.core.beans.AuditLog.SkipAudit;
import com.hb.crm.core.beans.Employee;
import com.hb.crm.core.beans.User;
import com.hb.crm.core.repositories.EmployeeRepository;
import com.hb.crm.core.repositories.UserRepository;
import com.hb.crm.core.searchBeans.BaseEntity;
import com.hb.crm.core.util.SeedingUtils;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.mapping.event.AbstractMongoEventListener;
import org.springframework.data.mongodb.core.mapping.event.AfterSaveEvent;
import org.springframework.data.mongodb.core.mapping.event.BeforeDeleteEvent;
import org.springframework.data.mongodb.core.mapping.event.BeforeSaveEvent;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.lang.reflect.Field;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

@Component
public class AuditableEntityListener extends AbstractMongoEventListener<Object> {

    private static final ThreadLocal<Map<Object, AuditLog>> context = ThreadLocal.withInitial(HashMap::new);

    @Autowired
    private MongoTemplate mongoTemplate;

    @Autowired
    @Qualifier("mongoTemplate2")
    private MongoTemplate mongoTemplate2;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private EmployeeRepository employeeRepository;


    @Override
    public void onBeforeSave(BeforeSaveEvent<Object> event) {
        Object target = event.getSource();
        if (SeedingUtils.isSeeding() || target instanceof SkipAudit) {
            return;
        }

        // For new entities (CREATE)
        if (getEntityId(target) == null) {
            var log = logAction(target, "CREATE", null, target, false);
            if (log != null)
                context.get().put(target, log);

        } else {
            // For existing entities (UPDATE)
            try {
                Object oldEntity = fetchOldState(target);
                if (oldEntity != null) {
                    Map<String, Object> oldValuesMap = new HashMap<>();
                    Map<String, Object> newValuesMap = new HashMap<>();
                    createChangedFieldsObjects(oldEntity, target, oldValuesMap, newValuesMap);
                    if (!oldValuesMap.isEmpty() || !newValuesMap.isEmpty()) {
                        logAction(target, "UPDATE", oldValuesMap, newValuesMap, true);
                    }
                }
            } catch (Exception e) {
                // Fallback: log as UPDATE without old values
                logAction(target, "UPDATE", null, target, true);
            }
        }
    }

    @Override
    public void onAfterSave(AfterSaveEvent<Object> event) {
        super.onAfterSave(event);
        Object target = event.getSource();
        AuditLog log = context.get().get(target);
        if (log != null) {
            // now the ID is generated
            log.setEntityId((String) getEntityId(target));
            AuditLogContext.getAuditLogRepository().save(log);
            context.get().remove(target); // cleanup
        }
    }

    @Override
    public void onBeforeDelete(BeforeDeleteEvent<Object> event) {
        Object target = event.getSource();
        if (target instanceof SkipAudit) {
            return;
        }
        logAction(target, "DELETE", target, null, true);
    }

    private AuditLog logAction(Object target, String actionType, Object oldValue, Object newValue, boolean create) {
        try {
            UserInfo userInfo = getCurrentUserInfo();
            String ipAddress = getClientIpAddress();

            ObjectMapper objectMapper = new ObjectMapper()
                    .disable(SerializationFeature.FAIL_ON_SELF_REFERENCES)
                    .disable(SerializationFeature.FAIL_ON_EMPTY_BEANS);

            // Convert to Map instead of JsonNode
            Map<String, Object> oldMap = oldValue != null
                    ? objectMapper.convertValue(oldValue, new TypeReference<>() {
            }) : null;

            Map<String, Object> newMap = newValue != null
                    ? objectMapper.convertValue(newValue, new TypeReference<>() {
            }) : null;

            AuditLog auditLog = new AuditLog();
            auditLog.setEntityName(target.getClass().getSimpleName());

            Object entityId = getEntityId(target);
            auditLog.setEntityId(entityId != null ? entityId.toString() : "unknown");

            auditLog.setActionType(actionType);
            auditLog.setOldValue(oldMap);
            auditLog.setNewValue(newMap);
            auditLog.setIpAddress(ipAddress);
            auditLog.setCreatedAt(LocalDateTime.now());

            if (userInfo != null) {
                auditLog.setUserEmail(userInfo.email());
                auditLog.setUserName(userInfo.fullName());
                auditLog.setUserId(userInfo.id());
                auditLog.setUserType(userInfo.type());
                auditLog.setUserRole(userInfo.role());
            } else {
                auditLog.setUserEmail("system");
                auditLog.setUserName("System User");
                auditLog.setUserId("system");
            }

            // Save the audit log
            if (create)
                AuditLogContext.getAuditLogRepository().save(auditLog);
            else
                return auditLog;
        } catch (Exception e) {
            // Handle exception silently to avoid breaking the main operation
            System.err.println("Failed to log audit action: " + e.getMessage());
        }

        return null;
    }

    private UserInfo getCurrentUserInfo() {
        try {
            // Try to get user from AuditContext first
            AuditContext auditContext = AuditContext.getCurrentContext();
            if (auditContext != null && auditContext.hasUserInfo()) {
                String userId = auditContext.getUserId();
                String employeeId = auditContext.getEmployeeId();

                if (userId != null) {
                    Optional<User> user = userRepository.findById(userId);
                    if (user.isPresent()) {
                        User u = user.get();
                        return new UserInfo(u.getId(), u.getUsername(), getFullName(u),
                                u.getUserInfo() != null ? u.getUserInfo().getEmail() : u.getUsername(),
                                u.getUsertype(), UserRole.USER);
                    }
                }

                if (employeeId != null) {
                    Optional<Employee> employee = employeeRepository.findById(employeeId);
                    if (employee.isPresent()) {
                        Employee e = employee.get();
                        return new UserInfo(e.getId(), e.getUsername(), e.getFullName(), e.getEmail(), null,
                                UserRole.ADMIN);
                    }
                }
            }

            // Fallback to SecurityContext
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            if (authentication != null && authentication.isAuthenticated() &&
                    !authentication.getName().equals("anonymousUser")) {

                String username = authentication.getName();

                // Try to find user by username
                Optional<User> userOpt = userRepository.findByUsername(username);
                if (userOpt.isPresent()) {
                    var user = userOpt.get();
                    return new UserInfo(user.getId(), user.getUsername(), getFullName(user),
                            user.getUserInfo() != null ? user.getUserInfo().getEmail() : user.getUsername(),
                            user.getUsertype(), UserRole.USER);
                }

                // Try to find employee by username
                Employee employee = employeeRepository.findByUsername(username);
                if (employee != null) {
                    return new UserInfo(employee.getId(), employee.getUsername(),
                            employee.getFullName(), employee.getEmail(), null, UserRole.ADMIN);
                }

                // Return minimal info if user/employee not found but authenticated
                return new UserInfo("unknown", username, username, username, null, null);
            }

            return null;
        } catch (Exception e) {
            System.err.println("Error getting current user info: " + e.getMessage());
            return null;
        }
    }

    private String getFullName(User user) {
        if (user.getFirstName() != null && user.getLastName() != null) {
            return user.getFirstName() + " " + user.getLastName();
        }
        return user.getUsername();
    }

    private Object getEntityId(Object entity) {
        try {
            // Look for a field annotated with @Id
            for (Field field : entity.getClass().getDeclaredFields()) {
                if (field.isAnnotationPresent(Id.class)) {
                    field.setAccessible(true);
                    return field.get(entity);
                }
            }

            // If no @Id annotation is found, fall back to a field named "id"
            try {
                Field idField = entity.getClass().getDeclaredField("id");
                idField.setAccessible(true);
                return idField.get(entity);
            } catch (NoSuchFieldException e) {
                // Field "id" doesn't exist
                return null;
            }
        } catch (Exception e) {
            System.err.println("Failed to extract entity ID: " + e.getMessage());
            return null;
        }
    }

    private String getClientIpAddress() {
        try {
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
            HttpServletRequest request = attributes.getRequest();

            String ipAddress = request.getHeader("X-Forwarded-For");
            if (ipAddress == null || ipAddress.isEmpty() || "unknown".equalsIgnoreCase(ipAddress)) {
                ipAddress = request.getHeader("Proxy-Client-IP");
            }
            if (ipAddress == null || ipAddress.isEmpty() || "unknown".equalsIgnoreCase(ipAddress)) {
                ipAddress = request.getHeader("WL-Proxy-Client-IP");
            }
            if (ipAddress == null || ipAddress.isEmpty() || "unknown".equalsIgnoreCase(ipAddress)) {
                ipAddress = request.getRemoteAddr();
            }

            // Handle multiple IPs in X-Forwarded-For
            if (ipAddress != null && ipAddress.contains(",")) {
                ipAddress = ipAddress.split(",")[0].trim();
            }

            return ipAddress;
        } catch (Exception e) {
            return "unknown";
        }
    }

    private Object fetchOldState(Object entity) {
        try {
            Object entityId = getEntityId(entity);
            if (entityId == null) {
                return null; // CREATE case
            }

            // Get entity class dynamically
            Class<?> entityClass = entity.getClass();

            // Choose template based on whether the class extends BaseEntity
            MongoTemplate templateToUse = BaseEntity.class.isAssignableFrom(entityClass)
                    ? mongoTemplate2
                    : mongoTemplate;

            // Fetch old entity from MongoDB
            return templateToUse.findById(entityId, entityClass);
        } catch (Exception e) {
            System.err.println("Failed to fetch old state: " + e.getMessage());
            return null;
        }
    }

    private void createChangedFieldsObjects(Object oldEntity, Object newEntity,
                                            Map<String, Object> oldValuesMap,
                                            Map<String, Object> newValuesMap) {
        try {
            Class<?> clazz = newEntity.getClass();
            Field[] fields = clazz.getDeclaredFields();

            for (Field field : fields) {
                field.setAccessible(true);
                Object oldValue = field.get(oldEntity);
                Object newValue = field.get(newEntity);

                if (!Objects.equals(oldValue, newValue)) {
                    // Convert problematic values to strings to avoid serialization issues
                    Object safeOldValue = makeSafeForSerialization(oldValue);
                    Object safeNewValue = makeSafeForSerialization(newValue);

                    oldValuesMap.put(field.getName(), safeOldValue);
                    newValuesMap.put(field.getName(), safeNewValue);
                }
            }
        } catch (Exception e) {
            System.err.println("Error creating changed fields objects: " + e.getMessage());
        }
    }

    private Object makeSafeForSerialization(Object value) {
        if (value == null) {
            return null;
        }

        try {
            // Test if the object can be serialized
            new ObjectMapper().writeValueAsString(value);
            return value;
        } catch (Exception e) {
            // If serialization fails, convert to string
            return value.toString();
        }
    }

    // Helper class to hold user information
    private record UserInfo(String id, String username, String fullName, String email, UserType type, UserRole role) {

    }
} 