package com.hb.crm.core.services.interfaces;


import com.hb.crm.core.Enums.*;
import com.hb.crm.core.beans.Notification.Notification;
import com.hb.crm.core.beans.User;
import com.hb.crm.core.dtos.PageDto;
import com.hb.crm.core.dtos.clientNotification.GroupedNotificationsDto;
import com.hb.crm.core.dtos.clientNotification.NotificationDto;
import com.hb.crm.core.dtos.notification.NotificationFilter;
import org.springframework.data.domain.Page;

import java.util.List;
import java.util.Map;

public interface NotificationService {

    boolean sendWhatsAppMessage(String to, String message);

    void sendAndStoreNotification(String entityId, NotificationType type,
                                  User user, List<Object> entities,
                                  String image, String icon,
                                  NotificationEntityType entityType,
                                  User navigatedUser,
                                  String slug,
                                  String username,
                                  UserType navigatedUserType);

    boolean retrySingleNotification(String notificationId);

    /**
     * @param token
     * @param title
     * @param body
     * @param payload
     * @apiNote Send notification to a single user by fcm token with data payload
     * @return: boolean that determine if the message sent successfully or not
     */
    boolean sendNotification(String token, String title, String body, Map<String, String> payload);

    boolean sendNotification(String token, NotificationType type, String userId);

    /**
     * @param topic
     * @param title
     * @param body
     * @apiNote Send broadcast notification to topic
     */
    public void sendBroadcastNotification(String topic, String title, String body);


    /**
     * @param topic
     * @param title
     * @param body
     * @param payload
     * @apiNote Send broadcast notification to a topic with payload data
     */
    public void sendBroadcastNotification(
            String topic,
            String title,
            String body,
            Map<String, String> payload
    );

    com.hb.crm.core.beans.Notification.Notification storeNotification(String subject, String body, NotificationType type, NotificationChannelType channelType,
                                                                      User user, boolean sent, String entityId, String image, Map<String, String> payload,
                                                                      String icon,
                                                                      NotificationEntityType entityType,
                                                                      User navigatedUser,
                                                                      String slug,
                                                                      String username,
                                                                      IconType iconType,
                                                                      UserType navigatedUserType);

    com.hb.crm.core.dtos.PageDto<NotificationDto> getNotifications(String userId, int page, int size);

    com.hb.crm.core.dtos.PageDto<NotificationDto> getUnreadNotifications(String userId, int page, int size);

    Long getUnreadNotificationsCount(String userId);

    /**
     * Updates the read status of a notification
     *
     * @param id   the notification ID
     * @param read true to mark as read, false to mark as unread
     */
    void updateNotificationReadStatus(String id, boolean read);

    Page<com.hb.crm.core.beans.Notification.Notification> search();

    PageDto<Notification> search(Map<String, Object> obj, int page, int i);

    com.hb.crm.core.beans.Notification.Notification getNotificationById(String id);

    void delete(String id);

    void update(com.hb.crm.core.beans.Notification.Notification obj);

    boolean sendSmsNotification(String phoneNumber, String message);

    void sendEmail(String email, String subject, String body);

    GroupedNotificationsDto getGroupedNotifications(String userId, int page, int size);

    GroupedNotificationsDto getGroupedUnreadNotifications(String userId, int page, int size);
    PageDto<NotificationDto> getAllNotificationsWithFilter(int page, int size, NotificationFilter filter);


    PageDto<com.hb.crm.core.beans.Notification.Notification> getAllNotificationsWithFilter(int page, int size, Boolean sent);

    void hideNotification(String notificationId, String userId);

    void hideNotifications(List<String> notificationIds, String userId);
}
