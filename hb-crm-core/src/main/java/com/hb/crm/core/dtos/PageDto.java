package com.hb.crm.core.dtos;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;


@Data
@NoArgsConstructor
@AllArgsConstructor
public class PageDto<T> {

    private int itemsPerPage =10;
    private long totalNoOfItems;
    private int pageNumber;

    @JsonIgnore
    private String sortColumn;

    @JsonIgnore
    private String sortOrder;
    List<T> items = new ArrayList<>();

	public PageDto(int noOfItems, long totalNoOfItems, int pageNumber, List<T> items) {
		super();
		this.itemsPerPage = noOfItems;
		this.totalNoOfItems = totalNoOfItems;
		this.pageNumber = pageNumber;
		this.items = items;
	}

	public PageDto(int itemsPerPage, int pageNumber) {
		super();
		this.itemsPerPage = itemsPerPage;
		this.pageNumber = pageNumber;
	}
	public PageDto(int itemsPerPage, int pageNumber, String sortColumn) {
		super();
		this.itemsPerPage = itemsPerPage;
		this.pageNumber = pageNumber;
		this.sortColumn = sortColumn;
	}
	public PageDto(int itemsPerPage, int pageNumber, String sortColumn, String sortOrder) {
		super();
		this.itemsPerPage = itemsPerPage;
		this.pageNumber = pageNumber;
		this.sortColumn = sortColumn;
		this.sortOrder = sortOrder;
	}

	public int getItemsPerPage() {
		return itemsPerPage;
	}
	public void setItemsPerPage(int itemsPerPage) {
		this.itemsPerPage = itemsPerPage;
	}
	public long getTotalNoOfItems() {
		return totalNoOfItems;
	}
	public void setTotalNoOfItems(long totalNoOfItems) {
		this.totalNoOfItems = totalNoOfItems;
	}
	public int getPageNumber() {
		return pageNumber;
	}
	public void setPageNumber(int pageNumber) {
		this.pageNumber = pageNumber;
	}
	public String getSortColumn() {
		return sortColumn;
	}

	public void setSortColumn(String sortColumn) {
		this.sortColumn = sortColumn;
	}
	public String getSortOrder() {
		return sortOrder;
	}

	public void setSortOrder(String sortOrder) {
		this.sortOrder = sortOrder;
	}

	public List<T> getItems() {
		return items;
	}
	public void setItems(List<T> items) {
		this.items = items;
	}

}