package com.hb.crm.core.repositories;

import com.hb.crm.core.Enums.PackageType;
import com.hb.crm.core.beans.SubPackage;
import org.bson.types.ObjectId;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.Aggregation;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;

import java.util.List;
import java.util.Optional;

public interface SubPackageRepository extends MongoRepository<SubPackage, String> {
    @Query("{ '_package._id': ?0, 'packageType': ?1 }")
    List<SubPackage> existsByPackageAndPackageType(String packageId, PackageType packageType);
    Optional<SubPackage> findBySlug(String slug);
    List<SubPackage> findBy_package_Id(String packageId);
    boolean existsBySlugIgnoreCase(String slug);
    boolean existsBySlugIgnoreCaseAndIdNot(String slug, String id);

    /**
     * Performs fuzzy search within a specific list of package IDs.
     * This method uses MongoDB Atlas Search with fuzzy matching capabilities
     * to handle typos and similar-sounding words (e.g., "pakis" → "paris", "jahan" → "Japan").
     *
     * @param searchTerm The search query to match against package fields
     * @param packageIds List of package IDs to restrict the search to
     * @return A list of SubPackage objects matching the search criteria within the provided IDs
     */
    @Aggregation(pipeline = {
        // Atlas Search stage with fuzzy matching
        "{ " +
            "$search: { " +
                "'index': 'default', " +
                "'compound': { " +
                    "'should': [ " +
                        "{ 'text': { " +
                            "'query': ?0, " +
                            "'path': 'name', " +
                            "'fuzzy': { " +
                                "'maxEdits': 2, " +
                                "'prefixLength': 0, " +
                                "'maxExpansions': 50 " +
                            "} " +
                        "} }, " +
                        "{ 'text': { " +
                            "'query': ?0, " +
                            "'path': 'slug', " +
                            "'fuzzy': { " +
                                "'maxEdits': 2, " +
                                "'prefixLength': 0, " +
                                "'maxExpansions': 50 " +
                            "} " +
                        "} } " +
                    "], " +
                    "'minimumShouldMatch': 1 " +
                "} " +
            "} " +
        "} ",
        // Filter by package IDs
        "{ $match: { '_id': { $in: ?1 } } }",
        // Add search score for relevance sorting
        "{ $addFields: { score: { $meta: 'searchScore' } } }",
        // Sort by relevance score
        "{ $sort: { score: -1 } }"
    })
    List<SubPackage> findBySearchTermAndPackageIds(String searchTerm, List<String> packageIds);

    /**
     * Find packages by list of IDs with pagination support
     */
    Page<SubPackage> findByIdIn(List<String> ids, Pageable pageable);

    /**
     * Find packages by list of IDs and type with pagination support
     */
    Page<SubPackage> findByPackageTypeAndIdIn(PackageType packageType, List<String> ids, Pageable pageable);

    /**
     * Find packages by list of IDs without pagination
     */
    List<SubPackage> findByIdIn(List<String> ids);

    /**
     * Find packages by type and list of IDs without pagination
     */
    List<SubPackage> findByPackageTypeAndIdIn(PackageType packageType, List<String> ids);

    @Query("{ '_package.$id': { $in: ?0 }, 'packageType': ?1 }")
    List<SubPackage> findByPackageIdsAndType(List<ObjectId> packageIds, PackageType packageType);
}
