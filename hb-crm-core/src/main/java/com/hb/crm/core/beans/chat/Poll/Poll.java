package com.hb.crm.core.beans.chat.Poll;

import lombok.Getter;
import lombok.Setter;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@Document
public class Poll {

    @Id
    String id;
    String question;
    boolean multiselect;
    boolean closed;
    LocalDateTime expiryDateTime;
    List<PollOption> options = new ArrayList<>();

    public void calculatePercentages() {
        if (options == null || options.isEmpty()) {
            return;
        }

        int totalVotes = options.stream()
                .mapToInt(PollOption::getVoteCount)
                .sum();

        if (totalVotes == 0) {
            options.forEach(option -> option.setPercentage(0.0));
            return;
        }

        options.forEach(option -> {
            double percentage = (option.getVoteCount() * 100.0) / totalVotes;
            option.setPercentage(Math.round(percentage * 100.0) / 100.0); // Round to 2 decimal places
        });
    }
}
