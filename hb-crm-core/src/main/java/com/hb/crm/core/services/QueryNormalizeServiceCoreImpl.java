package com.hb.crm.core.services;

import com.hb.crm.core.services.interfaces.QueryNormalizeService;
import org.springframework.data.mongodb.core.aggregation.*;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.*;


@Service
public class QueryNormalizeServiceCoreImpl implements QueryNormalizeService {

    @Override
    public Aggregation getUsersList(Criteria criteria) {
        List<AggregationOperation> operations = new ArrayList<>();


        // Project only the required fields
        operations.add(project("_id",
                "firstName", "lastName",
                "email", "mobile", "gender",
                "city", "country", "usertype", "username",
                "follwerscount", "followingcount",
                "coverImage", "profileImage"));

        // Apply sort from the pageable
        if (criteria != null) {
            operations.add(match(criteria));
        }
        return Aggregation.newAggregation(operations);
    }
}