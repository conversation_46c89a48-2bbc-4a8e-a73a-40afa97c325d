package com.hb.crm.core.searchBeans;

import com.hb.crm.core.Enums.ImageCategory;
import com.hb.crm.core.Enums.MediaType;
import com.hb.crm.core.beans.Tag;
import lombok.Data;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.LastModifiedDate;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
@Data
public class SearchMedia {
    @Id
    private String id;
    private String title;

    @CreatedDate
    private Date creationDate;
    private List<Tag> tags;
    @LastModifiedDate
    private Date lastUpdate;
    private String source;
    private String description;
    private String videoUrl;
    private String trimmedVideoUrl;
    private ImageCategory imageCategory;
    private BigDecimal videoDuration;
    private BigDecimal videoDurationMS;
    private String thumbnailClipUrl;
    private String thumbnailCaptureUrl;
    private MediaType mediaType;
    private String OwnerId;
    private Double videoSize;
    private String LastUpdaterId;
    private Boolean employee;
    private int numberOfReactions;
    private int numberOfComments;
    private String userId;
    private simpleUserInfo userInfo;
    private simplePackageInfo _package;
    private simplePostInfo post;
    private List<simpleUserInfo> taggedUsers;
    private Boolean showPackage=true;
    private Boolean showPost=true;
    public simplePostInfo getPost() {
        if(showPost){
            return post;
        }else {
            return null;
        }
    }

    public simplePackageInfo get_package() {
        if(showPackage){
            return _package;
        }else
            return null;
    }
}
