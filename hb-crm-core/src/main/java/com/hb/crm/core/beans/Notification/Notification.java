package com.hb.crm.core.beans.Notification;

import com.hb.crm.core.Enums.*;
import com.hb.crm.core.beans.AuditLog.SkipAudit;
import com.hb.crm.core.beans.User;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.DBRef;
import org.springframework.data.mongodb.core.mapping.Document;

import java.time.LocalDateTime;
import java.util.Map;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Document
public class Notification implements SkipAudit {

    @Id
    private String id;

    @DBRef(lazy = true)
    private User user;

    private String subject;
    private String body;
    private Map<String, String> payload;
    private String icon;
    private String image;
    private IconType iconType;
    private NotificationType type;
    private NotificationChannelType channelType;
    private LocalDateTime lastTriedAt;
    private LocalDateTime createdAt = LocalDateTime.now();
    private LocalDateTime readAt = LocalDateTime.now();
    private boolean read = false;
    private boolean sent = false;
    private long retryCount = 0;
    private String entityId;
    private String entitySlug;
    private String username;
    private NotificationEntityType entityType;
    private boolean hidden = false;
    private User navigatedUser;

    private UserType NavigatedUsertype = UserType.Influencer;
}
