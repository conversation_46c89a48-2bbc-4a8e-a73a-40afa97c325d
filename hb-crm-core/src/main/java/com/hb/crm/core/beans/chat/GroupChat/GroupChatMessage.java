package com.hb.crm.core.beans.chat.GroupChat;

import com.hb.crm.core.Enums.ConversationMessageStatus;
import com.hb.crm.core.Enums.ConversationMessageType;
import com.hb.crm.core.Enums.ReactionType;
import com.hb.crm.core.beans.User;
import com.hb.crm.core.beans.chat.Poll.Poll;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.DBRef;
import org.springframework.data.mongodb.core.mapping.Document;

import java.time.LocalDateTime;
import java.util.List;

@Getter
@Setter
@Document
@NoArgsConstructor
public class GroupChatMessage {

    @Id
    String id;
    String url;
    String text;
    String replyId;
    LocalDateTime dateTime;
    List<ReactionType> reaction;
    boolean deletedByMe = false;
    ConversationMessageType type;
    ConversationMessageStatus status;

    boolean pinned;
    LocalDateTime pinExpiryDate;

    Poll poll;

    @DBRef(lazy = true)
    GroupConversation conversation;

    @DBRef(lazy = true)
    User user;

    List<UserGroupChatMessage> userGroupChatMessages;
}
