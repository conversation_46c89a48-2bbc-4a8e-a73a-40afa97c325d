package com.hb.crm.core.services.interfaces;

import com.hb.crm.core.Enums.SearchEnum;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.query.Criteria;

public interface QueryNormalizeService {
    Aggregation getUsersList(Criteria criteria);

}
