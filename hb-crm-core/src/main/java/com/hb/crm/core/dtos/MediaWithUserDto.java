package com.hb.crm.core.dtos;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.hb.crm.core.Enums.ImageCategory;
import com.hb.crm.core.Enums.MediaType;
import com.hb.crm.core.beans.StoryOverlay;
import com.hb.crm.core.searchBeans.simplePackageInfo;
import com.hb.crm.core.searchBeans.simplePostInfo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class MediaWithUserDto {
    private String id;
    private String title;
    private Date creationDate;
    private Date lastUpdate;
    private String source;
    private String description;
    private String videoUrl;
    private List<TagDto> tags;
    private ImageCategory imageCategory;
    private BigDecimal videoDuration;
    private BigDecimal videoDurationMS;
    private String thumbnailClipUrl;
    private String thumbnailCaptureUrl;
    private MediaType mediaType;
    private String OwnerId;
    private Double videoSize;
    private String LastUpdaterId;
    private Boolean employee;
    private Boolean privateMedia;
    private int numberOfReactions;
    private int numberOfComments;
    private String userId;
    private SimpleUserMediaInfoDto user;  // Using the existing SimpleUserMediaInfoDto
    @JsonProperty("_package")
    private simplePackageInfo _package;
    private simplePostInfo postOrStory;
    private float latitude;
    private float longtuid;
    private String audioUrl;
    private List<SimpleUserinfoDto> taggedUsers;
    private List<StoryOverlay> overlays;
}