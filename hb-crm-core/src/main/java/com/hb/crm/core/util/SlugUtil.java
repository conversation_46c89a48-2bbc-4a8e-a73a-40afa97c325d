package com.hb.crm.core.util;

import com.hb.crm.core.beans.Package;
import com.hb.crm.core.beans.Post;

import java.text.Normalizer;
import java.time.format.DateTimeFormatter;
import java.util.Locale;
import java.util.regex.Pattern;

public class SlugUtil {

    private static final Pattern NON_LATIN = Pattern.compile("[^\\w-]");
    private static final Pattern WHITE_SPACE = Pattern.compile("\\s");
    private static final Pattern SPECIAL_CHAR = Pattern.compile("(^-|-$)");


    /**
     * Generates unique slug for package by package title and available date
     * @param pkg
     * @return String
     * @see com.hb.crm.core.beans.Package
     * @see com.hb.crm.core.beans.SubPackage
     */
    public String generatePackageSlug(Package pkg) {
        StringBuilder builder = new StringBuilder();
        String slug = toSlug(pkg.getName());
        builder.append(slug);
        builder.append("-");
        String date = pkg.getAvailableFrom().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        builder.append(date);
        return builder.toString();
    }


    /**
     * Generates unique slug for post from the first 50 chars,
     *
     * @param  post
     * @return String
     *
     * @see Post
     */
    public String generatePostSlug(Post post) {
        String slug = toSlug(post.getText().substring(0, 50));
        return toSlug(slug);
    }


    /**
     * Slugify string, remove white space, remove non-latin characters, lowercase.
     * @param input
     * @return String
     */
    private String toSlug(String input) {
        String nowhitespace = WHITE_SPACE.matcher(input).replaceAll("-");
        String normalized = Normalizer.normalize(nowhitespace, Normalizer.Form.NFD);
        String slug = NON_LATIN.matcher(normalized).replaceAll("");
        slug = SPECIAL_CHAR.matcher(slug).replaceAll("");
        return slug.toLowerCase(Locale.ENGLISH);
    }
}
