package com.hb.crm.core.repositories;

import com.hb.crm.core.Enums.UserType;
import com.hb.crm.core.beans.Notification.EmployeeNotificationMuteUser;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface EmployeeNotificationMuteUserRepository extends MongoRepository<EmployeeNotificationMuteUser, String> {

    Optional<EmployeeNotificationMuteUser> findByEmployeeIdAndMutedUser_Id(String employeeId, String mutedUserId);

    Page<EmployeeNotificationMuteUser> findByEmployeeId(String employeeId, Pageable pageable);

    List<EmployeeNotificationMuteUser> findByEmployeeId(String employeeId);

    Page<EmployeeNotificationMuteUser> findByEmployeeIdAndMutedUser_Usertype(String employeeId, UserType type, Pageable pageable);
}
