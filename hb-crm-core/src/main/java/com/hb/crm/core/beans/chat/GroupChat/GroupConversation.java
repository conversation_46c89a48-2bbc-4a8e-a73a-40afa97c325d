package com.hb.crm.core.beans.chat.GroupChat;

import com.hb.crm.core.beans.SubPackage;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.DBRef;
import org.springframework.data.mongodb.core.mapping.Document;


@Data
@Document
@NoArgsConstructor
@AllArgsConstructor
public class GroupConversation {
    @Id
    String id;
    String topic;

    @DBRef(lazy = true)
    SubPackage Package;
}
