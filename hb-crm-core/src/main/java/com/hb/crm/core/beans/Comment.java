package com.hb.crm.core.beans;

import lombok.Getter;
import lombok.Setter;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.CompoundIndex;
import org.springframework.data.mongodb.core.index.CompoundIndexes;
import org.springframework.data.mongodb.core.mapping.DBRef;
import org.springframework.data.mongodb.core.mapping.Document;

import java.time.LocalDateTime;
import java.util.List;

@Setter
@Getter
@Document(collection = "comment")
@CompoundIndexes({
    @CompoundIndex(name = "post_date_idx", def = "{'post': 1, 'createdDate': -1}"),
    @CompoundIndex(name = "blog_date_idx", def = "{'blog': 1, 'createdDate': -1}")
})
public class Comment {
    @Id
    private String id;
    private String comment;

    @DBRef(lazy = true)
    private Post post;

    @DBRef(lazy = true)
    private Blog blog;

    @DBRef(lazy = true)
    private User user;

    @DBRef(lazy = true)
    private List<User> mentions;

    @DBRef(lazy = true)
    private Media media;

    private LocalDateTime createdDate;
    private int numberOfReactions;
    private int numberOfReplyes;

}
