package com.hb.crm.core.beans.chat.OneChat;

import com.hb.crm.core.Enums.chat.ConversationTypes;
import com.hb.crm.core.beans.SubPackage;
import com.hb.crm.core.beans.User;
import lombok.*;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.DBRef;
import org.springframework.data.mongodb.core.mapping.Document;

import java.time.LocalDateTime;

@Data
@Document
@NoArgsConstructor
@AllArgsConstructor
public class Conversation {

    @Id
    String id;
    String topic;
    ConversationTypes type;
    boolean isMuted = false;
    boolean isClosed = false;

    @DBRef(lazy = true)
    User user;
    @DBRef(lazy = true)
    SubPackage Package;

    @CreatedDate
    LocalDateTime createdAt;
}
