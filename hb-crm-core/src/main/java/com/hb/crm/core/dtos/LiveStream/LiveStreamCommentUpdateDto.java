package com.hb.crm.core.dtos.LiveStream;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class LiveStreamCommentUpdateDto {
    private String id;
    private String comment;
    private String userId;
    private String username;
    private String firstName;
    private String lastName;
    private String profileImage;
    private String liveStreamId;
    private LocalDateTime createdDate;
    private String action;  // "ADD" or "REMOVE"
}