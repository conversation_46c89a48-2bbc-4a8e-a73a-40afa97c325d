# Audit Trail Configuration
# This file contains sample configuration properties for the audit trail system
# Copy these properties to your main application.properties or application.yml file

# ===================================================================
# AUDIT TRAIL CONFIGURATION
# ===================================================================

# Enable/disable audit trail system
audit.enabled=true

# Enable/disable async audit processing (recommended for performance)
audit.async=true

# Enable/disable audit context filter
audit.context.enabled=true

# Capture method arguments in audit logs (may impact performance)
audit.capture-arguments=true

# Capture return values in audit logs (not recommended for security)
audit.capture-return-value=false

# Maximum size of data to capture in audit logs (in characters)
audit.max-data-size=10000

# Default retention period for audit logs (in days)
audit.retention.days=365

# Enable/disable automatic archiving of old audit logs
audit.archive.enabled=true

# Retention period for archived audit logs (in days) - 7 years default
audit.archive.retention.days=2555

# Maximum number of records to export at once
audit.export.max-records=10000

# Environment name for audit logs
audit.environment=${spring.profiles.active:UNKNOWN}

# ===================================================================
# AUDIT MAINTENANCE CONFIGURATION
# ===================================================================

# Enable/disable audit maintenance tasks
audit.maintenance.enabled=true

# ===================================================================
# AUDIT FEATURE FLAGS
# ===================================================================

# Enable security-related audit logging
audit.enable-security-auditing=true

# Enable performance-related audit logging
audit.enable-performance-auditing=true

# Enable data change audit logging
audit.enable-data-change-auditing=true

# Enable compliance audit logging
audit.enable-compliance-auditing=true

# ===================================================================
# AUDIT PERFORMANCE CONFIGURATION
# ===================================================================

# Thread pool configuration for async audit processing
audit.thread-pool.core-size=2
audit.thread-pool.max-size=5
audit.thread-pool.queue-capacity=100

# ===================================================================
# AUDIT SECURITY CONFIGURATION
# ===================================================================

# Enable/disable sensitive data detection and redaction
audit.security.redact-sensitive-data=true

# Additional sensitive field patterns (comma-separated regex patterns)
audit.security.sensitive-patterns=.*ssn.*,.*credit.*,.*card.*

# ===================================================================
# AUDIT DATABASE CONFIGURATION
# ===================================================================

# MongoDB collection name for audit logs (default: audit_logs)
audit.mongodb.collection-name=audit_logs

# Enable/disable MongoDB auditing annotations
audit.mongodb.enable-auditing=true

# ===================================================================
# AUDIT LOGGING CONFIGURATION
# ===================================================================

# Log level for audit-related operations
logging.level.com.hb.crm.core.config.AOP.AuditAspect=INFO
logging.level.com.hb.crm.core.services.AuditServiceImpl=INFO
logging.level.com.hb.crm.core.config.AOP.AuditMaintenanceScheduler=INFO

# ===================================================================
# AUDIT COMPLIANCE CONFIGURATION
# ===================================================================

# Enable GDPR compliance features
audit.compliance.gdpr.enabled=true

# Enable SOX compliance features
audit.compliance.sox.enabled=true

# Enable HIPAA compliance features
audit.compliance.hipaa.enabled=false

# Data classification levels
audit.compliance.data-classification.levels=PUBLIC,INTERNAL,CONFIDENTIAL,RESTRICTED

# ===================================================================
# AUDIT ALERTING CONFIGURATION
# ===================================================================

# Enable audit alerting
audit.alerting.enabled=true

# Alert thresholds
audit.alerting.failure-rate-threshold=5.0
audit.alerting.security-event-threshold=10
audit.alerting.slow-operation-threshold=5000

# ===================================================================
# AUDIT EXPORT CONFIGURATION
# ===================================================================

# Supported export formats
audit.export.formats=CSV,JSON,PDF

# Include metadata in exports by default
audit.export.include-metadata=false

# Include sensitive data in exports (requires special permissions)
audit.export.include-sensitive-data=false

# ===================================================================
# AUDIT UI CONFIGURATION
# ===================================================================

# Default page size for audit log listings
audit.ui.default-page-size=20

# Maximum page size allowed
audit.ui.max-page-size=1000

# Default date range for audit log searches (in days)
audit.ui.default-date-range=30

# ===================================================================
# AUDIT INTEGRATION CONFIGURATION
# ===================================================================

# Enable integration with external SIEM systems
audit.integration.siem.enabled=false

# SIEM endpoint URL
audit.integration.siem.endpoint=

# SIEM API key
audit.integration.siem.api-key=

# Enable integration with external log aggregation systems
audit.integration.log-aggregation.enabled=false

# Log aggregation endpoint
audit.integration.log-aggregation.endpoint=

# ===================================================================
# AUDIT DEVELOPMENT CONFIGURATION
# ===================================================================

# Enable audit trail in development mode
audit.development.enabled=true

# Reduce retention period in development
audit.development.retention.days=30

# Enable debug logging in development
audit.development.debug-logging=true

# ===================================================================
# EXAMPLE ENVIRONMENT-SPECIFIC CONFIGURATIONS
# ===================================================================

# Production environment
#audit.environment=PRODUCTION
#audit.retention.days=2555
#audit.archive.enabled=true
#audit.async=true
#audit.capture-arguments=false
#audit.capture-return-value=false

# Development environment
#audit.environment=DEVELOPMENT
#audit.retention.days=30
#audit.archive.enabled=false
#audit.async=false
#audit.capture-arguments=true
#audit.capture-return-value=true

# Test environment
#audit.environment=TEST
#audit.retention.days=90
#audit.archive.enabled=true
#audit.async=true
#audit.capture-arguments=true
#audit.capture-return-value=false

# ===================================================================
# NOTES
# ===================================================================

# 1. Set audit.async=true in production for better performance
# 2. Set audit.capture-return-value=false in production for security
# 3. Adjust retention periods based on compliance requirements
# 4. Monitor audit log storage growth and adjust archiving settings
# 5. Use appropriate log levels to avoid performance impact
# 6. Configure proper database indexes for audit log queries
# 7. Consider using separate database for audit logs in high-volume systems
# 8. Regularly review and update sensitive field patterns
# 9. Test audit configuration in non-production environments first
# 10. Ensure proper backup and disaster recovery for audit logs
