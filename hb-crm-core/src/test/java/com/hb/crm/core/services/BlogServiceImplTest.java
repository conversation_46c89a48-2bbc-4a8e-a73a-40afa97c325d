package com.hb.crm.core.services;

import com.hb.crm.core.Enums.BlogStatus;
import com.hb.crm.core.Enums.ReactionType;
import com.hb.crm.core.beans.*;
import com.hb.crm.core.dtos.PageDto;
import com.hb.crm.core.repositories.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.slf4j.Logger;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * Comprehensive unit tests for BlogServiceImpl
 * 
 * Tests cover all CRUD operations, soft delete functionality, search operations,
 * analytics, reactions, and bulk operations with proper soft delete handling.
 */
@ExtendWith({MockitoExtension.class})
class BlogServiceImplTest {

    @Mock
    private BlogRepository blogRepository;
    
    @Mock
    private BlogCategoryRepository blogCategoryRepository;
    
    @Mock
    private BlogReactionRepository blogReactionRepository;
    
    @Mock
    private BlogViewRepository blogViewRepository;
    
    @Mock
    private UserRepository userRepository;

    @Mock
    private EmployeeRepository employeeRepository;

    @Mock
    private CommentRepository commentRepository;

    @Mock
    private MediaRepository mediaRepository;

    @Mock
    private com.hb.crm.admin.services.interfaces.QueryNormalizeService queryNormalizeService;

    @Mock
    private MongoTemplate mongoTemplate;

    @InjectMocks
    private BlogServiceImpl blogService;

    private Blog testBlog;
    private User testUser;
    private BlogCategory testCategory;
    private Tag testTag;

    @BeforeEach
    void setUp() {
        // Setup test user
        testUser = new User();
        testUser.setId("user123");
        testUser.setUsername("testuser");

        // Setup test category
        testCategory = new BlogCategory();
        testCategory.setId("category123");
        testCategory.setName("Technology");
        testCategory.setSlug("technology");

        // Setup test tag
        testTag = new Tag();
        testTag.setId("tag123");
        testTag.setText("Java");

        // Setup test blog
        testBlog = new Blog();
        testBlog.setId("blog123");
        testBlog.setTitle("Test Blog Title");
        testBlog.setSlug("test-blog-title");
        testBlog.setContent("This is test blog content");
        testBlog.setExcerpt("Test excerpt");
        testBlog.setAuthor(testUser);
        testBlog.setCategory(testCategory);
        testBlog.setTags(Arrays.asList(testTag));
        testBlog.setStatus(BlogStatus.draft);
        testBlog.setCreatedDate(LocalDateTime.now());
        testBlog.setUpdatedDate(LocalDateTime.now());
        testBlog.setDeleted(false);
        testBlog.setViewCount(0);
        testBlog.setLikeCount(0);
        testBlog.setCommentCount(0);
        testBlog.setShareCount(0);
    }

    // ==================== CRUD Operations Tests ====================

    @Test
    void testCreateBlog_Success() {
        // Arrange
        Blog newBlog = new Blog("New Blog", "Content", testUser);
        when(blogRepository.existsBySlugAndDeletedFalse(anyString())).thenReturn(false);
        when(blogRepository.save(any(Blog.class))).thenReturn(newBlog);

        // Act
        Blog result = blogService.createBlog(newBlog);

        // Assert
        assertNotNull(result);
        verify(blogRepository).save(any(Blog.class));
        verify(blogRepository).existsBySlugAndDeletedFalse(anyString());
    }

    @Test
    void testUpdateBlog_Success() {
        // Arrange
        Blog existingBlog = new Blog();
        existingBlog.setId("blog123");
        existingBlog.setTitle("Original Title");
        existingBlog.setSlug("original-title");
        existingBlog.setContent("Original content");
        existingBlog.setAuthor(testUser);

        Blog updatedBlog = new Blog();
        updatedBlog.setId("blog123");
        updatedBlog.setTitle("Updated Title");
        updatedBlog.setContent("Updated content");
        updatedBlog.setAuthor(testUser);

        when(blogRepository.findById("blog123")).thenReturn(Optional.of(existingBlog));
        when(blogRepository.save(any(Blog.class))).thenReturn(existingBlog);

        // Act
        Blog result = blogService.updateBlog(updatedBlog);

        // Assert
        assertNotNull(result);
        verify(blogRepository).findById("blog123");
        verify(blogRepository).save(any(Blog.class));
    }

    @Test
    void testUpdateBlog_NotFound() {
        // Arrange
        when(blogRepository.findById("blog123")).thenReturn(Optional.empty());

        // Act & Assert
        assertThrows(RuntimeException.class, () -> blogService.updateBlog(testBlog));
        verify(blogRepository).findById("blog123");
        verify(blogRepository, never()).save(any(Blog.class));
    }

    @Test
    void testGetBlogById_Success_NotDeleted() {
        // Arrange
        when(blogRepository.findById("blog123")).thenReturn(Optional.of(testBlog));

        // Act
        Optional<Blog> result = blogService.getBlogById("blog123");

        // Assert
        assertTrue(result.isPresent());
        assertEquals(testBlog, result.get());
        verify(blogRepository).findById("blog123");
    }

    @Test
    void testGetBlogById_FiltersSoftDeleted() {
        // Arrange
        testBlog.setDeleted(true);
        when(blogRepository.findById("blog123")).thenReturn(Optional.of(testBlog));

        // Act
        Optional<Blog> result = blogService.getBlogById("blog123");

        // Assert
        assertFalse(result.isPresent());
        verify(blogRepository).findById("blog123");
    }

    @Test
    void testGetBlogByIdIncludingDeleted_Success() {
        // Arrange
        testBlog.setDeleted(true);
        when(blogRepository.findById("blog123")).thenReturn(Optional.of(testBlog));

        // Act
        Optional<Blog> result = blogService.getBlogByIdIncludingDeleted("blog123");

        // Assert
        assertTrue(result.isPresent());
        assertEquals(testBlog, result.get());
        verify(blogRepository).findById("blog123");
    }

    @Test
    void testGetBlogBySlug_Success() {
        // Arrange
        when(blogRepository.findBySlugAndDeletedFalse("test-slug")).thenReturn(Optional.of(testBlog));

        // Act
        Optional<Blog> result = blogService.getBlogBySlug("test-slug");

        // Assert
        assertTrue(result.isPresent());
        assertEquals(testBlog, result.get());
        verify(blogRepository).findBySlugAndDeletedFalse("test-slug");
    }

    @Test
    void testDeleteBlog_Success() {
        // Act
        blogService.deleteBlog("blog123");

        // Assert
        verify(blogRepository).deleteById("blog123");
    }

    @Test
    void testSoftDeleteBlog_Success() {
        // Act
        blogService.softDeleteBlog("blog123");

        // Assert
        verify(mongoTemplate).updateFirst(any(Query.class), any(Update.class), eq(Blog.class));
    }

    @Test
    void testRestoreBlog_Success() {
        // Arrange
        testBlog.setDeleted(true);
        testBlog.setDeletedDate(LocalDateTime.now());
        when(blogRepository.findById("blog123")).thenReturn(Optional.of(testBlog));
        when(blogRepository.save(any(Blog.class))).thenReturn(testBlog);

        // Act
        Blog result = blogService.restoreBlog("blog123");

        // Assert
        assertNotNull(result);
        assertFalse(result.isDeleted());
        assertNull(result.getDeletedDate());
        assertEquals(BlogStatus.draft, result.getStatus());
        verify(blogRepository).findById("blog123");
        verify(blogRepository).save(any(Blog.class));
    }

    @Test
    void testRestoreBlog_NotSoftDeleted() {
        // Arrange
        testBlog.setDeleted(false);
        when(blogRepository.findById("blog123")).thenReturn(Optional.of(testBlog));

        // Act & Assert
        assertThrows(RuntimeException.class, () -> blogService.restoreBlog("blog123"));
        verify(blogRepository).findById("blog123");
        verify(blogRepository, never()).save(any(Blog.class));
    }

    // ==================== Status Management Tests ====================

    @Test
    void testPublishBlog_Success() {
        // Arrange
        when(blogRepository.findById("blog123")).thenReturn(Optional.of(testBlog));
        when(blogRepository.save(any(Blog.class))).thenReturn(testBlog);

        // Act
        Blog result = blogService.publishBlog("blog123");

        // Assert
        assertNotNull(result);
        assertEquals(BlogStatus.published, result.getStatus());
        assertNotNull(result.getPublishedDate());
        verify(blogRepository).findById("blog123");
        verify(blogRepository).save(any(Blog.class));
    }

    @Test
    void testUnpublishBlog_Success() {
        // Arrange
        testBlog.setStatus(BlogStatus.published);
        when(blogRepository.findById("blog123")).thenReturn(Optional.of(testBlog));
        when(blogRepository.save(any(Blog.class))).thenReturn(testBlog);

        // Act
        Blog result = blogService.unpublishBlog("blog123");

        // Assert
        assertNotNull(result);
        assertEquals(BlogStatus.draft, result.getStatus());
        verify(blogRepository).findById("blog123");
        verify(blogRepository).save(any(Blog.class));
    }

    @Test
    void testArchiveBlog_Success() {
        // Arrange
        when(blogRepository.findById("blog123")).thenReturn(Optional.of(testBlog));
        when(blogRepository.save(any(Blog.class))).thenReturn(testBlog);

        // Act
        Blog result = blogService.archiveBlog("blog123");

        // Assert
        assertNotNull(result);
        assertEquals(BlogStatus.archived, result.getStatus());
        verify(blogRepository).findById("blog123");
        verify(blogRepository).save(any(Blog.class));
    }

    @Test
    void testChangeBlogStatus_PreventsSoftDeletedModification() {
        // Arrange
        testBlog.setDeleted(true);
        when(blogRepository.findById("blog123")).thenReturn(Optional.of(testBlog));

        // Act & Assert
        assertThrows(RuntimeException.class, () -> 
            blogService.changeBlogStatus("blog123", BlogStatus.published));
        verify(blogRepository).findById("blog123");
        verify(blogRepository, never()).save(any(Blog.class));
    }

    // ==================== Search and Filtering Tests ====================

    @Test
    void testSearchBlogs_Success() {
        // Arrange
        List<Blog> blogs = Arrays.asList(testBlog);
        Page<Blog> blogPage = new PageImpl<>(blogs);
        when(blogRepository.searchByTitleContentOrExcerpt(eq(BlogStatus.published), eq("test"), any(Pageable.class)))
            .thenReturn(blogPage);

        // Act
        PageDto<Blog> result = blogService.searchBlogs("test", 0, 10);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.getItems().size());
        assertEquals(1, result.getTotalNoOfItems());
        verify(blogRepository).searchByTitleContentOrExcerpt(eq(BlogStatus.published), eq("test"), any(Pageable.class));
    }

    @Test
    void testGetBlogsByStatus_Success() {
        // Arrange
        List<Blog> blogs = Arrays.asList(testBlog);
        Page<Blog> blogPage = new PageImpl<>(blogs);
        when(blogRepository.findByStatusAndDeletedFalseOrderByPublishedDateDesc(eq(BlogStatus.published), any(Pageable.class)))
            .thenReturn(blogPage);

        // Act
        PageDto<Blog> result = blogService.getBlogsByStatus(BlogStatus.published, 0, 10);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.getItems().size());
        verify(blogRepository).findByStatusAndDeletedFalseOrderByPublishedDateDesc(eq(BlogStatus.published), any(Pageable.class));
    }

    @Test
    void testGetBlogsByAuthor_Success() {
        // Arrange
        List<Blog> blogs = Arrays.asList(testBlog);
        Page<Blog> blogPage = new PageImpl<>(blogs);
        when(userRepository.findById("user123")).thenReturn(Optional.of(testUser));
        when(blogRepository.findByAuthorAndStatusAndDeletedFalseOrderByUpdatedDateDesc(eq(testUser), eq(BlogStatus.published), any(Pageable.class)))
            .thenReturn(blogPage);

        // Act
        PageDto<Blog> result = blogService.getBlogsByAuthor("user123", BlogStatus.published, 0, 10);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.getItems().size());
        verify(userRepository).findById("user123");
        verify(blogRepository).findByAuthorAndStatusAndDeletedFalseOrderByUpdatedDateDesc(eq(testUser), eq(BlogStatus.published), any(Pageable.class));
    }

    @Test
    void testGetAllBlogsWithFilters_WithSearchTerm_Success() {
        // Arrange
        List<Blog> blogs = Arrays.asList(testBlog);
        when(mongoTemplate.find(any(Query.class), eq(Blog.class))).thenReturn(blogs);
        when(mongoTemplate.count(any(Query.class), eq(Blog.class))).thenReturn(1L);

        // Act
        PageDto<Blog> result = blogService.getAllBlogsWithFilters("test", null, false, 0, 10, "updatedDate", "DESC");

        // Assert
        assertNotNull(result);
        assertEquals(1, result.getItems().size());
        assertEquals(1, result.getTotalNoOfItems());
        assertEquals(0, result.getPageNumber());
        assertEquals(10, result.getItemsPerPage());
        verify(mongoTemplate).find(any(Query.class), eq(Blog.class));
        verify(mongoTemplate).count(any(Query.class), eq(Blog.class));
    }

    @Test
    void testGetAllBlogsWithFilters_WithStatusFilter_Success() {
        // Arrange
        List<Blog> blogs = Arrays.asList(testBlog);
        when(mongoTemplate.find(any(Query.class), eq(Blog.class))).thenReturn(blogs);
        when(mongoTemplate.count(any(Query.class), eq(Blog.class))).thenReturn(1L);

        // Act
        PageDto<Blog> result = blogService.getAllBlogsWithFilters(null, BlogStatus.published, false, 0, 10, "title", "ASC");

        // Assert
        assertNotNull(result);
        assertEquals(1, result.getItems().size());
        assertEquals(1, result.getTotalNoOfItems());
        verify(mongoTemplate).find(any(Query.class), eq(Blog.class));
        verify(mongoTemplate).count(any(Query.class), eq(Blog.class));
    }

    @Test
    void testGetAllBlogsWithFilters_IncludeDeleted_Success() {
        // Arrange
        List<Blog> blogs = Arrays.asList(testBlog);
        when(mongoTemplate.find(any(Query.class), eq(Blog.class))).thenReturn(blogs);
        when(mongoTemplate.count(any(Query.class), eq(Blog.class))).thenReturn(1L);

        // Act
        PageDto<Blog> result = blogService.getAllBlogsWithFilters(null, null, true, 0, 10, "createdDate", "DESC");

        // Assert
        assertNotNull(result);
        assertEquals(1, result.getItems().size());
        verify(mongoTemplate).find(any(Query.class), eq(Blog.class));
        verify(mongoTemplate).count(any(Query.class), eq(Blog.class));
    }

    @Test
    void testGetAllBlogsWithFilters_CombinedFilters_Success() {
        // Arrange
        List<Blog> blogs = Arrays.asList(testBlog);
        when(mongoTemplate.find(any(Query.class), eq(Blog.class))).thenReturn(blogs);
        when(mongoTemplate.count(any(Query.class), eq(Blog.class))).thenReturn(1L);

        // Act
        PageDto<Blog> result = blogService.getAllBlogsWithFilters("travel", BlogStatus.draft, false, 1, 5, "publishedDate", "ASC");

        // Assert
        assertNotNull(result);
        assertEquals(1, result.getItems().size());
        assertEquals(1, result.getPageNumber());
        assertEquals(5, result.getItemsPerPage());
        verify(mongoTemplate).find(any(Query.class), eq(Blog.class));
        verify(mongoTemplate).count(any(Query.class), eq(Blog.class));
    }

    @Test
    void testGetAllBlogsWithFilters_EmptyResults_Success() {
        // Arrange
        when(mongoTemplate.find(any(Query.class), eq(Blog.class))).thenReturn(Collections.emptyList());
        when(mongoTemplate.count(any(Query.class), eq(Blog.class))).thenReturn(0L);

        // Act
        PageDto<Blog> result = blogService.getAllBlogsWithFilters("nonexistent", null, false, 0, 10, "updatedDate", "DESC");

        // Assert
        assertNotNull(result);
        assertEquals(0, result.getItems().size());
        assertEquals(0, result.getTotalNoOfItems());
        verify(mongoTemplate).find(any(Query.class), eq(Blog.class));
        verify(mongoTemplate).count(any(Query.class), eq(Blog.class));
    }

    @Test
    void testGetBlogsByAuthor_AuthorNotFound() {
        // Arrange
        when(userRepository.findById("user123")).thenReturn(Optional.empty());

        // Act
        PageDto<Blog> result = blogService.getBlogsByAuthor("user123", BlogStatus.published, 0, 10);

        // Assert
        assertNotNull(result);
        assertEquals(0, result.getItems().size());
        verify(userRepository).findById("user123");
        verify(blogRepository, never()).findByAuthorAndStatusAndDeletedFalseOrderByUpdatedDateDesc(any(), any(), any());
    }

    @Test
    void testGetBlogsByCategory_Success() {
        // Arrange
        List<Blog> blogs = Arrays.asList(testBlog);
        Page<Blog> blogPage = new PageImpl<>(blogs);
        when(blogCategoryRepository.findById("category123")).thenReturn(Optional.of(testCategory));
        when(blogRepository.findByCategoryAndStatusAndDeletedFalseOrderByPublishedDateDesc(eq(testCategory), eq(BlogStatus.published), any(Pageable.class)))
            .thenReturn(blogPage);

        // Act
        PageDto<Blog> result = blogService.getBlogsByCategory("category123", BlogStatus.published, 0, 10);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.getItems().size());
        verify(blogCategoryRepository).findById("category123");
        verify(blogRepository).findByCategoryAndStatusAndDeletedFalseOrderByPublishedDateDesc(eq(testCategory), eq(BlogStatus.published), any(Pageable.class));
    }

    @Test
    void testGetFeaturedBlogs_Success() {
        // Arrange
        List<Blog> blogs = Arrays.asList(testBlog);
        Page<Blog> blogPage = new PageImpl<>(blogs);
        when(blogRepository.findByFeaturedTrueAndStatusAndDeletedFalseOrderByPublishedDateDesc(eq(BlogStatus.published), any(Pageable.class)))
            .thenReturn(blogPage);

        // Act
        PageDto<Blog> result = blogService.getFeaturedBlogs(0, 10);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.getItems().size());
        verify(blogRepository).findByFeaturedTrueAndStatusAndDeletedFalseOrderByPublishedDateDesc(eq(BlogStatus.published), any(Pageable.class));
    }

    @Test
    void testGetBlogsByDateRange_Success() {
        // Arrange
        LocalDateTime startDate = LocalDateTime.now().minusDays(7);
        LocalDateTime endDate = LocalDateTime.now();
        List<Blog> blogs = Arrays.asList(testBlog);
        Page<Blog> blogPage = new PageImpl<>(blogs);
        when(blogRepository.findByStatusAndDeletedFalseAndPublishedDateBetweenOrderByPublishedDateDesc(
            eq(BlogStatus.published), eq(startDate), eq(endDate), any(Pageable.class)))
            .thenReturn(blogPage);

        // Act
        PageDto<Blog> result = blogService.getBlogsByDateRange(startDate, endDate, 0, 10);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.getItems().size());
        verify(blogRepository).findByStatusAndDeletedFalseAndPublishedDateBetweenOrderByPublishedDateDesc(
            eq(BlogStatus.published), eq(startDate), eq(endDate), any(Pageable.class));
    }

    @Test
    void testGetBlogsByMultipleCategories_Success() {
        // Arrange
        List<String> categoryIds = Arrays.asList("category123");
        List<BlogCategory> categories = Arrays.asList(testCategory);
        List<Blog> blogs = Arrays.asList(testBlog);
        Page<Blog> blogPage = new PageImpl<>(blogs);
        when(blogCategoryRepository.findAllById(categoryIds)).thenReturn(categories);
        when(blogRepository.findByCategoryInAndStatusAndDeletedFalseOrderByPublishedDateDesc(
            eq(categories), eq(BlogStatus.published), any(Pageable.class)))
            .thenReturn(blogPage);

        // Act
        PageDto<Blog> result = blogService.getBlogsByMultipleCategories(categoryIds, 0, 10);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.getItems().size());
        verify(blogCategoryRepository).findAllById(categoryIds);
        verify(blogRepository).findByCategoryInAndStatusAndDeletedFalseOrderByPublishedDateDesc(
            eq(categories), eq(BlogStatus.published), any(Pageable.class));
    }

    // ==================== Slug Management Tests ====================

    @Test
    void testGenerateSlug_Success() {
        // Act
        String result = blogService.generateSlug("Test Blog Title With Special Characters!");

        // Assert
        assertEquals("test-blog-title-with-special-characters", result);
    }

    @Test
    void testGenerateUniqueSlug_NoConflict() {
        // Arrange
        when(blogRepository.existsBySlugAndDeletedFalse("test-title")).thenReturn(false);

        // Act
        String result = blogService.generateUniqueSlug("Test Title");

        // Assert
        assertEquals("test-title", result);
        verify(blogRepository).existsBySlugAndDeletedFalse("test-title");
    }

    @Test
    void testGenerateUniqueSlug_WithConflict() {
        // Arrange
        when(blogRepository.existsBySlugAndDeletedFalse("test-title")).thenReturn(true);
        when(blogRepository.existsBySlugAndDeletedFalse("test-title-1")).thenReturn(false);

        // Act
        String result = blogService.generateUniqueSlug("Test Title");

        // Assert
        assertEquals("test-title-1", result);
        verify(blogRepository).existsBySlugAndDeletedFalse("test-title");
        verify(blogRepository).existsBySlugAndDeletedFalse("test-title-1");
    }

    @Test
    void testIsSlugUnique_True() {
        // Arrange
        when(blogRepository.existsBySlugAndDeletedFalse("unique-slug")).thenReturn(false);

        // Act
        boolean result = blogService.isSlugUnique("unique-slug");

        // Assert
        assertTrue(result);
        verify(blogRepository).existsBySlugAndDeletedFalse("unique-slug");
    }

    @Test
    void testIsSlugUnique_False() {
        // Arrange
        when(blogRepository.existsBySlugAndDeletedFalse("existing-slug")).thenReturn(true);

        // Act
        boolean result = blogService.isSlugUnique("existing-slug");

        // Assert
        assertFalse(result);
        verify(blogRepository).existsBySlugAndDeletedFalse("existing-slug");
    }

    // ==================== View Tracking Tests ====================

    @Test
    void testRecordView_Success() {
        // Arrange
        when(blogRepository.findById("blog123")).thenReturn(Optional.of(testBlog));
        when(userRepository.findById("user123")).thenReturn(Optional.of(testUser));

        // Act
        blogService.recordView("blog123", "user123", "127.0.0.1", "Mozilla/5.0", "google.com");

        // Assert
        verify(blogViewRepository).save(any(BlogView.class));
        verify(mongoTemplate).updateFirst(any(Query.class), any(Update.class), eq(Blog.class));
    }

    @Test
    void testRecordAnonymousView_Success() {
        // Arrange
        when(blogRepository.findById("blog123")).thenReturn(Optional.of(testBlog));

        // Act
        blogService.recordAnonymousView("blog123", "127.0.0.1", "Mozilla/5.0", "google.com");

        // Assert
        verify(blogViewRepository).save(any(BlogView.class));
        verify(mongoTemplate).updateFirst(any(Query.class), any(Update.class), eq(Blog.class));
    }

    @Test
    void testGetViewCount_Success() {
        // Arrange
        when(blogRepository.findById("blog123")).thenReturn(Optional.of(testBlog));

        // Act
        long result = blogService.getViewCount("blog123");

        // Assert
        assertEquals(0, result);
        verify(blogRepository).findById("blog123");
    }

    // ==================== Reaction Tests ====================

    @Test
    void testAddReaction_Success() {
        // Arrange
        when(blogRepository.findById("blog123")).thenReturn(Optional.of(testBlog));
        when(userRepository.findById("user123")).thenReturn(Optional.of(testUser));
        when(blogReactionRepository.findByBlogAndUser(testBlog, testUser)).thenReturn(Optional.empty());

        // Act
        blogService.addReaction("blog123", "user123", ReactionType.like);

        // Assert
        verify(blogReactionRepository).save(any(BlogReaction.class));
        verify(mongoTemplate).updateFirst(any(Query.class), any(Update.class), eq(Blog.class));
    }

    @Test
    void testRemoveReaction_Success() {
        // Arrange
        BlogReaction existingReaction = new BlogReaction();
        existingReaction.setBlog(testBlog);
        existingReaction.setUser(testUser);
        existingReaction.setReactionType(ReactionType.like);

        when(blogRepository.findById("blog123")).thenReturn(Optional.of(testBlog));
        when(userRepository.findById("user123")).thenReturn(Optional.of(testUser));
        when(blogReactionRepository.findByBlogAndUser(testBlog, testUser)).thenReturn(Optional.of(existingReaction));

        // Act
        blogService.removeReaction("blog123", "user123");

        // Assert
        verify(blogReactionRepository).deleteByBlogAndUser(testBlog, testUser);
        verify(mongoTemplate).updateFirst(any(Query.class), any(Update.class), eq(Blog.class));
    }

    @Test
    void testUpdateReaction_Success() {
        // Arrange
        BlogReaction existingReaction = new BlogReaction();
        existingReaction.setBlog(testBlog);
        existingReaction.setUser(testUser);
        existingReaction.setReactionType(ReactionType.like);

        when(blogRepository.findById("blog123")).thenReturn(Optional.of(testBlog));
        when(userRepository.findById("user123")).thenReturn(Optional.of(testUser));
        when(blogReactionRepository.findByBlogAndUser(testBlog, testUser)).thenReturn(Optional.of(existingReaction));
        when(blogReactionRepository.save(any(BlogReaction.class))).thenReturn(existingReaction);

        // Act
        blogService.updateReaction("blog123", "user123", ReactionType.like);

        // Assert
        verify(blogReactionRepository).save(any(BlogReaction.class));
    }

    @Test
    void testGetReactionCounts_Success() {
        // Arrange
        BlogReactionRepository.ReactionCountDto likeCount = mock(BlogReactionRepository.ReactionCountDto.class);
        when(likeCount.get_id()).thenReturn(ReactionType.like);
        when(likeCount.getCount()).thenReturn(5L);

        List<BlogReactionRepository.ReactionCountDto> reactionCounts = Arrays.asList(likeCount);
        when(blogReactionRepository.getReactionCountsByBlog("blog123")).thenReturn(reactionCounts);

        // Act
        Map<ReactionType, Long> result = blogService.getReactionCounts("blog123");

        // Assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(5L, result.get(ReactionType.like));
        verify(blogReactionRepository).getReactionCountsByBlog("blog123");
    }

    @Test
    void testHasUserReacted_True() {
        // Arrange
        when(blogRepository.findById("blog123")).thenReturn(Optional.of(testBlog));
        when(userRepository.findById("user123")).thenReturn(Optional.of(testUser));
        when(blogReactionRepository.existsByBlogAndUser(testBlog, testUser)).thenReturn(true);

        // Act
        boolean result = blogService.hasUserReacted("blog123", "user123");

        // Assert
        assertTrue(result);
        verify(blogReactionRepository).existsByBlogAndUser(testBlog, testUser);
    }

    @Test
    void testGetUserReaction_Success() {
        // Arrange
        BlogReaction reaction = new BlogReaction();
        reaction.setReactionType(ReactionType.like);

        when(blogRepository.findById("blog123")).thenReturn(Optional.of(testBlog));
        when(userRepository.findById("user123")).thenReturn(Optional.of(testUser));
        when(blogReactionRepository.findByBlogAndUser(testBlog, testUser)).thenReturn(Optional.of(reaction));

        // Act
        ReactionType result = blogService.getUserReaction("blog123", "user123");

        // Assert
        assertEquals(ReactionType.like, result);
        verify(blogReactionRepository).findByBlogAndUser(testBlog, testUser);
    }

    // ==================== Analytics Tests ====================

    @Test
    void testGetBlogAnalytics_Success() {
        // Arrange
        LocalDateTime startDate = LocalDateTime.now().minusDays(7);
        LocalDateTime endDate = LocalDateTime.now();
        List<BlogViewRepository.DailyViewStats> dailyStats = new ArrayList<>();

        when(blogRepository.findById("blog123")).thenReturn(Optional.of(testBlog));
        when(blogViewRepository.getDailyViewStats("blog123", startDate, endDate)).thenReturn(dailyStats);

        // Act
        Map<String, Object> result = blogService.getBlogAnalytics("blog123", startDate, endDate);

        // Assert
        assertNotNull(result);
        assertEquals(0, result.get("viewCount"));
        assertEquals(0, result.get("likeCount"));
        assertEquals(0, result.get("commentCount"));
        assertEquals(0, result.get("shareCount"));
        assertEquals(false, result.get("deleted"));
        assertEquals(BlogStatus.draft, result.get("status"));
        verify(blogRepository).findById("blog123");
        verify(blogViewRepository).getDailyViewStats("blog123", startDate, endDate);
    }

    @Test
    void testGetMostViewedBlogs_Success() {
        // Arrange
        List<Blog> blogs = Arrays.asList(testBlog);
        when(blogRepository.findMostViewedBlogs(10)).thenReturn(blogs);

        // Act
        List<Blog> result = blogService.getMostViewedBlogs(10);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.size());
        verify(blogRepository).findMostViewedBlogs(10);
    }

    @Test
    void testGetMostLikedBlogs_Success() {
        // Arrange
        List<Blog> blogs = Arrays.asList(testBlog);
        when(blogRepository.findMostLikedBlogs(10)).thenReturn(blogs);

        // Act
        List<Blog> result = blogService.getMostLikedBlogs(10);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.size());
        verify(blogRepository).findMostLikedBlogs(10);
    }

    @Test
    void testGetRelatedBlogs_Success() {
        // Arrange
        List<Blog> relatedBlogs = Arrays.asList(testBlog);
        when(blogRepository.findById("blog123")).thenReturn(Optional.of(testBlog));
        when(blogRepository.findRelatedBlogs(eq("blog123"), anyString(), anyList(), eq(5)))
            .thenReturn(relatedBlogs);

        // Act
        List<Blog> result = blogService.getRelatedBlogs("blog123", 5);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.size());
        verify(blogRepository).findById("blog123");
        verify(blogRepository).findRelatedBlogs(eq("blog123"), anyString(), anyList(), eq(5));
    }

    // ==================== Bulk Operations Tests ====================

    @Test
    void testBulkUpdateStatus_Success() {
        // Arrange
        List<String> blogIds = Arrays.asList("blog1", "blog2", "blog3");

        // Act
        blogService.bulkUpdateStatus(blogIds, BlogStatus.published);

        // Assert
        verify(mongoTemplate).updateMulti(any(Query.class), any(Update.class), eq(Blog.class));
    }

    @Test
    void testBulkDelete_Success() {
        // Arrange
        List<String> blogIds = Arrays.asList("blog1", "blog2", "blog3");

        // Act
        blogService.bulkDelete(blogIds);

        // Assert
        verify(blogRepository).deleteAllById(blogIds);
    }

    @Test
    void testBulkArchive_Success() {
        // Arrange
        List<String> blogIds = Arrays.asList("blog1", "blog2", "blog3");

        // Act
        blogService.bulkArchive(blogIds);

        // Assert
        verify(mongoTemplate).updateMulti(any(Query.class), any(Update.class), eq(Blog.class));
    }

    @Test
    void testBulkSoftDelete_Success() {
        // Arrange
        List<String> blogIds = Arrays.asList("blog1", "blog2", "blog3");

        // Act
        blogService.bulkSoftDelete(blogIds);

        // Assert
        verify(mongoTemplate).updateMulti(any(Query.class), any(Update.class), eq(Blog.class));
    }

    // ==================== Comment Management Tests ====================

    @Test
    void testGetCommentCount_Success() {
        // Arrange
        when(blogRepository.findById("blog123")).thenReturn(Optional.of(testBlog));

        // Act
        long result = blogService.getCommentCount("blog123");

        // Assert
        assertEquals(0, result);
        verify(blogRepository).findById("blog123");
    }

    @Test
    void testUpdateCommentCount_Success() {
        // Arrange
        when(mongoTemplate.count(any(Query.class), eq(Comment.class))).thenReturn(5L);

        // Act
        blogService.updateCommentCount("blog123");

        // Assert
        verify(mongoTemplate).count(any(Query.class), eq(Comment.class));
        verify(mongoTemplate).updateFirst(any(Query.class), any(Update.class), eq(Blog.class));
    }

    // ==================== Content Management Tests ====================

    @Test
    void testDuplicateBlog_Success() {
        // Arrange
        when(blogRepository.findById("blog123")).thenReturn(Optional.of(testBlog));
        when(blogRepository.existsBySlugAndDeletedFalse(anyString())).thenReturn(false);
        when(blogRepository.save(any(Blog.class))).thenReturn(testBlog);

        // Act
        Blog result = blogService.duplicateBlog("blog123", "Duplicated Blog Title");

        // Assert
        assertNotNull(result);
        verify(blogRepository).findById("blog123");
        verify(blogRepository).save(any(Blog.class));
    }

    @Test
    void testCanUserEditBlog_Success() {
        // Arrange
        when(blogRepository.findById("blog123")).thenReturn(Optional.of(testBlog));

        // Act
        boolean result = blogService.canUserEditBlog("blog123", "user123");

        // Assert
        assertTrue(result);
        verify(blogRepository).findById("blog123");
    }

    @Test
    void testCanUserEditBlog_DifferentUser() {
        // Arrange
        when(blogRepository.findById("blog123")).thenReturn(Optional.of(testBlog));

        // Act
        boolean result = blogService.canUserEditBlog("blog123", "differentUser");

        // Assert
        assertFalse(result);
        verify(blogRepository).findById("blog123");
    }

    @Test
    void testCanUserDeleteBlog_Success() {
        // Arrange
        when(blogRepository.findById("blog123")).thenReturn(Optional.of(testBlog));

        // Act
        boolean result = blogService.canUserDeleteBlog("blog123", "user123");

        // Assert
        assertTrue(result);
        verify(blogRepository).findById("blog123");
    }

    @Test
    void testCanUserPublishBlog_Success() {
        // Arrange
        when(blogRepository.findById("blog123")).thenReturn(Optional.of(testBlog));

        // Act
        boolean result = blogService.canUserPublishBlog("blog123", "user123");

        // Assert
        assertTrue(result);
        verify(blogRepository).findById("blog123");
    }
}
